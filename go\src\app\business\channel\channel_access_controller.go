package channel

import (
	"fincore/utils/jsonschema"
	"fincore/utils/results"
	"net/http"

	"github.com/gin-gonic/gin"
)

// 渠道准入校验 CheckUser
func (c *ChannelController) CheckUser(ctx *gin.Context) {
	var req HXChannleCommReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.Failed(ctx, "参数绑定失败", err.Error())
		return
	}

	validator := jsonschema.NewValidator(GetCheckUserSchema())
	validationResult := validator.Validate(map[string]interface{}{
		"channelId": req.ChannelId,
		"timestamp": req.Timestamp,
		"requestId": req.RequestId,
		"data":      req.Data,
	})

	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	service := NewChannelService()
	result, err := service.CheckUser(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusOK, gin.H{
			"code": result,
			"msg":  "操作成功",
			"data": nil,
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": result,
		"msg":  err,
		"data": nil,
	})
}

// 渠道进件推送
// func Apply(ctx *gin.Context) {
// 	var req ApplyRequest
// 	if err := ctx.ShouldBindJSON(&req); err != nil {
// 		results.Failed(ctx, "参数绑定失败", err.Error())
// 		return
// 	}

// 	validator := jsonschema.NewValidator(GetApplySchema())
// 	validationResult := validator.Validate(map[string]interface{}{
// 		"channelId": req.ChannelId,
// 		"timestamp": req.Timestamp,
// 		"requestId": req.RequestId,
// 		"data":      req.Data,
// 	})
// }
