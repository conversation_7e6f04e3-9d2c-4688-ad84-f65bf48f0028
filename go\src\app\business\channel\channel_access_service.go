package channel

import (
	"encoding/json"
	"fincore/model"
	"fincore/utils/log"
	"fmt"

	"github.com/gin-gonic/gin"
)

const (
	// 不可准入
	NOT_ACCESS = 1
	// 可准入
	ACCESS = 0
)

// CheckUser 渠道准入校验
func (s *ChannelService) CheckUser(ctx *gin.Context, req HXChannleCommReq) (resp int, err error) {
	// 检查用户是否存在
	var userData CheckUserRequestData
	err = json.Unmarshal([]byte(req.Data), &userData)
	if err != nil {
		return NOT_ACCESS, err
	}

	exists, err := model.NewBusinessAppAccountService().CheckAccountExistsByIDCardMd5AndMobileMd5(userData.IdentityNumberMd5, userData.MobileMd5)
	if err != nil {
		return NOT_ACCESS, err
	}
	if exists {
		log.Channel().WithContext(ctx).Warn("用户已存在")
		return NOT_ACCESS, fmt.Errorf("用户已存在")
	}
	return
}
