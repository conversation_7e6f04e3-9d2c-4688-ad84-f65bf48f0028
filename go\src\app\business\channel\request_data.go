package channel

type HXChannleCommReq struct {
	ChannelId int    `json:"channelId"` //合作方分配的唯一标识
	Timestamp int    `json:"timestamp"` //请求时间戳
	RequestId string `json:"requestId"` //请求ID
	Data      string `json:"data"`      //请求数据
}

type CheckUserRequestData struct {
	MobileMd5         string `json:"mobileMd5"`         //手机号MD5
	IdentityNumberMd5 string `json:"identityNumberMd5"` //身份证号MD5
}

type ApplyRequestData struct {
	ApplyInfo      string `json:"applyInfo"`      // 进件信息
	ApplyContracts string `json:"applyContracts"` // 进件联系人信息
}
