package channel

import (
	"crypto/des"
	"encoding/base64"
	"errors"
	"fmt"
)

// DESUtil DES加密工具类
// 实现与Java版本兼容的DES/ECB/PKCS7Padding加密解密
type DESUtil struct{}

// PKCS7Padding PKCS7填充
func (d *DESUtil) pkcs7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := make([]byte, padding)
	for i := range padText {
		padText[i] = byte(padding)
	}
	return append(data, padText...)
}

// PKCS7UnPadding PKCS7去填充
func (d *DESUtil) pkcs7UnPadding(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, errors.New("数据为空")
	}

	padding := int(data[len(data)-1])
	if padding > len(data) || padding == 0 {
		return nil, errors.New("无效的填充")
	}

	// 验证填充的正确性
	for i := len(data) - padding; i < len(data); i++ {
		if data[i] != byte(padding) {
			return nil, errors.New("无效的填充")
		}
	}

	return data[:len(data)-padding], nil
}

// generateKey 生成DES密钥
func (d *DESUtil) generateKey(password string) ([]byte, error) {
	if len(password) < 8 {
		return nil, errors.New("加密失败，key不能小于8位")
	}

	// 取前8位作为DES密钥
	key := []byte(password)[:8]
	return key, nil
}

// Encrypt DES加密
// 使用DES/ECB/PKCS7Padding模式，与Java版本兼容
func (d *DESUtil) Encrypt(password, data string) (string, error) {
	if password == "" || len(password) < 8 {
		return "", errors.New("加密失败，key不能小于8位")
	}

	if data == "" {
		return "", nil
	}

	// 生成密钥
	key, err := d.generateKey(password)
	if err != nil {
		return "", err
	}

	// 创建DES加密块
	block, err := des.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建DES加密块失败: %v", err)
	}

	// PKCS7填充
	plaintext := []byte(data)
	plaintext = d.pkcs7Padding(plaintext, block.BlockSize())

	// ECB模式加密 (手动实现，因为Go标准库没有直接支持ECB)
	ciphertext := make([]byte, len(plaintext))
	blockSize := block.BlockSize()

	for i := 0; i < len(plaintext); i += blockSize {
		block.Encrypt(ciphertext[i:i+blockSize], plaintext[i:i+blockSize])
	}

	// Base64编码
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt DES解密
// 使用DES/ECB/PKCS7Padding模式
func (d *DESUtil) Decrypt(password, data string) (string, error) {
	if password == "" || len(password) < 8 {
		return "", errors.New("解密失败，key不能小于8位")
	}

	if data == "" {
		return "", nil
	}

	// Base64解码
	ciphertext, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return "", fmt.Errorf("Base64解码失败: %v", err)
	}

	// 生成密钥
	key, err := d.generateKey(password)
	if err != nil {
		return "", err
	}

	// 创建DES解密块
	block, err := des.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建DES解密块失败: %v", err)
	}

	// 检查密文长度
	if len(ciphertext)%block.BlockSize() != 0 {
		return "", errors.New("密文长度不正确")
	}

	// ECB模式解密
	plaintext := make([]byte, len(ciphertext))
	blockSize := block.BlockSize()

	for i := 0; i < len(ciphertext); i += blockSize {
		block.Decrypt(plaintext[i:i+blockSize], ciphertext[i:i+blockSize])
	}

	// 去除PKCS7填充
	plaintext, err = d.pkcs7UnPadding(plaintext)
	if err != nil {
		return "", fmt.Errorf("去除填充失败: %v", err)
	}

	return string(plaintext), nil
}

// 全局DES工具实例
var DES = &DESUtil{}

// Encrypt 全局DES加密函数，与Java版本兼容
func Encrypt(password, data string) (string, error) {
	return DES.Encrypt(password, data)
}

// Decrypt 全局DES解密函数，与Java版本兼容
func Decrypt(password, data string) (string, error) {
	return DES.Decrypt(password, data)
}
