package customer

import (
	"fincore/app/business/order"
	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/log"
	"fincore/utils/results"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/xuri/excelize/v2"
)

type CustomerController struct{}

func init() {
	controller := CustomerController{}
	gf.Register(&controller, reflect.TypeOf(controller).PkgPath())
}

// ListCustomers 获取业务应用账户列表
func (c *CustomerController) ListCustomers(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetCustomerListSchema()
	validator := jsonschema.NewValidator(schema)

	// 从POST请求体获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerList(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取业务应用账户列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取业务应用账户列表成功", result, nil)
}

// GetCustomerDetail 获取业务应用账户详情
func (c *CustomerController) GetCustomerDetail(ctx *gin.Context) {
	// 1. 参数校验
	idStr := ctx.Query("id")
	if idStr == "" {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerDetail(id)
	if err != nil {
		results.Failed(ctx, "获取业务应用账户详情失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取业务应用账户详情成功", result, nil)
}

// UpdateCustomerStatus 更新业务应用账户状态
func (c *CustomerController) UpdateCustomerStatus(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetCustomerStatusUpdateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	idValue, exists := validationResult.Data["id"]
	if !exists {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	// 前端传递的是 number 类型，在 Go 中解析为 float64
	floatID, ok := idValue.(float64)
	if !ok {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}
	id := int64(floatID)
	// 2. 业务逻辑处理
	service := CustomerService{}
	err := service.UpdateCustomerStatus(id, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "更新业务应用账户状态失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "更新业务应用账户状态成功", nil, nil)
}

// UpdateCustomerRemark 更新业务应用账户备注
func (c *CustomerController) UpdateCustomerRemark(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetCustomerRemarkUpdateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 从请求体中获取ID
	idValue, exists := validationResult.Data["id"]
	if !exists {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	// 前端传递的是 number 类型，在 Go 中解析为 float64
	floatID, ok := idValue.(float64)
	if !ok {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}
	id := int64(floatID)

	// 2. 业务逻辑处理
	service := CustomerService{}
	err := service.UpdateCustomerRemark(id, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "更新业务应用账户备注失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "更新业务应用账户备注成功", nil, nil)
}

// GetCustomerOptions 获取业务应用账户筛选选项
func (c *CustomerController) GetCustomerOptions(ctx *gin.Context) {
	// 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerOptions()
	if err != nil {
		results.Failed(ctx, "获取业务应用账户选项失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "获取业务应用账户选项成功", result, nil)
}

// GetCustomerRemarks 获取业务应用账户备注历史
func (c *CustomerController) GetCustomerRemarks(ctx *gin.Context) {
	// 1. 参数校验
	idStr := ctx.Query("id")
	if idStr == "" {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerDetail(id)
	if err != nil {
		results.Failed(ctx, "获取业务应用账户备注历史失败", err.Error())
		return
	}

	// 构建备注响应
	remarkData := map[string]interface{}{
		"id":         result.Id,
		"userRemark": result.UserRemark,
		"updateTime": result.CreateTime, // 暂时使用创建时间，后续可添加备注更新时间字段
	}

	// 3. 返回结果
	results.Success(ctx, "获取业务应用账户备注历史成功", remarkData, nil)
}

// GetCustomerStatistics 获取业务应用账户统计信息
func (c *CustomerController) GetCustomerStatistics(ctx *gin.Context) {
	// 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerStatistics()
	if err != nil {
		results.Failed(ctx, "获取业务应用账户统计信息失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "获取业务应用账户统计信息成功", result, nil)
}

// ExportCustomers 导出业务应用账户数据
func (c *CustomerController) ExportCustomers(ctx *gin.Context) {
	// 业务逻辑处理
	service := CustomerService{}
	err := service.ExportCustomers(ctx, nil)
	if err != nil {
		results.Failed(ctx, "导出业务应用账户数据失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "导出业务应用账户数据成功", nil, nil)
}

// ListRepurchaseCustomers 获取复购业务应用账户列表
func (c *CustomerController) ListRepurchaseCustomers(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetRepurchaseCustomerListSchema()
	validator := jsonschema.NewValidator(schema)

	// 从POST请求体获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetRepurchaseCustomerList(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取复购业务应用账户列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取复购业务应用账户列表成功", result, nil)
}

// SendRepurchaseSMS 发送复购短信
func (c *CustomerController) SendRepurchaseSMS(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetSendRepurchaseSMSSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	err := service.SendRepurchaseSMS(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "发送复购短信失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "发送复购短信成功", nil, nil)
}

// RecordRepurchaseAwaken 记录复购唤醒
func (c *CustomerController) RecordRepurchaseAwaken(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetRecordRepurchaseAwakenSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 获取当前登录用户信息
	userInfo, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "认证失败", "无法获取用户信息")
		return
	}

	// 将用户信息添加到请求数据中
	if user, ok := userInfo.(*middleware.UserClaims); ok {
		validationResult.Data["operatorId"] = user.ID

		// 优先使用Name，如果为空则使用Username
		operatorName := user.Name
		if operatorName == "" {
			operatorName = user.Username
		}
		validationResult.Data["operatorName"] = operatorName
	} else {
		results.Failed(ctx, "认证失败", "用户信息格式错误")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	err := service.RecordRepurchaseAwaken(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "记录复购唤醒失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "记录复购唤醒成功", nil, nil)
}

// GetRepurchaseAwakenRecords 获取复购唤醒记录
func (c *CustomerController) GetRepurchaseAwakenRecords(ctx *gin.Context) {
	// 1. 参数校验
	customerIdStr := ctx.Query("customerId")
	if customerIdStr == "" {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	customerId, err := strconv.ParseInt(customerIdStr, 10, 64)
	if err != nil {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetRepurchaseAwakenRecords(customerId)
	if err != nil {
		results.Failed(ctx, "获取复购唤醒记录失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取复购唤醒记录成功", result, nil)
}

// GetRepurchaseCustomerOptions 获取复购业务应用账户筛选选项
func (c *CustomerController) GetRepurchaseCustomerOptions(ctx *gin.Context) {
	// 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetRepurchaseCustomerOptions()
	if err != nil {
		results.Failed(ctx, "获取复购业务应用账户选项失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "获取复购业务应用账户选项成功", result, nil)
}

// UnlockCustomer 解除注销业务应用账户
func (c *CustomerController) UnlockCustomer(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	idInterface, exists := requestData["id"]
	if !exists {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	id, err := strconv.ParseInt(fmt.Sprintf("%v", idInterface), 10, 64)
	if err != nil {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	err = service.UnlockCustomer(id)
	if err != nil {
		results.Failed(ctx, "解除注销业务应用账户失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "解除注销业务应用账户成功", nil, nil)
}

// UpdateCustomerQuota 修改用户额度
func (c *CustomerController) UpdateCustomerQuota(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetUpdateQuotaSchema()
	validator := jsonschema.NewValidator(schema)

	// 从POST请求体获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 参数转换
	params := UpdateQuotaParams{
		CustomerID: int(requestData["customerId"].(float64)),
		ProductID:  int(requestData["productId"].(float64)),
		Amount:     requestData["amount"].(float64),
	}

	// 3. 业务逻辑处理
	service := CustomerService{}
	err := service.UpdateCustomerQuota(params)
	if err != nil {
		results.Failed(ctx, "修改客户额度失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "修改客户额度成功", nil, nil)
}

// GetBlacklistCustomers 获取黑名单用户列表
func (c *CustomerController) GetBlacklistCustomers(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetBlacklistCustomerListSchema()
	validator := jsonschema.NewValidator(schema)

	// 从GET请求查询参数获取数据
	requestData := make(map[string]interface{})

	// 获取查询参数
	if idCard := ctx.Query("idCard"); idCard != "" {
		requestData["idCard"] = idCard
	}
	if userName := ctx.Query("userName"); userName != "" {
		requestData["userName"] = userName
	}
	if telephone := ctx.Query("telephone"); telephone != "" {
		requestData["telephone"] = telephone
	}
	if page := ctx.Query("page"); page != "" {
		if pageNum, err := strconv.Atoi(page); err == nil {
			requestData["page"] = float64(pageNum)
		}
	}
	if pageSize := ctx.Query("pageSize"); pageSize != "" {
		if pageSizeNum, err := strconv.Atoi(pageSize); err == nil {
			requestData["pageSize"] = float64(pageSizeNum)
		}
	}

	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetBlacklistCustomers(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取黑名单用户列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取黑名单用户列表成功", result, nil)
}

// DeleteCustomer 删除业务应用账户 type = soft 为软删除，type = hard 为硬删除
func (c *CustomerController) PostDeleteCustomer(ctx *gin.Context) {

	// 获取当前登录用户信息
	userInfo, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "认证失败", "无法获取用户信息")
		return
	}
	userClaims := userInfo.(*middleware.UserClaims)
	currentUserID := int(userClaims.ID)
	if currentUserID != 1 {
		results.Failed(ctx, "权限不足", "此账户无删除账户权限")
		return
	}

	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}
	idInterface, exists := requestData["id"]
	if !exists {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	id, err := strconv.ParseInt(fmt.Sprintf("%v", idInterface), 10, 64)
	if err != nil {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}

	typeInterface, exists := requestData["type"]
	if !exists {
		results.Failed(ctx, "参数错误", "type不能为空")
		return
	}
	typeTxt, ok := typeInterface.(string)
	if !ok {
		results.Failed(ctx, "参数错误", "type格式无效")
		return
	}

	if typeTxt != "soft" && typeTxt != "hard" {
		results.Failed(ctx, "参数错误", "type格式无效")
		return
	}

	// 检查用户是否有在途订单
	orderService := order.NewOrderService(ctx)
	orderStats, err := orderService.GetUserOrderStats(id)
	if err != nil {
		log.Error("删除失败: 查询用户订单统计失败 userId=%v, err=%v", id, err)

		results.Failed(ctx, "删除失败，查询订单信息异常", err)

		return
	}

	// 检查是否有在途订单
	inProgressOrders := gconv.Int(orderStats["in_progress_orders"])
	if inProgressOrders > 0 {
		log.Error("删除失败，用户存在未完结的订单 userId=%v, inProgressCount=%d", id, inProgressOrders)
		results.Failed(ctx, "删除失败，用户存在未完结的订单", nil)
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	err = service.deleteCustomer(id, typeTxt, currentUserID)

	if err != nil {
		results.Failed(ctx, "删除业务应用账户失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "删除业务应用账户成功", nil, nil)
}

// ImportBlacklist 导入黑名单
func (c *CustomerController) ImportBlacklist(ctx *gin.Context) {
	// 1. 获取上传的文件
	file, header, err := ctx.Request.FormFile("file")
	if err != nil {
		results.Failed(ctx, "获取上传文件失败: "+err.Error(), nil)
		return
	}
	defer file.Close()

	// 2. 验证文件类型
	if !strings.HasSuffix(strings.ToLower(header.Filename), ".xlsx") {
		results.Failed(ctx, "文件格式错误，请上传XLSX文件", nil)
		return
	}

	// 3. 调用service层处理导入逻辑
	service := &CustomerService{}
	result, err := service.ImportBlacklist(file)
	if err != nil {
		results.Failed(ctx, "导入失败: "+err.Error(), nil)
		return
	}

	// 4. 返回结果
	results.Success(ctx, "导入成功", result, nil)
}

// GetBlacklistTemplate 下载黑名单导入模板
func (c *CustomerController) GetBlacklistTemplate(ctx *gin.Context) {
	// 创建新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 设置表头
	headers := []string{"userName", "idCard", "telephone", "riskMsg"}
	for i, header := range headers {
		cell, _ := excelize.CoordinatesToCellName(i+1, 1)
		f.SetCellValue("Sheet1", cell, header)
		
		// 设置列格式为文本类型
		columnLetter, _ := excelize.ColumnNumberToName(i + 1)
		columnRange := columnLetter + ":" + columnLetter
		style, _ := f.NewStyle(&excelize.Style{
			NumFmt: 49, // 文本格式
		})
		f.SetColStyle("Sheet1", columnRange, style)
	}

	// 添加示例数据行
	exampleData := []string{"张三", "******************", "15827843161", "用户征信被拉黑(示例数据,导入时删除即可)"}
	for i, data := range exampleData {
		cell, _ := excelize.CoordinatesToCellName(i+1, 2)
		f.SetCellValue("Sheet1", cell, data)
	}

	// 生成Excel文件内容
	buf, err := f.WriteToBuffer()
	if err != nil {
		results.Failed(ctx, "生成模板文件失败: "+err.Error(), nil)
		return
	}

	// 设置响应头
	ctx.Header("Content-Description", "File Transfer")
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Content-Disposition", "attachment; filename=import_blacklist.xlsx")
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")

	// 返回Excel文件内容
	ctx.Data(200, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", buf.Bytes())
}
