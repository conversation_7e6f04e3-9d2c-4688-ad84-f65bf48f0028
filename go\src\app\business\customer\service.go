package customer

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"mime/multipart"
	"regexp"
	"strconv"
	"strings"
	"time"

	"fincore/global"
	"fincore/model"
	"fincore/utils/gform"
	"fincore/utils/idcard"
	"fincore/utils/log"
	"fincore/utils/oss"
	"fincore/utils/pagination"
	"fincore/utils/shopspringutils"

	"github.com/gin-gonic/gin"
	idvalidator "github.com/guanguans/id-validator"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
)

type CustomerService struct{}

// CustomerQueryParams 业务应用账户查询参数
type CustomerQueryParams struct {
	// 基础信息
	Name       string `json:"name"`
	Mobile     string `json:"mobile"`
	IdCard     string `json:"idCard"`
	ReviewerID string `json:"reviewerID"`
	ChannelId  string `json:"channelId"`

	// 风控相关
	IsRisk         string `json:"isRisk"`
	RiskFlowNumber string `json:"riskFlowNumber"`
	RiskScoreMin   string `json:"riskScoreMin"`
	RiskScoreMax   string `json:"riskScoreMax"`

	// 额度相关
	ReminderQuotaMin string `json:"reminderQuotaMin"`
	ReminderQuotaMax string `json:"reminderQuotaMax"`

	// 状态相关
	IdentityStatus     string `json:"identityStatus"`
	IdentitySubStatus  string `json:"identitySubStatus"`
	OrderStatus        string `json:"orderStatus"`
	RegisterIncomplete string `json:"registerIncomplete"`
	RealNameIncomplete string `json:"realNameIncomplete"`
	HasQuotaNoOrder    string `json:"hasQuotaNoOrder"`
	IsNewUser          string `json:"isNewUser"`
	IsComplaint        string `json:"isComplaint"`

	// 其他
	UserRemark   string `json:"userRemark"`
	LoanCount    string `json:"loanCount"`
	DeviceSource string `json:"deviceSource"`

	// 时间范围
	LoanTimeStart     string `json:"loanTimeStart"`
	LoanTimeEnd       string `json:"loanTimeEnd"`
	RegisterTimeStart string `json:"registerTimeStart"`
	RegisterTimeEnd   string `json:"registerTimeEnd"`

	// 分页
	Page     string `json:"page"`
	PageSize string `json:"pageSize"`
}

// CustomerListItem 业务应用账户列表项
type CustomerListItem struct {
	Id                        int64   `json:"id" db:"id"`
	ChannelName               string  `json:"channelName" db:"channel_name"`
	ReviewerName              string  `json:"reviewerName" db:"reviewer_name"`
	DeviceSource              string  `json:"deviceSource" db:"deviceSource"`
	Name                      string  `json:"name" db:"name"`
	Mobile                    string  `json:"mobile" db:"mobile"`
	UserRemark                string  `json:"userRemark" db:"userRemark"`
	CreateTime                string  `json:"createTime" db:"createtime"`
	IdentityStatusText        string  `json:"identityStatusText"`
	IdentityStatusValue       uint64  `json:"identityStatusValue" db:"identityStatus"`
	OrderStatusText           string  `json:"orderStatusText"`
	OrderStatusValue          int     `json:"orderStatusValue" db:"orderStatus"`
	ComplaintStatusText       string  `json:"complaintStatusText"`
	ComplaintStatusValue      int     `json:"complaintStatusValue" db:"complaintStatus"`
	AllQuota                  float64 `json:"allQuota" db:"allQuota"`
	ReminderQuota             float64 `json:"reminderQuota" db:"reminderQuota"`
	RiskScore                 float64 `json:"riskScore" db:"riskScore"`
	IdCardMasked              string  `json:"idCardMasked"`
	IdCardFull                string  `json:"idCardFull" db:"idCard"`
	Gender                    string  `json:"gender"`
	Age                       int     `json:"age"`
	IdCardFrontUrl            string  `json:"idCardFrontUrl" db:"idCardFrontUrl"`
	IdCardBackUrl             string  `json:"idCardBackUrl" db:"idCardBackUrl"`
	OcrEndDate                string  `json:"ocrEndDate"`
	EmergencyContact0Name     string  `json:"emergencyContact0Name"`
	EmergencyContact0Phone    string  `json:"emergencyContact0Phone"`
	EmergencyContact0Relation string  `json:"emergencyContact0Relation"`
	EmergencyContact1Name     string  `json:"emergencyContact1Name"`
	EmergencyContact1Phone    string  `json:"emergencyContact1Phone"`
	EmergencyContact1Relation string  `json:"emergencyContact1Relation"`
	Degree                    string  `json:"degree"`
	Marry                     string  `json:"marry"`
	Occupation                string  `json:"occupation"`
	YearRevenue               string  `json:"yearRevenue"`
	FacePhotoUrl              string  `json:"facePhotoUrl"`
	// 新增响应字段
	OrderStatus         int    `json:"orderStatus"`                  // 订单状态（最新订单的状态）
	LoanCount           int    `json:"loanCount"`                    // 放款次数（状态为1或3的订单数量）
	BorrowingOrderCount int    `json:"borrowingOrderCount"`          // 在借订单数（状态为0和1的订单数量）
	TotalOrderCount     int    `json:"totalOrderCount"`              // 历史订单总数（包括所有状态的订单）
	LoanTime            string `json:"loanTime"`                     // 进件时间（客户最近一次创建订单的时间）
	LastRepayTime       string `json:"lastRepayTime"`                // 最后还款时间（最近一次还款的时间）
	BillDueTime         string `json:"billDueTime"`                  // 账单到期时间（下次还款到期时间）
	IsCancelled         int    `json:"isCancelled"`                  // 是否注销
	IsCancelledText     string `json:"isCancelledText"`              // 是否注销文本
	BlockReason         string `json:"blockReason" db:"blockReason"` // 拉黑原因

}

// CustomerListResponse 业务应用账户列表响应
type CustomerListResponse struct {
	List       []CustomerListItem `json:"list"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"pageSize"`
	TotalPages int                `json:"totalPages"`
	HasNext    bool               `json:"hasNext"`
	HasPrev    bool               `json:"hasPrev"`
}

// CustomerDetail 业务应用账户详情
type CustomerDetail struct {
	CustomerListItem
	// 可以添加更多详情字段
}

// CustomerOptions 业务应用账户筛选选项
type CustomerOptions struct {
	ReviewerOptions []OptionItem `json:"reviewerOptions"`
	ChannelOptions  []OptionItem `json:"channelOptions"`
}

// OptionItem 选项项
type OptionItem struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

// GetCustomerList 获取业务应用账户列表 - 使用窗口函数查询和并行查询
func (s *CustomerService) GetCustomerList(params map[string]interface{}) (*CustomerListResponse, error) {
	// 获取分页参数
	paginationReq := pagination.PaginationRequest{
		Page:     s.getIntFromMap(params, "page", 1),
		PageSize: s.getIntFromMap(params, "pageSize", 10),
	}

	// 使用统一的查询方法（包含统计字段）
	businessAppAccountService := model.NewBusinessAppAccountService()
	options := model.QueryOptions{
		IncludeStats: true,
		ForExport:    false,
		SingleRecord: false,
	}
	result, err := businessAppAccountService.GetBusinessAppAccountUnified(params, paginationReq, options)
	if err != nil {
		return nil, fmt.Errorf("获取业务应用账户列表失败: %v", err)
	}

	// 转换数据格式
	response := &CustomerListResponse{
		List:       make([]CustomerListItem, 0),
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
		HasNext:    result.HasNext,
		HasPrev:    result.HasPrev,
	}

	if businessAppAccounts, ok := result.Data.([]model.BusinessAppAccount); ok && len(businessAppAccounts) > 0 {

		userIDs := make([]int64, len(businessAppAccounts))
		for i, account := range businessAppAccounts {
			userIDs[i] = account.ID
		}

		// 并行查询统计信息
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		g, ctx := errgroup.WithContext(ctx)

		var (
			orderStats map[int64]model.OrderStatsInfo
			repayStats map[int64]model.RepayStatsInfo
		)

		// 查询订单统计信息
		g.Go(func() error {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				var err error
				orderStats, err = businessAppAccountService.GetOrderStatsForUsers(userIDs)
				return err
			}
		})

		// 查询还款统计信息
		g.Go(func() error {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				var err error
				repayStats, err = businessAppAccountService.GetRepayStatsForUsers(userIDs)
				return err
			}
		})

		if err := g.Wait(); err != nil {
			if ctx.Err() == context.DeadlineExceeded {
				return nil, fmt.Errorf("查询超时，请稍后重试")
			}
			return nil, fmt.Errorf("并行查询统计信息失败: %v", err)
		}

		// 组装响应数据
		for _, businessAppAccount := range businessAppAccounts {
			var (
				userOrderStat model.OrderStatsInfo // 订单信息
				userRepayStat model.RepayStatsInfo // 还款信息
				ok            bool
			)

			if userOrderStat, ok = orderStats[businessAppAccount.ID]; ok {
				userOrderStat = orderStats[businessAppAccount.ID]
			}

			if userRepayStat, ok = repayStats[businessAppAccount.ID]; ok {
				userRepayStat = repayStats[businessAppAccount.ID]
			}

			item := CustomerListItem{
				Id:                   businessAppAccount.ID,
				ChannelName:          businessAppAccount.ChannelName,
				ReviewerName:         businessAppAccount.ReviewerName,
				DeviceSource:         businessAppAccount.DeviceSource,
				Name:                 businessAppAccount.Name,
				Mobile:               businessAppAccount.Mobile,
				UserRemark:           businessAppAccount.UserRemark,
				CreateTime:           s.formatTimestamp(businessAppAccount.CreateTime),
				IdentityStatusValue:  businessAppAccount.IdentityStatus,
				IdentityStatusText:   s.getIdentityStatusText(businessAppAccount.IdentityStatus),
				ComplaintStatusValue: businessAppAccount.ComplaintStatus,
				ComplaintStatusText:  s.getComplaintStatusText(businessAppAccount.ComplaintStatus),
				AllQuota:             businessAppAccount.AllQuota,
				ReminderQuota:        businessAppAccount.ReminderQuota,
				RiskScore:            businessAppAccount.RiskScore,
				IdCardFull:           businessAppAccount.IDCard,
				IdCardMasked:         s.maskIdCard(businessAppAccount.IDCard),
				Gender:               idcard.GetGenderFromIDCard(businessAppAccount.IDCard),
				Age:                  idcard.CalculateAge(businessAppAccount.IDCard),
				IdCardFrontUrl:       businessAppAccount.IDCardFrontUrl,
				IdCardBackUrl:        businessAppAccount.IDCardBackUrl,
				OrderStatus:          businessAppAccount.OrderStatus,                 // 订单状态
				LoanCount:            businessAppAccount.LoanCount,                   // 放款次数
				BorrowingOrderCount:  userOrderStat.BorrowingOrderCount,              // 在借订单数
				TotalOrderCount:      userOrderStat.TotalOrderCount,                  // 历史订单总数
				LoanTime:             s.formatTimestamp(businessAppAccount.LoanTime), // 进件时间
				LastRepayTime:        s.formatTimestamp(userRepayStat.LastRepayTime), // 最后还款时间
				BillDueTime:          s.formatTimestamp(userRepayStat.BillDueTime),   // 账单到期时间
				IsCancelled:          businessAppAccount.IsCancelled,                 // 是否注销
				IsCancelledText:      s.getIsCancelledText(businessAppAccount.IsCancelled),
			}
			response.List = append(response.List, item)
		}
	}

	return response, nil
}

// GetCustomerDetail 获取业务应用账户详情
func (s *CustomerService) GetCustomerDetail(id int64) (*CustomerDetail, error) {

	var (
		ctx                context.Context
		cancel             context.CancelFunc
		orderStats         map[int64]model.OrderStatsInfo
		businessAppAccount *model.BusinessAppAccount
		err                error
	)

	ctx, cancel = context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	g, _ := errgroup.WithContext(ctx)

	businessAppAccountService := model.NewBusinessAppAccountService()

	g.Go(func() error {

		businessAppAccount, err = businessAppAccountService.GetBusinessAppAccountByID(id)
		if err != nil {
			return fmt.Errorf("获取业务应用账户详情失败: %v", err)
		}
		return nil
	})

	g.Go(func() error {
		userIDs := []int64{id}
		orderStats, err = businessAppAccountService.GetOrderStatsForUsers(userIDs)
		if err != nil {
			return fmt.Errorf("获取订单统计信息失败: %v", err)
		}

		return nil
	})

	if err := g.Wait(); err != nil {
		return nil, err
	}

	var idCardFrontUrl, idCardBackUrl, facePhotoUrl string
	if oss.IsOSSEnabled() {

		ossClient, err := oss.GetOSSClient()
		if err != nil {
			log.Error("OSS客户端初始化失败: %v", err)
			return nil, err
		} else {
			objectKey := oss.GetObjectKeyFromURL(businessAppAccount.IDCardFrontUrl)
			idCardFrontUrl, err = ossClient.GetSignedURL(objectKey, 15*time.Minute)
			if err != nil {
				log.Error("获取ID卡正面URL失败: %v", err)
			}
			objectKey = oss.GetObjectKeyFromURL(businessAppAccount.IDCardBackUrl)
			idCardBackUrl, err = ossClient.GetSignedURL(objectKey, 15*time.Minute)
			if err != nil {
				log.Error("获取ID卡反面URL失败: %v", err)
			}
			objectKey = oss.GetObjectKeyFromURL(businessAppAccount.FacePhotoUrl)
			facePhotoUrl, err = ossClient.GetSignedURL(objectKey, 15*time.Minute)
			if err != nil {
				log.Error("获取人脸照片URL失败: %v", err)
			}
		}

	} else {
		idCardFrontUrl = businessAppAccount.IDCardFrontUrl
		idCardBackUrl = businessAppAccount.IDCardBackUrl
		facePhotoUrl = businessAppAccount.FacePhotoUrl
	}

	// 转换为本地类型
	detail := &CustomerDetail{
		CustomerListItem: CustomerListItem{
			Id:                        businessAppAccount.ID,
			ChannelName:               businessAppAccount.ChannelName,
			ReviewerName:              businessAppAccount.ReviewerName,
			DeviceSource:              businessAppAccount.DeviceSource,
			Name:                      businessAppAccount.Name,
			Mobile:                    businessAppAccount.Mobile,
			UserRemark:                businessAppAccount.UserRemark,
			CreateTime:                s.formatTimestamp(businessAppAccount.CreateTime),
			LoanCount:                 businessAppAccount.LoanCount,
			IdentityStatusValue:       businessAppAccount.IdentityStatus,
			IdentityStatusText:        s.getIdentityStatusText(businessAppAccount.IdentityStatus),
			OrderStatusValue:          businessAppAccount.OrderStatus,
			OrderStatusText:           s.getOrderStatusText(businessAppAccount.OrderStatus),
			ComplaintStatusValue:      businessAppAccount.ComplaintStatus,
			ComplaintStatusText:       s.getComplaintStatusText(businessAppAccount.ComplaintStatus),
			AllQuota:                  businessAppAccount.AllQuota,
			ReminderQuota:             businessAppAccount.ReminderQuota,
			RiskScore:                 businessAppAccount.RiskScore,
			IdCardFull:                businessAppAccount.IDCard,
			IdCardMasked:              s.maskIdCard(businessAppAccount.IDCard),
			Gender:                    businessAppAccount.Gender,
			Age:                       businessAppAccount.Age,
			OcrEndDate:                businessAppAccount.OcrEndDate,
			IdCardFrontUrl:            idCardFrontUrl,
			IdCardBackUrl:             idCardBackUrl,
			FacePhotoUrl:              facePhotoUrl,
			EmergencyContact0Name:     businessAppAccount.EmergencyContact0Name,
			EmergencyContact0Phone:    businessAppAccount.EmergencyContact0Phone,
			EmergencyContact0Relation: businessAppAccount.EmergencyContact0Relation,
			EmergencyContact1Name:     businessAppAccount.EmergencyContact1Name,
			EmergencyContact1Phone:    businessAppAccount.EmergencyContact1Phone,
			EmergencyContact1Relation: businessAppAccount.EmergencyContact1Relation,
			Degree:                    businessAppAccount.Degree,
			Marry:                     businessAppAccount.Marry,
			Occupation:                businessAppAccount.Occupation,
			YearRevenue:               businessAppAccount.YearRevenue,
			LoanTime:                  s.formatTimestamp(businessAppAccount.LoanTime), // 进件时间
		},
	}

	detail.CustomerListItem.LoanTime = ""
	if userOrderStat, ok := orderStats[businessAppAccount.ID]; ok {
		detail.TotalOrderCount = userOrderStat.TotalOrderCount
		detail.BorrowingOrderCount = userOrderStat.BorrowingOrderCount
	}

	return detail, nil
}

// UpdateCustomerStatus 更新业务应用账户状态
func (s *CustomerService) UpdateCustomerStatus(id int64, data map[string]interface{}) error {
	businessAppAccountService := model.NewBusinessAppAccountService()
	return businessAppAccountService.UpdateBusinessAppAccountStatus(id, data)
}

// UpdateCustomerRemark 更新业务应用账户备注
func (s *CustomerService) UpdateCustomerRemark(id int64, data map[string]interface{}) error {
	businessAppAccountService := model.NewBusinessAppAccountService()
	return businessAppAccountService.UpdateBusinessAppAccountRemark(id, data)
}

// GetCustomerOptions 获取业务应用账户筛选选项
func (s *CustomerService) GetCustomerOptions() (*CustomerOptions, error) {
	businessAppAccountService := model.NewBusinessAppAccountService()
	options, err := businessAppAccountService.GetBusinessAppAccountOptions()
	if err != nil {
		return nil, fmt.Errorf("获取业务应用账户选项失败: %v", err)
	}

	// 转换为本地类型
	response := &CustomerOptions{
		ReviewerOptions: make([]OptionItem, len(options.ReviewerOptions)),
		ChannelOptions:  make([]OptionItem, len(options.ChannelOptions)),
	}

	for i, option := range options.ReviewerOptions {
		response.ReviewerOptions[i] = OptionItem{
			Value: option.Value,
			Label: option.Label,
		}
	}

	for i, option := range options.ChannelOptions {
		response.ChannelOptions[i] = OptionItem{
			Value: option.Value,
			Label: option.Label,
		}
	}

	return response, nil
}

// GetCustomerStatistics 获取业务应用账户统计信息
func (s *CustomerService) GetCustomerStatistics() (map[string]interface{}, error) {
	// 预留接口，暂时返回空数据
	statistics := map[string]interface{}{
		"totalBusinessAppAccounts":     0,
		"newBusinessAppAccounts":       0,
		"activeBusinessAppAccounts":    0,
		"riskBusinessAppAccounts":      0,
		"complaintBusinessAppAccounts": 0,
	}

	return statistics, nil
}

// ExportCustomers 导出业务应用账户数据
func (s *CustomerService) ExportCustomers(ctx *gin.Context, filterData map[string]interface{}) error {
	// 使用GetCustomerList方法获取完整数据（包含统计字段）
	filterParams := make(map[string]interface{})
	for k, v := range filterData {
		filterParams[k] = fmt.Sprintf("%v", v)
	}
	filterParams["pageSize"] = "10000" // 导出所有数据

	customerResult, err := s.GetCustomerList(filterParams)
	if err != nil {
		return fmt.Errorf("获取客户列表数据失败: %v", err)
	}

	// 设置响应头
	ctx.Header("Content-Type", "text/csv")
	ctx.Header("Content-Disposition", "attachment; filename=business_app_accounts.csv")

	// 创建CSV写入器
	writer := csv.NewWriter(ctx.Writer)
	defer writer.Flush()

	// 写入表头
	headers := []string{
		"ID", "姓名", "手机号", "身份证号", "渠道名称", "审核员", "设备来源", "用户备注",
		"进件时间", "创建时间", "放款次数", "认证状态", "订单状态", "投诉状态",
		"总额度", "剩余额度", "风控分数",
	}
	writer.Write(headers)

	// 写入数据 - 使用CustomerListItem数据
	for _, item := range customerResult.List {
		row := []string{
			fmt.Sprintf("%d", item.Id),
			item.Name,
			item.Mobile,
			item.IdCardFull, // 使用完整身份证号，不脱敏
			item.ChannelName,
			item.ReviewerName,
			item.DeviceSource,
			item.UserRemark,
			item.LoanTime,                     // 进件时间
			item.CreateTime,                   // 创建时间
			fmt.Sprintf("%d", item.LoanCount), // 放款次数
			fmt.Sprintf("%d", item.IdentityStatusValue),
			fmt.Sprintf("%d", item.OrderStatus), // 订单状态
			fmt.Sprintf("%d", item.ComplaintStatusValue),
			fmt.Sprintf("%.2f", item.AllQuota),
			fmt.Sprintf("%.2f", item.ReminderQuota),
			fmt.Sprintf("%.2f", item.RiskScore),
		}
		writer.Write(row)
	}

	return nil
}

// ======================== 复购业务应用账户相关功能 ========================

// RepurchaseCustomerQueryParams 复购业务应用账户查询参数
type RepurchaseCustomerQueryParams struct {
	// 基础信息
	Name   string `json:"name"`
	Mobile string `json:"mobile"`
	IdCard string `json:"idCard"`

	// 渠道和订单信息
	ChannelId           string `json:"channelId"`
	IsCancelled         string `json:"isCancelled"`
	BorrowingOrderCount string `json:"borrowingOrderCount"`
	TotalOrderCount     string `json:"totalOrderCount"`

	// 额度信息
	TotalAmountMin     float64 `json:"totalAmountMin"`
	TotalAmountMax     float64 `json:"totalAmountMax"`
	AvailableAmountMin float64 `json:"availableAmountMin"`
	AvailableAmountMax float64 `json:"availableAmountMax"`

	// 排序和时间信息
	SortType           string `json:"sortType"`
	LastRepayTimeStart string `json:"lastRepayTimeStart"`
	LastRepayTimeEnd   string `json:"lastRepayTimeEnd"`
	BillDueTimeStart   string `json:"billDueTimeStart"`
	BillDueTimeEnd     string `json:"billDueTimeEnd"`

	// 分页
	Page     int `json:"page"`
	PageSize int `json:"pageSize"`
}

// RepurchaseCustomerItem 复购业务应用账户列表项
type RepurchaseCustomerItem struct {
	Id                  int64   `json:"id" db:"id"`
	ChannelName         string  `json:"channelName" db:"channel_name"`
	IsCancelled         int     `json:"isCancelled" db:"isCancelled"`
	IsCancelledText     string  `json:"isCancelledText"`
	Name                string  `json:"name" db:"name"`
	Mobile              string  `json:"mobile" db:"mobile"`
	IdCardMasked        string  `json:"idCardMasked"`
	IdCardFull          string  `json:"idCardFull" db:"idCard"`
	TotalAmount         float64 `json:"totalAmount" db:"allQuota"`
	AvailableAmount     float64 `json:"availableAmount" db:"reminderQuota"`
	BorrowingOrderCount int     `json:"borrowingOrderCount" db:"borrowingOrderCount"`
	TotalOrderCount     int     `json:"totalOrderCount" db:"totalOrderCount"`
	RegisterTime        string  `json:"registerTime" db:"register_time"`
	LastRepayTime       string  `json:"lastRepayTime" db:"lastRepayTime"`
	BillDueTime         string  `json:"billDueTime" db:"billDueTime"`
	LastAwakenTime      string  `json:"lastAwakenTime" db:"awaken_time"`
	AwakenCount         int     `json:"awakenCount" db:"awaken_count"`
	AwakenRemark        string  `json:"awakenRemark" db:"awaken_remark"`
}

// RepurchaseCustomerListResponse 复购业务应用账户列表响应
type RepurchaseCustomerListResponse struct {
	List       []RepurchaseCustomerItem `json:"list"`
	Total      int64                    `json:"total"`
	Page       int                      `json:"page"`
	PageSize   int                      `json:"pageSize"`
	TotalPages int                      `json:"totalPages"`
	HasNext    bool                     `json:"hasNext"`
	HasPrev    bool                     `json:"hasPrev"`
}

// RepurchaseAwakenRecord 复购唤醒记录
type RepurchaseAwakenRecord struct {
	Id            int64  `json:"id" db:"id"`
	CustomerId    int64  `json:"customerId" db:"customer_id"`
	AwakenContent string `json:"awakenContent" db:"awaken_content"`
	AwakenType    int    `json:"awakenType" db:"awaken_type"`
	AwakenStatus  int    `json:"awakenStatus" db:"awaken_status"`
	OperatorId    int64  `json:"operatorId" db:"operator_id"`
	OperatorName  string `json:"operatorName" db:"operator_name"`
	Remark        string `json:"remark" db:"remark"`
	CreateTime    string `json:"createTime" db:"create_time"`
}

// GetRepurchaseCustomerList 获取复购业务应用账户列表
func (s *CustomerService) GetRepurchaseCustomerList(params map[string]interface{}) (*RepurchaseCustomerListResponse, error) {
	// 获取分页参数
	paginationReq := pagination.PaginationRequest{
		Page:     s.getIntFromMap(params, "page", 1),
		PageSize: s.getIntFromMap(params, "pageSize", 10),
	}

	// 调用model层方法
	businessAppAccountService := model.NewBusinessAppAccountService()
	result, err := businessAppAccountService.GetRepurchaseBusinessAppAccountList(params, paginationReq)
	if err != nil {
		return nil, fmt.Errorf("获取复购业务应用账户列表失败: %v", err)
	}

	// 转换数据格式
	response := &RepurchaseCustomerListResponse{
		List:       make([]RepurchaseCustomerItem, 0),
		Total:      result.Total,
		Page:       result.Page,
		PageSize:   result.PageSize,
		TotalPages: result.TotalPages,
		HasNext:    result.HasNext,
		HasPrev:    result.HasPrev,
	}

	if businessAppAccounts, ok := result.Data.([]model.RepurchaseBusinessAppAccount); ok {
		for _, businessAppAccount := range businessAppAccounts {
			item := RepurchaseCustomerItem{
				Id:                  businessAppAccount.ID,
				ChannelName:         businessAppAccount.ChannelName,
				IsCancelled:         businessAppAccount.IsCancelled,
				IsCancelledText:     businessAppAccount.IsCancelledText,
				Name:                businessAppAccount.Name,
				Mobile:              businessAppAccount.Mobile,
				IdCardMasked:        businessAppAccount.IdCardMasked,
				IdCardFull:          businessAppAccount.IdCardFull,
				TotalAmount:         businessAppAccount.TotalAmount,
				AvailableAmount:     businessAppAccount.AvailableAmount,
				BorrowingOrderCount: businessAppAccount.BorrowingOrderCount,
				TotalOrderCount:     businessAppAccount.TotalOrderCount,
				RegisterTime:        businessAppAccount.RegisterTime,
				LastRepayTime:       businessAppAccount.LastRepayTime,
				BillDueTime:         businessAppAccount.BillDueTime,
				LastAwakenTime:      businessAppAccount.LastAwakenTime,
				AwakenCount:         businessAppAccount.AwakenCount,
				AwakenRemark:        businessAppAccount.AwakenRemark,
			}
			response.List = append(response.List, item)
		}
	}

	return response, nil
}

// SendRepurchaseSMS 发送复购短信
func (s *CustomerService) SendRepurchaseSMS(params map[string]interface{}) error {
	customerId := s.getInt64FromMap(params, "customerId")
	if customerId <= 0 {
		return fmt.Errorf("业务应用账户ID不能为空")
	}

	businessAppAccountService := model.NewBusinessAppAccountService()
	return businessAppAccountService.SendRepurchaseSMS(customerId)
}

// RecordRepurchaseAwaken 记录复购唤醒
func (s *CustomerService) RecordRepurchaseAwaken(params map[string]interface{}) error {
	// 构建唤醒记录
	record := model.RepurchaseAwakenRecord{
		CustomerID:    s.getInt64FromMap(params, "customerId"),
		AwakenType:    s.getIntFromMap(params, "awakenType", 1),
		AwakenContent: s.getStringFromMap(params, "awakenContent"),
		OperatorID:    s.getInt64FromMap(params, "operatorId"),
		Remark:        s.getStringFromMap(params, "remark"),
	}

	businessAppAccountService := model.NewBusinessAppAccountService()
	return businessAppAccountService.CreateRepurchaseAwakenRecord(record)
}

// GetRepurchaseAwakenRecords 获取复购唤醒记录
func (s *CustomerService) GetRepurchaseAwakenRecords(customerId int64) ([]RepurchaseAwakenRecord, error) {
	businessAppAccountService := model.NewBusinessAppAccountService()
	modelRecords, err := businessAppAccountService.GetRepurchaseAwakenRecords(customerId)
	if err != nil {
		return nil, fmt.Errorf("获取复购唤醒记录失败: %v", err)
	}

	// 转换为本地类型
	records := make([]RepurchaseAwakenRecord, len(modelRecords))
	for i, record := range modelRecords {
		records[i] = RepurchaseAwakenRecord{
			Id:            record.ID,
			CustomerId:    record.CustomerID,
			AwakenContent: record.AwakenContent,
			AwakenType:    record.AwakenType,
			AwakenStatus:  record.AwakenStatus,
			OperatorId:    record.OperatorID,
			OperatorName:  record.OperatorName,
			Remark:        record.Remark,
			CreateTime:    s.formatTimestamp(record.CreateTime),
		}
	}

	return records, nil
}

// GetRepurchaseCustomerOptions 获取复购业务应用账户筛选选项
func (s *CustomerService) GetRepurchaseCustomerOptions() (*CustomerOptions, error) {
	// 复购业务应用账户的选项与普通业务应用账户相同
	return s.GetCustomerOptions()
}

// ======================== 工具方法 ========================

// getIntFromMap 从map中获取int值
func (s *CustomerService) getIntFromMap(data map[string]interface{}, key string, defaultValue int) int {
	if val, ok := data[key]; ok && val != nil {
		switch v := val.(type) {
		case int:
			return v
		case int64:
			return int(v)
		case float64:
			return int(v)
		case string:
			if intVal, err := strconv.Atoi(v); err == nil {
				return intVal
			}
		}
	}
	return defaultValue
}

// getStringFromMap 从map中获取string值
func (s *CustomerService) getStringFromMap(data map[string]interface{}, key string) string {
	if val, ok := data[key]; ok && val != nil {
		return fmt.Sprintf("%v", val)
	}
	return ""
}

// getInt64FromMap 从map中获取int64值
func (s *CustomerService) getInt64FromMap(data map[string]interface{}, key string) int64 {
	if val, ok := data[key]; ok && val != nil {
		switch v := val.(type) {
		case int64:
			return v
		case int:
			return int64(v)
		case float64:
			return int64(v)
		case string:
			if int64Val, err := strconv.ParseInt(v, 10, 64); err == nil {
				return int64Val
			}
		}
	}
	return 0
}

// getUint64FromMap 从map中获取uint64值
func (s *CustomerService) getUint64FromMap(data map[string]interface{}, key string) uint64 {
	if val, ok := data[key]; ok && val != nil {
		switch v := val.(type) {
		case uint64:
			return v
		case int64:
			return uint64(v)
		case int:
			return uint64(v)
		case float64:
			return uint64(v)
		case string:
			if uint64Val, err := strconv.ParseUint(v, 10, 64); err == nil {
				return uint64Val
			}
		}
	}
	return 0
}

// getFloat64FromMap 从map中获取float64值
func (s *CustomerService) getFloat64FromMap(data map[string]interface{}, key string) float64 {
	if val, ok := data[key]; ok && val != nil {
		switch v := val.(type) {
		case float64:
			return v
		case int:
			return float64(v)
		case int64:
			return float64(v)
		case string:
			if floatVal, err := strconv.ParseFloat(v, 64); err == nil {
				return floatVal
			}
		}
	}
	return 0.0
}

// formatTimestamp 格式化时间戳
func (s *CustomerService) formatTimestamp(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format("2006-01-02 15:04:05")
}

// maskIdCard 身份证号脱敏处理
func (s *CustomerService) maskIdCard(idCard string) string {
	if idCard == "" {
		return ""
	}
	if len(idCard) < 8 {
		return idCard
	}
	return idCard[:4] + "********" + idCard[len(idCard)-4:]
}

// getIdentityStatusText 获取认证状态文本
func (s *CustomerService) getIdentityStatusText(status uint64) string {
	switch status {
	case 0:
		return "未认证"
	case 1:
		return "认证中"
	case 2:
		return "认证成功"
	case 3:
		return "认证失败"
	default:
		return "未知状态"
	}
}

// getOrderStatusText 获取订单状态文本
func (s *CustomerService) getOrderStatusText(status int) string {
	switch status {
	case 0:
		return "未下单"
	case 1:
		return "进行中"
	case 2:
		return "已完成"
	case 3:
		return "已取消"
	default:
		return "未知状态"
	}
}

// getComplaintStatusText 获取投诉状态文本
func (s *CustomerService) getComplaintStatusText(status int) string {
	switch status {
	case 0:
		return "否"
	case 1:
		return "是"
	default:
		return "未知状态"
	}
}

// getIsCancelledText 获取是否注销文本
func (s *CustomerService) getIsCancelledText(isCancelled int) string {
	switch isCancelled {
	case 0:
		return "未注销"
	case 1:
		return "已注销"
	default:
		return "未知状态"
	}
}

// UnlockCustomer 解除注销业务应用账户
func (s *CustomerService) UnlockCustomer(customerId int64) error {
	businessAppAccountService := model.NewBusinessAppAccountService()
	return businessAppAccountService.UnlockBusinessAppAccount(customerId)
}

// UpdateQuotaParams 修改额度参数
type UpdateQuotaParams struct {
	CustomerID int     `json:"customerId"`
	ProductID  int     `json:"productId"`
	Amount     float64 `json:"amount"`
}

// UpdateCustomerQuota 修改客户额度
func (s *CustomerService) UpdateCustomerQuota(params UpdateQuotaParams) error {
	businessAppAccountService := model.NewBusinessAppAccountService()
	productRulesService := model.NewProductRulesService()

	// 检查客户是否存在
	account, err := businessAppAccountService.GetAccountByID(int64(params.CustomerID))
	if err != nil {
		return fmt.Errorf("获取客户信息失败: %v", err)
	}
	if account == nil {
		return fmt.Errorf("客户不存在")
	}

	// 获取产品规则信息
	productRule, err := productRulesService.GetProductRuleByID(params.ProductID)
	if err != nil {
		return fmt.Errorf("获取产品规则失败: %v", err)
	}
	if productRule == nil {
		return fmt.Errorf("产品规则不存在")
	}

	// 使用decimal计算当前在借额度（总额度 - 剩余额度）
	currentBorrowedAmount := shopspringutils.SubtractAmountsWithDecimal(account.AllQuota, account.ReminderQuota)

	// 获取产品的最小贷款金额
	productLoanAmount := productRule.LoanAmount

	// 计算最小允许额度（产品贷款金额 + 当前在借额度）
	minAllowedAmount := shopspringutils.AddAmountsWithDecimal(productLoanAmount, currentBorrowedAmount)

	// 检查新额度是否满足最小要求
	if shopspringutils.CompareAmountsWithDecimal(params.Amount, minAllowedAmount) < 0 {
		global.App.Log.Warn("新额度不能小于产品最小贷款金额%.2f元与当前在借额度%.2f元之和%.2f元", zap.Float64("productLoanAmount", productLoanAmount), zap.Float64("currentBorrowedAmount", currentBorrowedAmount), zap.Float64("minAllowedAmount", minAllowedAmount))
		return fmt.Errorf("新额度不能小于产品最小贷款金额%.2f元与当前在借额度%.2f元之和%.2f元", productLoanAmount, currentBorrowedAmount, minAllowedAmount)
	}

	// 使用decimal计算新的剩余额度（新总额度 - 当前在借额度）
	newRemainingAmount := shopspringutils.SubtractAmountsWithDecimal(params.Amount, currentBorrowedAmount)

	// 使用CeilToTwoDecimal处理新额度和剩余额度
	finalAmount := shopspringutils.CeilToTwoDecimal(params.Amount)
	finalRemainingAmount := shopspringutils.CeilToTwoDecimal(newRemainingAmount)

	// 更新客户额度和产品ID
	err = businessAppAccountService.UpdateQuotaAndProduct(int64(params.CustomerID), finalAmount, finalRemainingAmount, params.ProductID)
	if err != nil {
		return fmt.Errorf("更新客户额度失败: %v", err)
	}

	return nil
}

// deleteCustomer 删除业务应用账户
// BlacklistItem 黑名单用户项
type BlacklistItem struct {
	ID          int64  `json:"id" db:"id"`
	Name        string `json:"name" db:"name"`
	IdCard      string `json:"idCard" db:"idCard"`
	Mobile      string `json:"mobile" db:"mobile"`
	BlockReason string `json:"blockReason" db:"blockReason"`
}

// BlacklistResponse 黑名单查询响应
type BlacklistResponse struct {
	Items      []BlacklistItem `json:"items"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	PageSize   int             `json:"pageSize"`
	TotalPages int             `json:"totalPages"`
	HasNext    bool            `json:"hasNext"`
	HasPrev    bool            `json:"hasPrev"`
}

// GetBlacklistCustomers 获取黑名单用户列表
func (s *CustomerService) GetBlacklistCustomers(params map[string]interface{}) (*BlacklistResponse, error) {
	businessAppAccountService := model.NewBusinessAppAccountService()

	// 构建分页请求
	page := s.getIntFromMap(params, "page", 1)
	pageSize := s.getIntFromMap(params, "pageSize", 20)
	paginationReq := pagination.PaginationRequest{
		Page:     page,
		PageSize: pageSize,
	}

	// 构建过滤条件
	filters := map[string]interface{}{
		"status": model.StatusTypeBlacklist, // 黑名单状态
	}

	// idCard 精确查询
	if idCard := s.getStringFromMap(params, "idCard"); idCard != "" {
		filters["idCard"] = idCard
	}

	// userName 精确查询
	if userName := s.getStringFromMap(params, "userName"); userName != "" {
		filters["name"] = userName
	}

	// telephone 精确查询
	if telephone := s.getStringFromMap(params, "telephone"); telephone != "" {
		filters["mobile"] = telephone
	}

	// 调用 BusinessAppAccountService 查询黑名单用户
	result, err := businessAppAccountService.GetBlacklistCustomers(filters, paginationReq)
	if err != nil {
		log.Error("获取黑名单用户列表失败: %v", err)
		return nil, fmt.Errorf("获取黑名单用户列表失败: %v", err)
	}

	// 转换数据格式
	var items []BlacklistItem
	if rawData, ok := result.Data.([]gform.Data); ok {
		for _, dataMap := range rawData {
			blacklistItem := BlacklistItem{
				ID:          getInt64FromMap(dataMap, "id"),
				Name:        getStringFromMap(dataMap, "name"),
				IdCard:      getStringFromMap(dataMap, "idCard"),
				Mobile:      getStringFromMap(dataMap, "mobile"),
				BlockReason: getStringFromMap(dataMap, "blockReason"),
			}
			items = append(items, blacklistItem)
		}
	}

	// 计算分页信息
	totalPages := int((result.Total + int64(pageSize) - 1) / int64(pageSize))
	hasNext := page < totalPages
	hasPrev := page > 1

	return &BlacklistResponse{
		Items:      items,
		Total:      result.Total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	}, nil
}

func (s *CustomerService) deleteCustomer(customerId int64, typeStr string, currentUserID int) error {
	businessAppAccountService := model.NewBusinessAppAccountService()
	// 检查客户是否存在
	account, err := businessAppAccountService.GetAccountByID(customerId)
	if err != nil {
		log.Error("删除客户失败: 获取客户信息失败, customerId=%d, err=%v", customerId, err)
		return fmt.Errorf("获取客户信息失败: %v", err)
	}
	if account == nil {
		return fmt.Errorf("客户不存在")
	}

	// 硬删除
	if typeStr == "hard" {
		err = businessAppAccountService.CompletelyDeleteBusinessAppAccountByID(customerId)
		if err != nil {
			log.Error("硬删除客户失败: %v", err)
			return fmt.Errorf("删除客户失败: %v", err)
		}
	}
	// 软删除
	if typeStr == "soft" {
		err = businessAppAccountService.DeleteBusinessAppAccountByID(customerId, 1, currentUserID)
		if err != nil {
			log.Error("软删除客户失败: %v", err)
			return fmt.Errorf("删除客户失败: %v", err)
		}
	}

	return nil
}

// BlacklistImportResult 黑名单导入结果
type BlacklistImportResult struct {
	TotalCount   int      `json:"total_count"`   // 总记录数
	SuccessCount int      `json:"success_count"` // 成功导入数
	FailedCount  int      `json:"failed_count"`  // 失败数
	Errors       []string `json:"errors"`        // 错误信息列表
}

// ImportBlacklist 导入黑名单
func (s *CustomerService) ImportBlacklist(file multipart.File) (*BlacklistImportResult, error) {
	result := &BlacklistImportResult{
		Errors: make([]string, 0),
	}

	// 1. 读取XLSX文件
	f, err := excelize.OpenReader(file)
	if err != nil {
		return nil, fmt.Errorf("读取XLSX文件失败: %v", err)
	}
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("读取工作表失败: %v", err)
	}

	if len(rows) == 0 {
		return nil, errors.New("XLSX文件为空")
	}

	// 2. 验证XLSX头部
	header := rows[0]
	expectedHeaders := []string{"userName", "idCard", "telephone", "riskMsg"}
	if len(header) != len(expectedHeaders) {
		return nil, fmt.Errorf("XLSX文件格式错误，期望%d列，实际%d列", len(expectedHeaders), len(header))
	}

	for i, expected := range expectedHeaders {
		if i >= len(header) || header[i] != expected {
			return nil, fmt.Errorf("XLSX文件头部错误，第%d列期望'%s'，实际'%s'", i+1, expected, func() string {
				if i >= len(header) {
					return "缺失"
				}
				return header[i]
			}())
		}
	}

	// 创建 model 服务实例
	businessAppAccountService := model.NewBusinessAppAccountService()

	// 3. 处理数据行
	result.TotalCount = len(rows) - 1 // 减去头部行
	for i, row := range rows[1:] {    // 跳过头部行
		rowNum := i + 2 // 行号从2开始（第1行是头部）

		if len(row) != 4 {
			err := fmt.Sprintf("第%d行数据列数错误，期望4列，实际%d列", rowNum, len(row))
			result.Errors = append(result.Errors, err)
			result.FailedCount++
			continue
		}

		userName := strings.TrimSpace(row[0])
		idCard := strings.TrimSpace(row[1])
		telephone := strings.TrimSpace(row[2])
		riskMsg := strings.TrimSpace(row[3])

		// 4. 数据验证
		if userName == "" {
			err := fmt.Sprintf("第%d行用户名不能为空", rowNum)
			result.Errors = append(result.Errors, err)
			result.FailedCount++
			continue
		}

		if idCard == "" {
			err := fmt.Sprintf("第%d行身份证号不能为空", rowNum)
			result.Errors = append(result.Errors, err)
			result.FailedCount++
			continue
		}

		if telephone == "" {
			err := fmt.Sprintf("第%d行手机号不能为空", rowNum)
			result.Errors = append(result.Errors, err)
			result.FailedCount++
			continue
		}

		// 5. 身份证验证
		if !s.validateIDCard(idCard) {
			err := fmt.Sprintf("第%d行身份证号格式不合法: %s", rowNum, idCard)
			result.Errors = append(result.Errors, err)
			result.FailedCount++
			continue
		}

		// 6. 手机号验证
		if !s.validateMobile(telephone) {
			err := fmt.Sprintf("第%d行手机号格式不合法: %s", rowNum, telephone)
			result.Errors = append(result.Errors, err)
			result.FailedCount++
			continue
		}

		// 7. 检查是否已存在
		exists, err := businessAppAccountService.CheckAccountExistsByIDCard(idCard)
		if err != nil {
			err := fmt.Sprintf("第%d行检查用户是否存在时出错: %v", rowNum, err)
			result.Errors = append(result.Errors, err)
			result.FailedCount++
			continue
		}

		// 8. 插入或更新数据
		if exists {
			// 更新现有记录
			err = businessAppAccountService.UpdateAccountToBlacklist(idCard, telephone, userName, riskMsg)
		} else {
			// 插入新记录
			err = businessAppAccountService.InsertBlacklistAccount(userName, idCard, telephone, riskMsg)
		}

		if err != nil {
			err := fmt.Sprintf("第%d行保存数据失败: %v", rowNum, err)
			result.Errors = append(result.Errors, err)
			result.FailedCount++
			continue
		}

		result.SuccessCount++
	}

	return result, nil
}

// validateIDCard 验证身份证号码
func (s *CustomerService) validateIDCard(idCard string) bool {
	return idvalidator.IsValid(idCard, false)
}

// validateMobile 验证手机号码
func (s *CustomerService) validateMobile(mobile string) bool {
	// 使用正则表达式验证手机号格式
	matched, _ := regexp.MatchString(`^1[3-9]\d{9}$`, mobile)
	return matched
}

// convertToGBK 将字符串转换为 GBK 编码
