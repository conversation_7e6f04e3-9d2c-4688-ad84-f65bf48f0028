package customer

import (
	"fincore/global"
	"fincore/model"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// setupTestEnvironment 设置测试环境，切换到正确的工作目录
func setupTestEnvironment() (string, error) {
	path, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 切换到src目录，确保能找到resource/config.yml
	srcPath := filepath.Join(path, "../../../")
	err = os.Chdir(srcPath)
	if err != nil {
		return "", err
	}

	return path, nil // 返回原始路径，用于defer恢复
}

func init() {
	os.Setenv("ENV", "dev")
	// 设置测试环境
	_, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")
}

// setupRealCustomerService 设置使用真实数据库的客户服务
func setupRealCustomerService() *CustomerService {
	return &CustomerService{}
}

// TestUpdateCustomerQuota 测试修改客户额度功能
func TestUpdateCustomerQuota(t *testing.T) {
	service := setupRealCustomerService()
	businessAppAccountService := model.NewBusinessAppAccountService()

	// 测试场景1：正常修改额度
	t.Run("正常修改客户额度", func(t *testing.T) {
		// 使用测试数据
		testCustomerID := 2
		testProductID := 3
		testAmount := 7000.0

		// 获取修改前的客户信息
		beforeAccount, err := businessAppAccountService.GetBusinessAppAccountByID(int64(testCustomerID))
		if err != nil {
			t.Logf("获取客户信息失败: %v", err)
			return
		}
		if beforeAccount == nil {
			t.Logf("测试客户不存在，跳过测试")
			return
		}

		t.Logf("修改前 - 客户ID: %d, 总额度: %.2f, 剩余额度: %.2f, 产品ID: %d",
			beforeAccount.ID, beforeAccount.AllQuota, beforeAccount.ReminderQuota, beforeAccount.AvailableProductID)

		// 执行额度修改
		params := UpdateQuotaParams{
			CustomerID: testCustomerID,
			ProductID:  testProductID,
			Amount:     testAmount,
		}

		err = service.UpdateCustomerQuota(params)
		if err != nil {
			t.Logf("UpdateCustomerQuota error: %v", err)
			return
		}

		// 验证修改后的结果
		afterAccount, err := businessAppAccountService.GetBusinessAppAccountByID(int64(testCustomerID))
		if err != nil {
			t.Logf("获取修改后客户信息失败: %v", err)
			return
		}

		assert.NotNil(t, afterAccount)
		assert.Equal(t, testAmount, afterAccount.AllQuota)
		// 注意：AvailableProductID 字段可能不在结构体中，这里只验证核心逻辑
		// assert.Equal(t, testProductID, afterAccount.AvailableProductID)

		// 验证剩余额度计算逻辑
		currentBorrowedAmount := beforeAccount.AllQuota - beforeAccount.ReminderQuota
		expectedReminderQuota := testAmount - currentBorrowedAmount
		if expectedReminderQuota < 0 {
			expectedReminderQuota = 0
		}
		assert.Equal(t, expectedReminderQuota, afterAccount.ReminderQuota)

		t.Logf("修改后 - 客户ID: %d, 总额度: %.2f, 剩余额度: %.2f, 产品ID: %d",
			afterAccount.ID, afterAccount.AllQuota, afterAccount.ReminderQuota, afterAccount.AvailableProductID)
		t.Logf("UpdateCustomerQuota 测试成功")
	})

}

// TestUpdateQuotaParams 测试参数验证
func TestUpdateQuotaParams(t *testing.T) {
	// 测试参数结构体
	params := UpdateQuotaParams{
		CustomerID: 123,
		ProductID:  456,
		Amount:     8888.88,
	}

	assert.Equal(t, 123, params.CustomerID)
	assert.Equal(t, 456, params.ProductID)
	assert.Equal(t, 8888.88, params.Amount)
	t.Logf("参数结构体测试通过: %+v", params)
}
