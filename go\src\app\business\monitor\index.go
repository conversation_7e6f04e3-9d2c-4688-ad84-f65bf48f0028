package monitor

import (
	"fincore/app/utils"
	"fincore/utils/gf"
	"fincore/utils/results"
	"reflect"

	"github.com/gin-gonic/gin"
)

type Index struct {
	NoNeedLogin []string //忽略登录接口配置-忽略全部传[*]
	NoNeedAuths []string //忽略RBAC权限认证接口配置-忽略全部传[*]
}

func init() {
	gf.Register(&Index{}, reflect.TypeOf(Index{}).PkgPath())
}

func (i *Index) ListLoginLogs(ctx *gin.Context) {
	// 参数校验
	validationResult := utils.ParamsValidateAndGet(ctx, GetLoginLogsSchema)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 业务逻辑处理
	result, err := GetLoginLogs(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取登录日志列表失败", err.Error())
		return
	}

	results.Success(ctx, "获取登录日志列表成功", result, nil)

}

// 短信记录列表
func (i *Index) ListSmsRecords(ctx *gin.Context) {
	// 参数校验
	validationResult := utils.ParamsValidateAndGet(ctx, GetSmsRecordsSchema)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 业务逻辑处理
	result, err := GetSmsRecords(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取短信记录列表失败", err.Error())
		return
	}

	results.Success(ctx, "获取短信记录列表成功", result, nil)

}

// 操作日志列表
func (i *Index) ListOperateLogs(ctx *gin.Context) {
	// 参数校验
	validationResult := utils.ParamsValidateAndGet(ctx, GetOperateLogsSchema)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 业务逻辑处理
	result, err := GetOperateLogs(ctx, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取操作日志列表失败", err.Error())
		return
	}

	results.Success(ctx, "获取操作日志列表成功", result, nil)

}
