package monitor

import (
	"fincore/app/utils"
	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/pagination"
	"fmt"

	"github.com/gin-gonic/gin"
)

func GetLoginLogs(ctx *gin.Context, params map[string]interface{}) (*pagination.PaginationResponse, error) {
	pageReq := utils.GetPageReq(params)
	countQuery := buildLoginLogListQuery(params)
	dataQuery := buildLoginLogListQuery(params).
		Fields(`l.id as id, 
		l.uid as uid,
		l.ip as ip,
		l.operate_name as operate_name,
		l.result as result,
		l.detail as detail,
		l.region as region,
		DATE_FORMAT(l.created_at,  '%Y-%m-%d %H:%i:%s') as created_at`).
		OrderBy("l.id DESC")
	logType := convert.GetIntFromMap(params, "log_type", 1)
	if logType == 1 {
		dataQuery = dataQuery.AddFields("a.username as user_name")
	} else {
		// 如果name是空字符串使用mobile
		dataQuery = dataQuery.AddFields("IF(a.name != '', a.name, a.mobile) as user_name")
	}

	// 使用分页工具函数执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, pageReq)
	if err != nil {
		return nil, fmt.Errorf("获取登录日志列表失败: %v", err)
	}

	return result, nil
}

// buildLoginLogListQuery 构建登录日志列表查询对象
func buildLoginLogListQuery(params map[string]interface{}) gform.IOrm {
	var query gform.IOrm
	logType := convert.GetIntFromMap(params, "log_type", 1)
	if logType == 1 {
		query = model.DB().Table("admin_operate_log l").
			LeftJoin("business_account a", "l.uid = a.id")
	} else {
		query = model.DB().Table("user_operate_log l").
			LeftJoin("business_app_account a", "l.uid = a.id")
	}

	// 应用筛选条件
	query = applyLoginLogListFilters(query, params, logType)

	return query
}

// applyLoginLogListFilters 应用登录日志列表筛选条件
func applyLoginLogListFilters(query gform.IOrm, params map[string]interface{}, logType int) gform.IOrm {

	// 用户姓名查询
	if userName, ok := params["user_name"]; ok && userName != "" {
		if logType == 1 {
			query = query.Where("a.username", "like", fmt.Sprintf("%%%v%%", userName))
		} else {
			query = query.Where("a.name", "like", fmt.Sprintf("%%%v%%", userName))
		}
	}

	// 逾期日期范围查询
	if startTime, ok := params["start_time"]; ok && startTime != "" {
		query = query.Where("l.created_at", ">=", startTime)
	}

	if endTime, ok := params["end_time"]; ok && endTime != "" {
		query = query.Where("l.created_at", "<=", endTime)
	}

	query = query.Where("operate_name", "in", []string{"登录", "短信登录"})

	return query
}

func GetSmsRecords(ctx *gin.Context, params map[string]interface{}) (*pagination.PaginationResponse, error) {
	pageReq := utils.GetPageReq(params)
	countQuery := buildSmsRecordListQuery(params)
	dataQuery := buildSmsRecordListQuery(params).
		Fields(`l.id as id, 
		l.title as title,
		l.content as content,
		l.sender as sender,
		l.recipient as recipient,
		l.mobile as mobile,
		DATE_FORMAT(l.created_at,  '%Y-%m-%d %H:%i:%s') as created_at`).
		OrderBy("l.id DESC")

	// 使用分页工具函数执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, pageReq)
	if err != nil {
		return nil, fmt.Errorf("获取短信记录列表失败: %v", err)
	}

	return result, nil
}

// buildSmsRecordListQuery 构建短信记录列表查询对象
func buildSmsRecordListQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB().Table("sms_record l")
	// 应用筛选条件
	query = applySmsRecordListFilters(query, params)
	return query
}

// applySmsRecordListFilters 应用短信记录列表筛选条件
func applySmsRecordListFilters(query gform.IOrm, params map[string]interface{}) gform.IOrm {
	// 短信标题查询
	if title, ok := params["title"]; ok && title != "" {
		query = query.Where("l.title", "like", fmt.Sprintf("%%%v%%", title))
	}
	// 接收人查询
	if recipient, ok := params["recipient"]; ok && recipient != "" {
		query = query.Where("l.recipient", "like", fmt.Sprintf("%%%v%%", recipient))
	}
	// 手机号查询
	if mobile, ok := params["mobile"]; ok && mobile != "" {
		query = query.Where("l.mobile", "like", fmt.Sprintf("%%%v%%", mobile))
	}

	// 日期范围查询
	if startTime, ok := params["start_time"]; ok && startTime != "" {
		query = query.Where("l.created_at", ">=", startTime)
	}

	if endTime, ok := params["end_time"]; ok && endTime != "" {
		query = query.Where("l.created_at", "<=", endTime)
	}
	return query
}

func GetOperateLogs(ctx *gin.Context, params map[string]interface{}) (*pagination.PaginationResponse, error) {
	pageReq := utils.GetPageReq(params)
	countQuery := buildOperateLogListQuery(params)
	dataQuery := buildOperateLogListQuery(params).
		Fields(`l.id as id, 
		l.uid as uid,
		l.operands_uid as operands_uid,
		l.operate_name as operate_name,
		a.username as operator,
		b.name as operand,
		b.mobile as operand_mobile,
		b.idCard as operand_idCard,
		l.result as result,
		l.detail as detail,
		l.region as region,
		DATE_FORMAT(l.created_at,  '%Y-%m-%d %H:%i:%s') as created_at`).
		OrderBy("l.id DESC")
	// 使用分页工具函数执行查询
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, pageReq)
	if err != nil {
		return nil, fmt.Errorf("获取操作日志列表失败: %v", err)
	}
	return result, nil
}

func buildOperateLogListQuery(params map[string]interface{}) gform.IOrm {
	query := model.DB().Table("admin_operate_log l").
		LeftJoin("business_account a", "l.uid = a.id").
		LeftJoin("business_app_account b", "l.operands_uid = b.id")
	// 应用筛选条件
	query = applyOperateLogListFilters(query, params)
	return query
}

func applyOperateLogListFilters(query gform.IOrm, params map[string]interface{}) gform.IOrm {

	// 操作人查询
	if operator, ok := params["operator"]; ok && operator != "" {
		query = query.Where("a.username", "like", fmt.Sprintf("%%%v%%", operator))
	}

	// 操作对象查询 根据接收的operand字段模糊嵌套查询关联business_app_account表的name和mobile和idCard字段进行模糊查询
	if operand, ok := params["operand"]; ok && operand != "" {
		query = query.Where("b.name", "like", fmt.Sprintf("%%%v%%", operand)).
			OrWhere("b.mobile", "like", fmt.Sprintf("%%%v%%", operand)).
			OrWhere("b.idCard", "like", fmt.Sprintf("%%%v%%", operand))
	}

	// 操作类型查询
	if operateName, ok := params["operate_name"]; ok && operateName != "" {
		query = query.Where("l.operate_name", "like", fmt.Sprintf("%%%v%%", operateName))
	}

	// 日期范围查询
	if startTime, ok := params["start_time"]; ok && startTime != "" {
		query = query.Where("l.created_at", ">=", startTime)
	}

	if endTime, ok := params["end_time"]; ok && endTime != "" {
		query = query.Where("l.created_at", "<=", endTime)
	}

	return query
}
