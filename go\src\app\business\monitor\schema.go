package monitor

import "fincore/utils/jsonschema"

func GetLoginLogsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "登录日志查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{

			"user_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   50,
				Description: "用户姓名",
			},
			"log_type": {
				Type:        "number",
				Required:    true,
				Description: "日志类型,1-管理员,2-用户",
				Enum:        []string{"1", "2"},
				Default:     1,
			},

			"start_time": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$",
				Description: "开始时间范围 (格式: YYYY-MM-DD HH:MM:SS)",
			},
			"end_time": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$",
				Description: "结束时间范围 (格式: YYYY-MM-DD HH:MM:SS)",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Max:         &[]float64{100}[0],
				Description: "每页数量",
				Default:     20,
			},
		},
		Required: []string{"log_type", "start_time", "end_time"},
	}
}

func GetSmsRecordsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "短信记录查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"title": {
				Type:        "string",
				Required:    false,
				MaxLength:   60,
				Description: "标题",
			},
			"recipient": {
				Type:        "string",
				Required:    false,
				MaxLength:   30,
				Description: "接收人",
			},
			"mobile": {
				Type:        "string",
				Required:    false,
				MaxLength:   11,
				Description: "手机号",
			},
			// "status": {
			// 	Type:        "number",
			// 	Required:    false,
			// 	Description: "短信状态,0-未发送,1-已发送",
			// 	Enum:        []string{"0", "1"},
			// },

			"start_time": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$",
				Description: "开始时间范围 (格式: YYYY-MM-DD HH:MM:SS)",
			},
			"end_time": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$",
				Description: "结束时间范围 (格式: YYYY-MM-DD HH:MM:SS)",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "页码",
				Default:     1,
			},

			"page_size": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Max:         &[]float64{100}[0],
				Description: "每页数量",
				Default:     20,
			},
		},
	}
}

func GetOperateLogsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "操作日志查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"operator": {
				Type:        "string",
				Required:    false,
				MaxLength:   30,
				Description: "操作人",
			},
			"operand": {
				Type:        "string",
				Required:    false,
				MaxLength:   30,
				Description: "操作对象（名字、电话号码、身份证）",
			},
			"operate_name": {
				Type:        "string",
				Required:    false,
				MaxLength:   30,
				Description: "操作类型",
			},
			"start_time": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$",
				Description: "开始时间范围 (格式: YYYY-MM-DD HH:MM:SS)",
			},
			"end_time": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$",
				Description: "结束时间范围 (格式: YYYY-MM-DD HH:MM:SS)",
			},
			"page": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Description: "页码",
				Default:     1,
			},
			"page_size": {
				Type:        "number",
				Required:    false,
				Min:         &[]float64{1}[0],
				Max:         &[]float64{100}[0],
				Description: "每页数量",
				Default:     20,
			},
		},
	}
}
