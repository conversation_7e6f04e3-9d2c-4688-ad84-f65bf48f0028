package payment

import (
	"context"
	"encoding/json"
	"errors"
	businessorder "fincore/app/business/order"
	businessrepayment "fincore/app/business/repayment"
	"fincore/app/dianziqian/utils"
	"fincore/model"
	"fincore/thirdparty/payment/sumpay"
	"fincore/utils/convert"
	"fincore/utils/decimal"
	"fincore/utils/gform"
	"fincore/utils/lock"
	"fincore/utils/log"
	repaymentUtils "fincore/utils/repayment"
	"fincore/utils/shopspringutils"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-module/carbon/v2"
)

// 代付回调状态常量
const (
	DisbursementCallbackStatusSuccess    = "00" // 成功
	DisbursementCallbackStatusProcessing = "01" // 处理中
	DisbursementCallbackStatusFailed     = "03" // 失败
)

// 代付回调响应码常量
const (
	CallbackResponseCodeSuccess = "000000" // 成功
	CallbackResponseCodeFailed  = "000001" // 失败
)

// PaymentService 支付服务
type PaymentService struct {
	transactionModel *model.BusinessPaymentTransactionsService
	billModel        *model.BusinessRepaymentBillsService
	orderModel       *model.BusinessLoanOrdersService
	repository       *PaymentRepository
	logger           *log.Logger
	ctx              context.Context
}

// PaymentServiceOption 支付服务选项函数类型
type PaymentServiceOption func(*PaymentService)

// WithLogger 设置支付服务日志管理器
func WithLogger(logger *log.Logger) PaymentServiceOption {
	return func(service *PaymentService) {
		service.logger = logger
	}
}

func WithBillModel() PaymentServiceOption {
	return func(service *PaymentService) {
		service.billModel = model.NewBusinessRepaymentBillsService(service.ctx)
	}
}

func WithOrderModel() PaymentServiceOption {
	return func(service *PaymentService) {
		service.orderModel = model.NewBusinessLoanOrdersService(service.ctx)
	}
}

func WithTransactionModel() PaymentServiceOption {
	return func(service *PaymentService) {
		service.transactionModel = model.NewBusinessPaymentTransactionsService(service.ctx)
	}
}

func WithRepository() PaymentServiceOption {
	return func(service *PaymentService) {
		service.repository = NewPaymentRepository(service.ctx)
	}
}

// NewPaymentServiceWithOptions 创建支付服务实例，支持选项模式
func NewPaymentServiceWithOptions(ctx context.Context, options ...PaymentServiceOption) *PaymentService {
	service := &PaymentService{
		ctx:    ctx,
		logger: log.Payment().WithContext(ctx),
	}
	for _, option := range options {
		option(service)
	}
	return service
}

// HandleDisbursementCallback 处理代付回调通知
func (s *PaymentService) HandleDisbursementCallback(req *DisbursementCallbackRequest) (*DisbursementCallbackResponse, error) {
	var eStr string
	// 1. 根据交易流水号查询交易记录
	transaction, err := s.transactionModel.GetTransactionByChannelTransactionNo(req.TraceNo)
	if err != nil {
		eStr = fmt.Sprintf("查询交易记录失败: %v", err)
		s.logger.Error(eStr)
		return &DisbursementCallbackResponse{
			RespCode: CallbackResponseCodeFailed,
			RespMsg:  eStr,
		}, err
	}

	// 2. 检查交易记录状态是否允许更新
	if transaction.Status == model.TransactionStatusSuccess || transaction.Status == model.TransactionStatusFailed {
		// 已经是最终状态，直接返回成功
		return &DisbursementCallbackResponse{
			RespCode: CallbackResponseCodeSuccess,
			RespMsg:  "处理成功",
		}, nil
	}

	// 3. 映射回调状态到内部状态
	internalStatus, err := s.mapCallbackStatusToInternal(req.Status)
	if err != nil {
		eStr = fmt.Sprintf("无效的回调状态: %s", req.Status)
		s.logger.Error(eStr)
		return &DisbursementCallbackResponse{
			RespCode: CallbackResponseCodeFailed,
			RespMsg:  eStr,
		}, err
	}

	// 4. 使用事务更新交易记录和订单状态
	err = model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		updateMap := map[string]interface{}{
			"status":                 internalStatus,
			"channel_transaction_no": req.TraceNo,
		}

		if internalStatus == model.TransactionStatusFailed {
			updateMap["error_code"] = req.RespCode
			updateMap["error_message"] = req.RespMsg
		}

		resultJson, err := utils.StructToJson(req)
		if err != nil {
			eStr = fmt.Sprintf("转换结果失败: %v", err)
			s.logger.Error(eStr)
			return errors.New(eStr)
		}
		updateMap["callback_result"] = resultJson

		// 更新交易记录状态
		updateErr := s.transactionModel.UpdateTransactionStatus(
			tx,
			model.UpdateTransactionStatusResultWhere{
				ChannelTransactionNo: req.TraceNo,
			},
			updateMap,
		)
		if updateErr != nil {
			eStr = fmt.Sprintf("更新交易记录失败: %v", updateErr)
			s.logger.Error(eStr)
			return errors.New(eStr)
		}

		// 如果是成功状态，更新订单状态为放款中
		if req.Status == DisbursementCallbackStatusSuccess {
			orderUpdateErr := s.orderModel.UpdateOrderStatus(tx, transaction.OrderID, model.OrderStatusDisbursed, nil)
			if orderUpdateErr != nil {
				eStr = fmt.Sprintf("更新订单状态失败: %v", orderUpdateErr)
				s.logger.Error(eStr)
				return errors.New(eStr)
			}
		}

		return nil
	})

	if err != nil {
		eStr = fmt.Sprintf("处理失败: %v", err)
		s.logger.Error(eStr)
		return &DisbursementCallbackResponse{
			RespCode: CallbackResponseCodeFailed,
			RespMsg:  eStr,
		}, err
	}

	return &DisbursementCallbackResponse{
		RespCode: CallbackResponseCodeSuccess,
		RespMsg:  "处理成功",
	}, nil
}

// mapCallbackStatusToInternal 映射回调状态到内部状态
func (s *PaymentService) mapCallbackStatusToInternal(callbackStatus string) (int, error) {
	switch callbackStatus {
	case DisbursementCallbackStatusSuccess:
		return model.TransactionStatusSuccess, nil
	case DisbursementCallbackStatusProcessing:
		return model.TransactionStatusSubmitted, nil
	case DisbursementCallbackStatusFailed:
		return model.TransactionStatusFailed, nil
	default:
		return 0, fmt.Errorf("未知的回调状态: %s", callbackStatus)
	}
}

// HandlePaymentCallback 处理支付回调通知
func (s *PaymentService) HandlePaymentCallback(req *PaymentCallbackRequest) (err error) {
	var eStr string

	// 分解 order_no 获取订单编号
	_, transactionNo, _, err := businessrepayment.ParsePayOrderNoFields(req.OrderNo)
	if err != nil {
		eStr = fmt.Sprintf("解析订单号失败: %v", err)
		s.logger.Error(eStr)
		return err
	}

	repaymentService := businessrepayment.NewPaymentService(s.ctx)

	_, err = repaymentService.QueryPaymentStatus(transactionNo)
	if err != nil {
		eStr = fmt.Sprintf("查询支付状态失败: %v", err)
		s.logger.Error(eStr)
		return err
	}

	return
}

// ProcessRefund 处理客户退款
func (s *PaymentService) ProcessRefund(req *RefundRequest, operatorID int, operatorName string) (*RefundResponse, error) {
	var eStr string
	// 1. 参数验证
	if req.TransactionID <= 0 {
		eStr = "流水记录ID无效"
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}
	if req.RefundAmount <= 0 {
		eStr = "退款金额必须大于0"
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	// 2. 使用流水ID加锁，防止并发退款
	lockKey := fmt.Sprintf("refund_transaction_%d", req.TransactionID)
	refundLock := lock.GetLock(lockKey, 5*time.Minute)
	refundLock.Lock()
	defer refundLock.Unlock()

	// 3. 查询原始流水记录
	originalTransaction, err := s.repository.GetTransactionByID(req.TransactionID)
	if err != nil {
		eStr = fmt.Sprintf("查询流水记录失败: %v", err)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	if originalTransaction == nil {
		eStr = fmt.Sprintf("退款流水记录不存在: %d", req.TransactionID)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	// 4. 验证流水状态
	if originalTransaction.Status != model.TransactionStatusSuccess {
		eStr = fmt.Sprintf("只能对处理成功的流水进行退款，当前状态: %d", originalTransaction.Status)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	// 6. 查询关联账单
	if originalTransaction.BillID == nil {
		eStr = "账单ID为空"
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	bill, err := s.repository.GetBillByID(*originalTransaction.BillID)
	if err != nil {
		eStr = fmt.Sprintf("查询关联账单失败: %v", err)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	if bill == nil {
		eStr = "账单不存在"
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	// 历史提交退款记录
	refundTransactions, err := s.repository.GetTransactionByRelatedTransactionNo(model.TransactionCondition{
		RelatedTransactionNo: originalTransaction.TransactionNo,
		Status:               []any{model.TransactionStatusSubmitted, model.TransactionStatusSuccess},
	})
	if err != nil {
		eStr = fmt.Sprintf("查询退款流水记录失败: %v", err)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	// 已退款金额
	var refundAmount float64
	if len(refundTransactions) > 0 {
		for _, refundTransaction := range refundTransactions {
			refundAmount += float64(refundTransaction.Amount)
		}
	}

	// 5. 验证退款金额不能超过原交易金额 - 已退款的金额
	if req.RefundAmount > float64(originalTransaction.Amount)-refundAmount {
		eStr = fmt.Sprintf("累计提交的退款金额超过原交易金额: %.2f", originalTransaction.Amount)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	// 8. 生成退款流水号和退款订单号
	refundTransactionNo := GenerateRefundTransactionNo()

	// 8. 创建退款流水记录
	refundTransaction := &model.BusinessPaymentTransactions{
		TransactionNo:        refundTransactionNo,
		RelatedTransactionNo: &originalTransaction.TransactionNo,
		OrderID:              originalTransaction.OrderID,
		OrderNo:              originalTransaction.OrderNo,
		BillID:               originalTransaction.BillID,
		UserID:               originalTransaction.UserID,
		PaymentChannelID:     originalTransaction.PaymentChannelID,
		Type:                 model.TransactionTypeRefund,
		WithholdType:         originalTransaction.WithholdType,
		Amount:               model.Decimal(req.RefundAmount),
		Status:               model.TransactionStatusPending,
		Remark:               &req.RefundReason, // 直接设置退款原因
	}

	err = s.repository.CreateTransaction(refundTransaction)
	if err != nil {
		eStr = fmt.Sprintf("创建退款流水失败: %v", err)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	// 9. 调用第三方退款接口
	paymentService, err := sumpay.NewSumpayService(sumpay.WithContext(s.ctx))
	if err != nil {
		// 更新退款流水状态为失败
		s.updateRefundTransactionStatus(refundTransactionNo, model.TransactionStatusFailed, fmt.Sprintf("获取支付服务失败: %v", err), "")
		eStr = fmt.Sprintf("获取支付服务失败: %v", err)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	// 构建退款请求
	merConfig, err := businessrepayment.GetMerNoByWithholdType(*originalTransaction.WithholdType)
	if err != nil {
		eStr = fmt.Sprintf("获取商户配置失败: %v", err)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	refundRequest := sumpay.NewRefundRequest(
		refundTransactionNo,
		originalTransaction.ThirdPartyOrderNo,
		fmt.Sprintf("%.2f", req.RefundAmount),
		"",
		merConfig,
	)

	// 调用退款接口
	result, err := paymentService.Refund(refundRequest)
	if err != nil {
		// 更新退款流水状态为失败
		s.updateRefundTransactionStatus(refundTransactionNo, model.TransactionStatusFailed, fmt.Sprintf("调用退款接口失败: %v", err), "")
		eStr = fmt.Sprintf("调用退款接口失败: %v", err)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	// 10. 处理第三方响应
	var status string
	var message string

	resultJson, err := utils.StructToJson(result)
	if err != nil {
		eStr = fmt.Sprintf("转换结果失败: %v", err)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	if respCode, ok := result["resp_code"].(string); ok {
		if respCode == sumpay.RespCodeSuccess {

			type SumpayRefundResponse struct {
				MerNo       string `json:"mer_no"`
				RefundNo    string `json:"refund_no"`
				RefundTime  string `json:"refund_time"` // 退款时间 YYYYMMDDHHMMSS
				Status      string `json:"status"`
				RefSerialNo string `json:"ref_serial_no"`
				RefundAmt   string `json:"trade_amt"`
				SuccessTime string `json:"success_time"`
			}

			var sumpayRefundResponse SumpayRefundResponse
			err = gconv.Struct(result["sumpay_refund_response"], &sumpayRefundResponse)
			if err != nil {
				eStr = fmt.Sprintf("转换结果失败: %v", err)
				s.logger.Error(eStr)
				return nil, errors.New(eStr)
			}

			// 退款请求提交成功，使用事务确保数据一致性
			err = model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
				// 更新退款流水状态
				updateMap := map[string]interface{}{
					"status":                 model.TransactionStatusSubmitted,
					"channel_transaction_no": sumpayRefundResponse.RefSerialNo,
					"callback_result":        resultJson,
				}

				err := s.transactionModel.UpdateTransactionStatus(tx, model.UpdateTransactionStatusResultWhere{
					TransactionNo: refundTransactionNo,
				}, updateMap)
				if err != nil {
					return fmt.Errorf("更新退款流水状态失败: %v", err)
				}

				// 累加账单的累计退款金额
				err = s.repository.AddBillRefundAmountWithTx(tx, *originalTransaction.BillID, req.RefundAmount)
				if err != nil {
					return fmt.Errorf("累加账单退款金额失败: %v", err)
				}

				return nil
			})

			if err != nil {
				eStr = fmt.Sprintf("退款提交成功后更新数据失败: %v", err)
				s.logger.Error(eStr)
				return nil, errors.New(eStr)
			}

			status = "submitted"
			message = "退款请求已提交"

			// todo 第三方退款流程处理比较慢，放到定时任务同步
			// 异步刷新退款状态
			// go func(refundID int) {
			// 	defer func() {
			// 		if r := recover(); r != nil {
			// 			s.logger.WithFields(
			// 				log.Any("refund_id", refundID),
			// 				log.Any("panic_info", r),
			// 			).Error("异步刷新退款状态发生 panic")
			// 		}
			// 	}()

			// 	// 第三方处理比较慢，等待30秒
			// 	time.Sleep(30 * time.Second)

			// 	// 创建执行阶段的超时控制
			// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			// 	defer cancel()

			// 	done := make(chan error, 1)
			// 	go func() {
			// 		done <- s.QueryRefundStatus(refundID)
			// 	}()

			// 	select {
			// 	case err := <-done:
			// 		// 正常完成
			// 		if err != nil {
			// 			s.logger.WithFields(
			// 				log.Any("refund_id", fmt.Sprintf("%d", refundID)),
			// 				log.ErrorField(err),
			// 			).Error("异步刷新退款状态失败")
			// 		} else {
			// 			s.logger.WithFields(
			// 				log.Any("refund_id", fmt.Sprintf("%d", refundID)),
			// 			).Info("异步刷新退款状态成功")
			// 		}
			// 	case <-ctx.Done():
			// 		// 处理超时
			// 		s.logger.WithFields(
			// 			log.Any("refund_id", fmt.Sprintf("%d", refundID)),
			// 			log.String("reason", "execution_timeout"),
			// 			log.Duration("timeout_duration", 5*time.Second),
			// 		).Warn("刷新退款状态执行超时")
			// 		return
			// 	}
			// }(refundTransaction.ID)

		} else {
			// 退款请求失败
			status = "failed"
			if respMsg, msgOk := result["resp_msg"].(string); msgOk {
				message = fmt.Sprintf("退款失败: %s", respMsg)
			} else {
				message = "退款失败"
			}
			s.updateRefundTransactionStatus(refundTransactionNo, model.TransactionStatusFailed, message, resultJson)
		}
	} else {
		// 响应格式异常
		status = "failed"
		message = "第三方响应格式异常"
		s.updateRefundTransactionStatus(refundTransactionNo, model.TransactionStatusFailed, message, resultJson)
	}

	// 11. 记录操作日志
	logDetails := fmt.Sprintf("退款金额: %.2f, 原流水号: %s, 操作结果: %s", req.RefundAmount, originalTransaction.TransactionNo, message)
	err = s.repository.CreateOperationLog(originalTransaction.OrderID, operatorID, operatorName, "客户退款", logDetails)
	if err != nil {
		eStr = fmt.Sprintf("记录退款操作日志失败: %v", err)
		s.logger.Error(eStr)
		return nil, errors.New(eStr)
	}

	return &RefundResponse{
		RefundTransactionNo: refundTransactionNo,
		RefundAmount:        req.RefundAmount,
		Status:              status,
		Message:             message,
	}, nil
}

// updateRefundTransactionStatus 更新退款交易状态
func (s *PaymentService) updateRefundTransactionStatus(transactionNo string, status int, errorMsg string, reslutJson string) {
	updateMap := map[string]interface{}{
		"status":          status,
		"error_message":   errorMsg,
		"callback_result": reslutJson,
	}

	if status == model.TransactionStatusSubmitted || status == model.TransactionStatusSuccess {
		now := time.Now()
		updateMap["completed_at"] = &now
	}

	err := s.repository.UpdateTransactionStatus(
		model.UpdateTransactionStatusResultWhere{
			TransactionNo: transactionNo,
		},
		updateMap,
	)

	var eStr string
	if err != nil {
		eStr = fmt.Sprintf("更新退款交易状态失败: %v", err)
		s.logger.Error(eStr)
	}
}

// QueryRefundStatus 查询退款状态
func (s *PaymentService) QueryRefundStatus(refundTransactionID int) error {
	queryLock, err := lock.GetRefundRefreshLock(refundTransactionID, s.logger).Lock()
	if err != nil {
		return fmt.Errorf("获取退款刷新锁失败: %v", err)
	}
	defer queryLock.Unlock()

	// 1. 查询退款流水记录
	transaction, err := s.repository.GetTransactionByID(refundTransactionID)
	var eStr string
	if err != nil {
		eStr = fmt.Sprintf("查询退款流水记录失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	if transaction == nil {
		eStr = fmt.Sprintf("退款流水记录不存在: %d", refundTransactionID)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	// 是否可刷新状态
	if transaction.Status != model.TransactionStatusSubmitted && transaction.Status != model.TransactionStatusFailed {
		eStr = fmt.Sprintf("当前状态为: %s, 不可刷新状态", model.TransactionStatusDescriptions[transaction.Status])
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	// 3. 调用第三方退款查询接口
	paymentService, err := sumpay.NewSumpayService(sumpay.WithContext(s.ctx))
	if err != nil {
		eStr = fmt.Sprintf("获取支付服务失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	// 构建退款查询请求
	merConf, err := businessrepayment.GetMerNoByWithholdType(*transaction.WithholdType)
	if err != nil {
		eStr = fmt.Sprintf("获取商户配置失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}
	refundSearchRequest := sumpay.NewRefundSearchRequest(transaction.TransactionNo, merConf)

	// 调用退款查询接口
	result, err := paymentService.RefundSearch(refundSearchRequest)
	if err != nil {
		eStr = fmt.Sprintf("调用退款查询接口失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	resultJson, err := utils.StructToJson(result)
	if err != nil {
		eStr = fmt.Sprintf("转换结果失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	// 4. 处理第三方响应
	respCode, ok := result["resp_code"].(string)
	if !ok {
		eStr = "第三方响应格式异常：缺少resp_code字段"
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	if respCode != sumpay.RespCodeSuccess {
		// 查询失败，更新流水状态为失败
		respMsg, _ := result["resp_msg"].(string)
		s.updateRefundTransactionStatus(transaction.TransactionNo, model.TransactionStatusFailed, fmt.Sprintf("退款查询失败: %s", respMsg), "")
		eStr = fmt.Sprintf("退款查询失败: %s", respMsg)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	// 5. 解析退款查询响应数据
	refundSearchResponseData, ok := result["sumpay_refund_search_response"].(string)
	if !ok {
		eStr = "第三方响应格式异常：缺少sumpay_refund_search_response字段"
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	refundSearchResponse := sumpay.SumpayRefundSearchResponse{}
	err = json.Unmarshal([]byte(refundSearchResponseData), &refundSearchResponse)
	if err != nil {
		eStr = fmt.Sprintf("解析退款查询响应数据失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	// 6. 根据退款状态进行相应处理
	switch refundSearchResponse.Status {
	case "1": // 成功
		return s.handleRefundSuccess(transaction, &refundSearchResponse, resultJson)
	case "0": // 失败
		return s.handleRefundFailure(transaction, resultJson)
	case "2": // 处理中
		// 处理中，不做任何操作
		s.logger.WithFields(
			log.String("transaction_no", transaction.TransactionNo),
		).Info("退款状态为处理中，不做任何操作")
		return nil
	default:
		eStr = fmt.Sprintf("未知的退款状态: %s", refundSearchResponse.Status)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}
}

// handleRefundSuccess 处理退款成功
func (s *PaymentService) handleRefundSuccess(
	transaction *model.BusinessPaymentTransactions,
	refundResponse *sumpay.SumpayRefundSearchResponse,
	reslultJson string) error {
	// 开启事务处理
	return model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		var eStr string
		var err error

		updateMap := map[string]interface{}{
			"status":                 model.TransactionStatusSuccess,
			"error_message":          "",
			"channel_transaction_no": refundResponse.RefSerialNo, // 关联退款流水号
			"callback_result":        reslultJson,
		}

		updateMap["completed_at"] = time.Now()
		amount := model.Decimal(gconv.Float64(refundResponse.TradeAmt))
		updateMap["amount"] = amount // 退款金额

		err = s.transactionModel.UpdateTransactionStatus(tx, model.UpdateTransactionStatusResultWhere{
			TransactionNo: transaction.TransactionNo,
		}, updateMap)
		if err != nil {
			eStr = fmt.Sprintf("更新退款流水状态失败: %v", err)
			s.logger.Error(eStr)
			return errors.New(eStr)
		}

		// 更新账单和订单前加锁
		statusLock, err := lock.GetBillOrderLock(*transaction.BillID, transaction.OrderID, s.logger).Lock()
		if err != nil {
			eStr = fmt.Sprintf("获取账单订单锁失败: %v", err)
			s.logger.Error(eStr)
			return errors.New(eStr)
		}
		defer statusLock.Unlock()

		// 2. 更新关联账单记录
		if transaction.BillID != nil {
			var originalAmount model.Decimal
			// 实际退款金额与提交的金额不一致
			if amount != transaction.Amount {
				originalAmount = transaction.Amount
			}
			err = s.updateBillForRefund(tx, *transaction.BillID, amount, originalAmount)
			if err != nil {
				eStr = fmt.Sprintf("更新账单记录失败: %v", err)
				s.logger.Error(eStr)
				return errors.New(eStr)
			}
		}

		// 3. 更新关联订单状态
		err = s.updateOrderForRefund(tx, transaction.OrderID, decimal.Decimal(amount))
		if err != nil {
			eStr = fmt.Sprintf("更新订单状态失败: %v", err)
			s.logger.Error(eStr)
			return errors.New(eStr)
		}

		return nil
	})
}

// handleRefundFailure 处理退款失败
func (s *PaymentService) handleRefundFailure(transaction *model.BusinessPaymentTransactions, reslultJson string) error {
	return model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		// 更新退款流水状态为失败
		updateMap := map[string]interface{}{
			"status":          model.TransactionStatusFailed,
			"error_message":   "退款失败",
			"callback_result": reslultJson,
		}

		err := s.transactionModel.UpdateTransactionStatus(tx, model.UpdateTransactionStatusResultWhere{
			TransactionNo: transaction.TransactionNo,
		}, updateMap)
		if err != nil {
			return fmt.Errorf("更新退款流水状态失败: %v", err)
		}

		// 退款失败时，撤回之前累加的退款金额
		if transaction.BillID != nil {
			refundAmountFloat := float64(transaction.Amount) // 单位本来就是元
			err := s.repository.SubtractBillRefundAmountWithTx(tx, *transaction.BillID, refundAmountFloat)
			if err != nil {
				return fmt.Errorf("撤回账单退款金额失败: %v", err)
			}
		}

		return nil
	})
}

// updateBillForRefund 更新账单记录（退款成功时）
func (s *PaymentService) updateBillForRefund(tx gform.IOrm, billID int, refundAmount model.Decimal, originalAmount model.Decimal) error {
	var eStr string
	// 获取账单信息
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		eStr = fmt.Sprintf("获取账单失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	// 计算更新后的已付金额
	newPaidAmount := bill.PaidAmount - refundAmount
	if newPaidAmount < 0 {
		newPaidAmount = 0
	}

	updateMap := map[string]interface{}{
		"paid_amount": newPaidAmount,
	}

	// 如果传入 originalAmount 大于0 要重新计算累计退款金额
	// 累计退款金额 = 账单累计退款金额 - originalAmount + 本次退款金额
	if originalAmount > 0 {
		totalRefundAmount := bill.TotalRefundAmount - originalAmount + refundAmount
		updateMap["total_refund_amount"] = totalRefundAmount
	}
	// 需要考虑是否已逾期
	now := time.Now()
	// 根据退款金额判断账单状态
	if newPaidAmount == 0 {
		// 已付金额为 0，状态变更为待支付
		if bill.DueDate.Before(now) {
			updateMap["status"] = model.RepaymentBillStatusOverdueUnpaid // 逾期待支付
		} else {
			updateMap["status"] = model.RepaymentBillStatusUnpaid // 待支付
		}

	} else {
		if bill.DueDate.Before(now) {
			updateMap["status"] = model.RepaymentBillStatusOverduePartialPaid // 逾期部分支付
		} else {
			updateMap["status"] = model.RepaymentBillStatusPartialPaid // 部分支付
		}
	}

	// 清理结清时间
	updateMap["paid_at"] = (*time.Time)(nil)

	// 更新账单
	_, err = s.billModel.UpdateBill(tx, model.UpdateBillResultWhere{
		ID: billID,
	}, updateMap)
	if err != nil {
		eStr = fmt.Sprintf("更新账单失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	// 重新占用退款流水所属账单的本金额度
	order, err := s.orderModel.GetOrderByID(tx, bill.OrderID)
	if err != nil {
		return fmt.Errorf("获取订单失败: %v", err)
	}
	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	err = businessorder.NewRepository(s.ctx).UpdateUserRemainingAmount(businessorder.UpdateUserRemainingAmountParams{
		Tx:           tx,
		Order:        order,
		LoanAmount:   float64(bill.DuePrincipal),
		IsWithdraw:   false,
		OnlyWithdraw: false,
	})
	if err != nil {
		return fmt.Errorf("更新用户剩余额度失败: %v", err)
	}

	return nil
}

// updateOrderForRefund 更新订单状态（退款成功时）
func (s *PaymentService) updateOrderForRefund(tx gform.IOrm, orderID int, refundAmount decimal.Decimal) error {
	var eStr string
	// 获取订单信息
	order, err := s.orderModel.GetOrderByID(tx, orderID)
	if err != nil {
		eStr = fmt.Sprintf("获取订单失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	// 计算更新后的已付总额
	newAmountPaid := order.AmountPaid - refundAmount
	if newAmountPaid < 0 {
		newAmountPaid = 0
	}

	orderUpdateDataMap := map[string]interface{}{
		"amount_paid": newAmountPaid,
	}

	// 如果订单状态已经是交易完成，需要变更为放款中
	if order.Status == model.OrderStatusCompleted {
		orderUpdateDataMap["status"] = model.OrderStatusDisbursed
		// 清理完成时间
		orderUpdateDataMap["completed_at"] = (*time.Time)(nil)
	}

	// 更新订单
	err = s.orderModel.UpdateOrder(tx, model.UpdateOrderCondition{
		ID: orderID,
	}, orderUpdateDataMap)
	if err != nil {
		eStr = fmt.Sprintf("更新订单失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}
	return nil
}

// 退款回调状态常量
const (
	RefundCallbackStatusSuccess    = "1" // 成功
	RefundCallbackStatusProcessing = "2" // 处理中
	RefundCallbackStatusFailed     = "0" // 失败
)

// HandleRefundCallback 处理退款通知回调
func (s *PaymentService) HandleRefundCallback(req *RefundCallbackRequest) (*RefundCallbackResponse, error) {
	var eStr string

	// 1. 根据退款流水号查询退款交易记录
	transaction, err := s.repository.GetTransactionByNo(req.RefundNo)
	if err != nil {
		eStr = fmt.Sprintf("查询退款交易记录失败: %v", err)
		s.logger.Error(eStr)
		return &RefundCallbackResponse{
			RespCode: CallbackResponseCodeFailed,
			RespMsg:  eStr,
		}, err
	}

	// 2. 验证交易记录类型
	if transaction.Type != model.TransactionTypeRefund {
		eStr = fmt.Sprintf("交易记录类型不匹配，期望: %s, 实际: %s", model.TransactionTypeRefund, transaction.Type)
		s.logger.Error(eStr)
		return &RefundCallbackResponse{
			RespCode: CallbackResponseCodeFailed,
			RespMsg:  eStr,
		}, errors.New(eStr)
	}

	// 3. 检查交易记录状态是否允许更新
	if transaction.Status == model.TransactionStatusSuccess || transaction.Status == model.TransactionStatusFailed {
		// 已经是最终状态，直接返回成功（幂等性处理）
		s.logger.WithFields(
			log.String("refund_no", req.RefundNo),
			log.String("serial_no", req.SerialNo),
			log.Int("current_status", transaction.Status),
		).Info("退款交易已处于最终状态，跳过处理")

		return &RefundCallbackResponse{
			RespCode: CallbackResponseCodeSuccess,
			RespMsg:  "处理成功",
		}, nil
	}

	// 4. 根据回调响应码判断处理逻辑
	if req.RespCode != CallbackResponseCodeSuccess {
		// 回调失败，更新交易状态为失败
		s.logger.WithFields(
			log.String("refund_no", req.RefundNo),
			log.String("serial_no", req.SerialNo),
			log.String("resp_code", req.RespCode),
			log.String("resp_msg", req.RespMsg),
		).Info("退款回调失败")

		err = s.updateRefundCallbackTransactionStatus(req.SerialNo, model.TransactionStatusFailed, req.RespMsg, req)
		if err != nil {
			eStr = fmt.Sprintf("更新退款交易状态失败: %v", err)
			s.logger.Error(eStr)
			return &RefundCallbackResponse{
				RespCode: CallbackResponseCodeFailed,
				RespMsg:  eStr,
			}, err
		}

		return &RefundCallbackResponse{
			RespCode: CallbackResponseCodeSuccess,
			RespMsg:  "处理成功",
		}, nil
	}

	// 5. 根据退款状态进行相应处理
	switch req.Status {
	case RefundCallbackStatusSuccess: // 成功
		return s.handleRefundCallbackSuccess(transaction, req)
	case RefundCallbackStatusFailed: // 失败
		return s.handleRefundCallbackFailure(transaction, req)
	case RefundCallbackStatusProcessing: // 处理中
		// 处理中，更新状态但不做业务逻辑处理
		err = s.updateRefundCallbackTransactionStatus(req.SerialNo, model.TransactionStatusSubmitted, "退款处理中", req)
		if err != nil {
			eStr = fmt.Sprintf("更新退款交易状态失败: %v", err)
			s.logger.Error(eStr)
			return &RefundCallbackResponse{
				RespCode: CallbackResponseCodeFailed,
				RespMsg:  eStr,
			}, err
		}
		return &RefundCallbackResponse{
			RespCode: CallbackResponseCodeSuccess,
			RespMsg:  "处理成功",
		}, nil
	default:
		eStr = fmt.Sprintf("未知的退款状态: %s", req.Status)
		s.logger.Error(eStr)
		return &RefundCallbackResponse{
			RespCode: CallbackResponseCodeFailed,
			RespMsg:  eStr,
		}, errors.New(eStr)
	}
}

// handleRefundCallbackSuccess 处理退款回调成功
func (s *PaymentService) handleRefundCallbackSuccess(
	transaction *model.BusinessPaymentTransactions,
	req *RefundCallbackRequest) (*RefundCallbackResponse, error) {

	// 开启事务处理
	err := model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		var eStr string
		var err error

		// 1. 更新退款流水状态
		updateMap := map[string]interface{}{
			"status":                 model.TransactionStatusSuccess,
			"error_message":          "",
			"channel_transaction_no": req.SerialNo, // 退款流水号
		}

		// 解析退款时间和金额
		if req.SuccessTime != "" {
			successTime := convert.ConvertToTime(req.SuccessTime)
			updateMap["completed_at"] = &successTime
		}

		var amountChange bool

		if req.SuccessAmount != "" {
			amount := convert.ConvertToFloat(req.SuccessAmount)
			amountDecimal := model.Decimal(*amount)
			if amountDecimal != transaction.Amount {
				// 实际退款与提交不一致
				amountChange = true
				updateMap["amount"] = amountDecimal
			}
		}

		// 序列化回调结果
		callbackResultJson, _ := json.Marshal(req)
		updateMap["callback_result"] = string(callbackResultJson)

		err = s.transactionModel.UpdateTransactionStatus(tx, model.UpdateTransactionStatusResultWhere{
			TransactionNo: transaction.TransactionNo,
		}, updateMap)
		if err != nil {
			eStr = fmt.Sprintf("更新退款流水状态失败: %v", err)
			s.logger.Error(eStr)
			return errors.New(eStr)
		}

		// 2. 更新关联账单记录
		if transaction.BillID != nil {
			if req.SuccessAmount != "" {
				amount := convert.ConvertToFloat(req.SuccessAmount)
				refundAmount := model.Decimal(*amount)
				if refundAmount > 0 {
					var originalAmount model.Decimal
					if amountChange {
						originalAmount = transaction.Amount
					}
					err = s.updateBillForRefund(tx, *transaction.BillID, refundAmount, originalAmount)
					if err != nil {
						eStr = fmt.Sprintf("更新账单记录失败: %v", err)
						s.logger.Error(eStr)
						return errors.New(eStr)
					}
				}
			}
		}

		// 3. 更新关联订单状态
		refundAmount := decimal.Decimal(0)
		if req.SuccessAmount != "" {
			amount := convert.ConvertToFloat(req.SuccessAmount)
			if amount != nil {
				refundAmount = decimal.Decimal(*amount)
			}
		}

		err = s.updateOrderForRefund(tx, transaction.OrderID, refundAmount)
		if err != nil {
			eStr = fmt.Sprintf("更新订单状态失败: %v", err)
			s.logger.Error(eStr)
			return errors.New(eStr)
		}

		return nil
	})

	if err != nil {
		return &RefundCallbackResponse{
			RespCode: CallbackResponseCodeFailed,
			RespMsg:  fmt.Sprintf("处理退款成功回调失败: %v", err),
		}, err
	}

	s.logger.WithFields(
		log.String("refund_no", req.RefundNo),
		log.String("serial_no", req.SerialNo),
		log.String("success_amount", req.SuccessAmount),
	).Info("退款回调成功处理完成")

	return &RefundCallbackResponse{
		RespCode: CallbackResponseCodeSuccess,
		RespMsg:  "处理成功",
	}, nil
}

// handleRefundCallbackFailure 处理退款回调失败
func (s *PaymentService) handleRefundCallbackFailure(
	transaction *model.BusinessPaymentTransactions,
	req *RefundCallbackRequest) (*RefundCallbackResponse, error) {

	err := model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		// 更新退款流水状态为失败
		updateMap := map[string]interface{}{
			"status":        model.TransactionStatusFailed,
			"error_message": "退款失败",
		}

		// 序列化回调结果
		callbackResultJson, _ := json.Marshal(req)
		updateMap["callback_result"] = string(callbackResultJson)

		err := s.transactionModel.UpdateTransactionStatus(tx, model.UpdateTransactionStatusResultWhere{
			TransactionNo: req.SerialNo,
		}, updateMap)
		if err != nil {
			return fmt.Errorf("更新退款交易状态失败: %v", err)
		}

		// 退款失败时，撤回之前累加的退款金额
		if transaction.BillID != nil {
			refundAmountFloat := float64(transaction.Amount) // 单位本来就是元
			err := s.repository.SubtractBillRefundAmountWithTx(tx, *transaction.BillID, refundAmountFloat)
			if err != nil {
				return fmt.Errorf("撤回账单退款金额失败: %v", err)
			}
		}

		return nil
	})

	if err != nil {
		eStr := fmt.Sprintf("退款回调失败处理失败: %v", err)
		s.logger.Error(eStr)
		return &RefundCallbackResponse{
			RespCode: CallbackResponseCodeFailed,
			RespMsg:  eStr,
		}, err
	}

	s.logger.WithFields(
		log.String("refund_no", req.RefundNo),
		log.String("serial_no", req.SerialNo),
		log.String("resp_msg", req.RespMsg),
	).Info("退款回调失败处理完成")

	return &RefundCallbackResponse{
		RespCode: CallbackResponseCodeSuccess,
		RespMsg:  "处理成功",
	}, nil
}

// updateRefundCallbackTransactionStatus 更新退款回调交易状态
func (s *PaymentService) updateRefundCallbackTransactionStatus(transactionNo string, status int, errorMsg string, req *RefundCallbackRequest) error {
	updateMap := map[string]interface{}{
		"status":        status,
		"error_message": errorMsg,
	}

	// 序列化回调结果
	callbackResultJson, _ := json.Marshal(req)
	updateMap["callback_result"] = string(callbackResultJson)

	if status == model.TransactionStatusSubmitted || status == model.TransactionStatusSuccess {
		now := time.Now()
		updateMap["completed_at"] = now
	}

	// 如果是成功状态，设置渠道交易号
	if status == model.TransactionStatusSuccess && req.SerialNo != "" {
		updateMap["channel_transaction_no"] = req.SerialNo
	}

	err := s.repository.UpdateTransactionStatus(
		model.UpdateTransactionStatusResultWhere{
			TransactionNo: transactionNo,
		},
		updateMap,
	)

	if err != nil {
		eStr := fmt.Sprintf("更新退款交易状态失败: %v", err)
		s.logger.Error(eStr)
		return errors.New(eStr)
	}

	return nil
}

// ProcessPartialOfflinePayment 处理部分线下支付
func (s *PaymentService) ProcessPartialOfflinePayment(req *PartialOfflinePaymentRequest, operatorID int, operatorName string) (*PartialOfflinePaymentResponse, error) {
	s.logger.WithFields(
		log.Int("bill_id", req.BillID),
		log.Float64("amount", req.Amount),
		log.Int("operator_id", operatorID),
	).Info("开始处理部分线下支付")

	// 加锁
	lockKey := fmt.Sprintf("repayment:bill:%d", req.BillID)
	repaymentLock := lock.GetLock(lockKey, 5*time.Second)

	repaymentLock.Lock()
	defer repaymentLock.Unlock()

	// 1. 验证并获取账单信息
	bill, err := s.repository.GetBillByID(req.BillID)
	if err != nil {
		return nil, fmt.Errorf("查询账单失败: %v", err)
	}
	if bill == nil {
		return nil, fmt.Errorf("账单不存在")
	}

	// 2. 验证账单状态
	if bill.Status != model.RepaymentBillStatusUnpaid &&
		bill.Status != model.RepaymentBillStatusOverdueUnpaid &&
		bill.Status != model.RepaymentBillStatusPartialPaid {
		return nil, fmt.Errorf("账单状态不允许部分还款，当前状态: %d", bill.Status)
	}

	// 3. 计算已支付的金额
	guaranteePaidAmount, assetPaidAmount, err := shopspringutils.CalculateSubmittedAmounts(req.BillID)
	if err != nil {
		return nil, err
	}

	totalPaidAmount := shopspringutils.AddAmountsWithDecimal(guaranteePaidAmount, assetPaidAmount)
	// 验证支付金额,不能超过剩余应还金额
	totalRemainingAmount := shopspringutils.SubtractAmountsWithDecimal(float64(bill.TotalDueAmount), shopspringutils.AddAmountsWithDecimal(float64(bill.TotalWaiveAmount), totalPaidAmount))
	if req.Amount > totalRemainingAmount {
		return nil, fmt.Errorf("支付金额超过应还金额")
	}

	// 4. 获取订单信息
	order, err := s.repository.GetOrderByID(bill.OrderID)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}

	// 5. 查找支付渠道ID 填写 0
	paymentChannelID := 0

	// 6. 启动数据库事务
	var response *PartialOfflinePaymentResponse
	err = model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		// 7.1 计算担保费比例和金额
		if bill.TotalDueAmount <= 0 {
			return fmt.Errorf("账单当期应还总额为0，无法拆分")
		}

		guaranteeFeeRatio := shopspringutils.DivideAmountsWithDecimal(float64(bill.DueGuaranteeFee), float64(bill.TotalDueAmount))
		guaranteeAmount := shopspringutils.CeilToTwoDecimal(shopspringutils.MultiplyAmountsWithDecimal(req.Amount, guaranteeFeeRatio))
		assetAmount := shopspringutils.SubtractAmountsWithDecimal(req.Amount, guaranteeAmount)
		assetAmount = shopspringutils.CeilToTwoDecimal(assetAmount)

		now := time.Now()

		// 创建 WithholdType 变量
		guaranteeWithholdType := businessrepayment.WithholdTypeGuarantee
		assetWithholdType := businessrepayment.WithholdTypeAsset

		// 7.2 创建担保费流水记录
		if guaranteeAmount > 0 {
			guaranteeTransactionNo := sumpay.GeneratePaymentTransactionNo()
			guaranteeTransaction := &model.BusinessPaymentTransactions{
				TransactionNo:               guaranteeTransactionNo,
				OrderID:                     bill.OrderID,
				OrderNo:                     order.OrderNo,
				BillID:                      &req.BillID,
				UserID:                      bill.UserID,
				PaymentChannelID:            paymentChannelID,
				Type:                        model.TransactionTypePartialOfflineRepayment,
				WithholdType:                &guaranteeWithholdType,
				Amount:                      model.Decimal(guaranteeAmount),
				Status:                      model.TransactionStatusSuccess,
				OfflinePaymentChannelDetail: &req.PaymentChannel,
				OfflinePaymentVoucher:       &req.Voucher,
				CreatedAt:                   now,
				CompletedAt:                 &now,
			}

			if err := s.transactionModel.CreateTransaction(guaranteeTransaction); err != nil {
				return fmt.Errorf("创建担保费流水记录失败: %v", err)
			}
		}

		// 7.3 创建资管费流水记录
		if assetAmount > 0 {
			assetTransactionNo := sumpay.GeneratePaymentTransactionNo()
			assetTransaction := &model.BusinessPaymentTransactions{
				TransactionNo:               assetTransactionNo,
				OrderID:                     bill.OrderID,
				OrderNo:                     order.OrderNo,
				BillID:                      &req.BillID,
				UserID:                      bill.UserID,
				PaymentChannelID:            paymentChannelID,
				Type:                        model.TransactionTypePartialOfflineRepayment,
				WithholdType:                &assetWithholdType,
				Amount:                      model.Decimal(assetAmount),
				Status:                      model.TransactionStatusSuccess,
				OfflinePaymentChannelDetail: &req.PaymentChannel,
				OfflinePaymentVoucher:       &req.Voucher,
				CreatedAt:                   now,
				CompletedAt:                 &now,
			}

			if err := s.transactionModel.CreateTransaction(assetTransaction); err != nil {
				return fmt.Errorf("创建资管费流水记录失败: %v", err)
			}
		}

		// 7.2 更新账单信息
		newPaidAmount := shopspringutils.AddAmountsWithDecimal(float64(bill.PaidAmount), req.Amount)
		updateMap := map[string]interface{}{
			"paid_amount": newPaidAmount,
		}

		// 判断账单状态
		var billEnd bool
		if newPaidAmount >= shopspringutils.SubtractAmountsWithDecimal(float64(bill.TotalDueAmount), float64(bill.TotalWaiveAmount)) {
			billEnd = true
			// 全额还清
			updateMap["paid_at"] = &now

			dueTime := carbon.CreateFromStdTime(bill.DueDate).EndOfDay().StdTime()
			if time.Now().After(dueTime) {
				updateMap["status"] = model.RepaymentBillStatusOverduePaid // 逾期已支付
			} else {
				updateMap["status"] = model.RepaymentBillStatusPaid // 已支付
			}
		} else {
			// 部分还款
			if bill.DueDate.Before(time.Now()) {
				updateMap["status"] = model.RepaymentBillStatusOverduePartialPaid // 逾期部分支付
			} else {
				updateMap["status"] = model.RepaymentBillStatusPartialPaid // 部分支付
			}
		}

		_, err := s.billModel.UpdateBill(tx, model.UpdateBillResultWhere{
			ID: req.BillID,
		}, updateMap)
		if err != nil {
			return fmt.Errorf("更新账单失败: %v", err)
		}

		if billEnd {
			// 查询订单
			order, err := s.repository.GetOrderByID(bill.OrderID)
			if err != nil {
				return fmt.Errorf("查询订单失败: %v", err)
			}
			if order == nil {
				return fmt.Errorf("订单不存在")
			}
			// 更新剩余额度，撤回占用
			err = businessorder.NewRepository(s.ctx).UpdateUserRemainingAmount(businessorder.UpdateUserRemainingAmountParams{
				Tx:           tx,
				Order:        order,
				LoanAmount:   float64(bill.DuePrincipal),
				IsWithdraw:   true,
				OnlyWithdraw: false,
			})
			if err != nil {
				return fmt.Errorf("更新用户剩余额度失败: %v", err)
			}

		}

		// 7.3 更新订单已还金额
		newOrderPaidAmount := order.AmountPaid + decimal.Decimal(req.Amount)
		orderUpdateDataMap := map[string]interface{}{
			"amount_paid": newOrderPaidAmount,
		}

		// 检查是否需要更新订单状态为已结清，该订单下所有账单都为已结清状态
		allPaid := true
		bills, err := s.billModel.GetBillsByOrderID(tx, bill.OrderID)
		if err != nil {
			return fmt.Errorf("查询账单失败: %v", err)
		}
		for _, bill := range bills {
			if bill.Status != model.RepaymentBillStatusPaid &&
				bill.Status != model.RepaymentBillStatusOverduePaid &&
				bill.Status != model.RepaymentBillStatusEarlySettlement &&
				bill.Status != model.RepaymentBillStatusSettled {
				allPaid = false
				break
			}
		}

		if allPaid {
			orderUpdateDataMap["status"] = model.OrderStatusCompleted
			orderUpdateDataMap["completed_at"] = &now
		}

		err = s.orderModel.UpdateOrder(tx, model.UpdateOrderCondition{
			ID: bill.OrderID,
		}, orderUpdateDataMap)

		if err != nil {
			return fmt.Errorf("更新订单失败: %v", err)
		}

		// 7.4 记录操作日志
		logDetails := fmt.Sprintf("部分线下支付: 金额%.2f元(担保费%.2f元,资管费%.2f元), 渠道:%s", req.Amount, guaranteeAmount, assetAmount, req.PaymentChannel)
		if err := s.repository.CreateOperationLog(bill.OrderID, operatorID, operatorName, "部分线下支付", logDetails); err != nil {
			s.logger.WithFields(log.String("error", err.Error())).Warn("记录操作日志失败")
		}

		// 构建响应
		response = &PartialOfflinePaymentResponse{
			BillID:  req.BillID,
			Amount:  req.Amount,
			Status:  "success",
			Message: "部分线下支付处理成功",
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 刷新风控数据
	err = businessorder.NewOrderServiceWithOptions(s.ctx, businessorder.WithRepository(), businessorder.WithOrderModel()).RefreshRiskData(bill.UserID)
	if err != nil {
		s.logger.WithFields(log.String("error", err.Error())).Warn("刷新风控数据失败")
		return nil, err
	}

	s.logger.WithFields(
		log.Int("bill_id", req.BillID),
	).Info("部分线下支付处理完成")
	return response, nil
}

// ProcessCancelOfflinePayment 处理销账取消
func (s *PaymentService) ProcessCancelOfflinePayment(req *CancelOfflinePaymentRequest, operatorID int, operatorName string) (*CancelOfflinePaymentResponse, error) {
	s.logger.WithFields(
		log.String("transaction_no", req.TransactionNo),
		log.Int("operator_id", operatorID),
	).Info("开始处理销账取消")

	// 1. 查询流水记录
	transaction, err := s.repository.GetTransactionByNo(req.TransactionNo)
	if err != nil {
		return nil, fmt.Errorf("查询流水失败: %v", err)
	}
	if transaction == nil {
		return nil, fmt.Errorf("流水记录不存在")
	}

	// 2. 验证流水状态和类型
	if transaction.Type != model.TransactionTypePartialOfflineRepayment {
		return nil, fmt.Errorf("只能取消部分线下支付流水")
	}

	if transaction.Status != model.TransactionStatusSuccess {
		return nil, fmt.Errorf("只能取消成功状态的流水")
	}

	// 3. 检查是否已被删除（通过重新查询验证）
	// 如果能查询到说明未被删除，因为查询方法已经过滤了deleted_at不为空的记录

	// 4. 验证必要字段
	if transaction.BillID == nil {
		return nil, fmt.Errorf("流水记录缺少账单ID")
	}

	// 5. 获取账单和订单信息
	bill, err := s.repository.GetBillByID(*transaction.BillID)
	if err != nil {
		return nil, fmt.Errorf("查询账单失败: %v", err)
	}
	if bill == nil {
		return nil, fmt.Errorf("账单不存在")
	}

	order, err := s.repository.GetOrderByID(transaction.OrderID)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}

	// 6. 加锁防止并发
	lockKey := fmt.Sprintf("repayment:bill:%d", *transaction.BillID)
	repaymentLock := lock.GetLock(lockKey, 5*time.Second)
	repaymentLock.Lock()
	defer repaymentLock.Unlock()

	// 7. 事务处理
	var response *CancelOfflinePaymentResponse
	err = model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		now := time.Now()

		// 7.1 软删除流水记录
		err := s.repository.SoftDeleteTransaction(tx, req.TransactionNo, now)
		if err != nil {
			return fmt.Errorf("删除流水记录失败: %v", err)
		}

		// 7.2 逆向更新账单信息
		err = s.reverseBillUpdate(tx, bill, transaction.Amount)
		if err != nil {
			return fmt.Errorf("更新账单失败: %v", err)
		}

		// 7.3 逆向更新订单信息
		err = s.reverseOrderUpdate(tx, order, decimal.Decimal(transaction.Amount))
		if err != nil {
			return fmt.Errorf("更新订单失败: %v", err)
		}

		// 7.4 记录操作日志
		withholdTypeStr := ""
		if transaction.WithholdType != nil {
			withholdTypeStr = *transaction.WithholdType
		}
		logDetails := fmt.Sprintf("销账取消: 流水号%s, 金额%.2f元, 类型:%s",
			req.TransactionNo, float64(transaction.Amount), withholdTypeStr)
		err = s.repository.CreateOperationLog(transaction.OrderID, operatorID, operatorName, "销账取消", logDetails)
		if err != nil {
			s.logger.WithFields(log.String("error", err.Error())).Warn("记录操作日志失败")
		}

		// 构建响应
		response = &CancelOfflinePaymentResponse{
			TransactionNo: req.TransactionNo,
			BillID:        *transaction.BillID,
			Amount:        float64(transaction.Amount),
			Status:        "success",
			Message:       "销账取消处理成功",
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	s.logger.WithFields(
		log.String("transaction_no", req.TransactionNo),
		log.Int("bill_id", *transaction.BillID),
	).Info("销账取消处理完成")

	return response, nil
}

// reverseBillUpdate 逆向更新账单信息
func (s *PaymentService) reverseBillUpdate(tx gform.IOrm, bill *model.BusinessRepaymentBills, cancelAmount model.Decimal) error {
	// 计算新的已还金额
	newPaidAmount := bill.PaidAmount - cancelAmount
	if newPaidAmount < 0 {
		return fmt.Errorf("取消金额超过账单已还金额")
	}

	updateMap := map[string]interface{}{
		"paid_amount": newPaidAmount,
	}

	// 重新计算账单状态
	carbonDue := carbon.CreateFromStdTime(bill.DueDate)
	if newPaidAmount == 0 {
		// 完全未支付
		updateMap["paid_at"] = (*time.Time)(nil)
		if carbonDue.DiffInDays(carbon.Now()) > 0 {
			updateMap["status"] = model.RepaymentBillStatusOverdueUnpaid
		} else {
			updateMap["status"] = model.RepaymentBillStatusUnpaid
		}
	} else if newPaidAmount < bill.TotalDueAmount {
		// 部分支付
		if carbonDue.DiffInDays(carbon.Now()) > 0 {
			updateMap["status"] = model.RepaymentBillStatusOverduePartialPaid
		} else {
			updateMap["status"] = model.RepaymentBillStatusPartialPaid
		}
	}
	// 如果 newPaidAmount >= bill.TotalDueAmount，保持原状态

	_, err := s.billModel.UpdateBill(tx, model.UpdateBillResultWhere{
		ID: bill.ID,
	}, updateMap)

	return err
}

// reverseOrderUpdate 逆向更新订单信息
func (s *PaymentService) reverseOrderUpdate(tx gform.IOrm, order *model.BusinessLoanOrders, cancelAmount decimal.Decimal) error {
	// 计算新的已还金额
	newOrderPaidAmount := order.AmountPaid - cancelAmount
	if newOrderPaidAmount < 0 {
		return fmt.Errorf("取消金额超过订单已还金额")
	}

	updateMap := map[string]interface{}{
		"amount_paid": newOrderPaidAmount,
	}

	// 如果订单从完成状态变为未完成
	if order.Status == model.OrderStatusCompleted && newOrderPaidAmount < order.TotalRepayableAmount {
		updateMap["status"] = model.OrderStatusDisbursed // 恢复为放款成功状态
		updateMap["completed_at"] = nil
	}

	return s.orderModel.UpdateOrder(tx, model.UpdateOrderCondition{
		ID: int(order.ID),
	}, updateMap)
}

// CalculateRepaymentSchedule 计算还款计划
func (s *PaymentService) CalculateRepaymentSchedule(req *CalculateScheduleRequest) (*repaymentUtils.RepaymentSchedule, error) {
	// 参数验证
	if req == nil {
		return nil, errors.New("请求参数不能为空")
	}

	if req.AnnualInterestRate == 0 {
		return nil, errors.New("年利率不能为0")
	}

	// 构建产品规则对象
	productRule := &model.ProductRules{
		LoanAmount:         req.LoanAmount,
		LoanPeriod:         req.LoanPeriod,
		TotalPeriods:       req.TotalPeriods,
		GuaranteeFee:       req.GuaranteeFee,
		AnnualInterestRate: req.AnnualInterestRate,
		OtherFees:          req.OtherFees,
		PrePrincipal:       req.PrePrincipal,
		PreInterest:        req.PreInterest,
		PreGuaranteeFee:    req.PreGuaranteeFee,
	}

	// 调用还款计算器计算还款计划
	schedule, err := repaymentUtils.CalculateRepaymentSchedule(productRule)
	if err != nil {
		if s.logger != nil {
			s.logger.Error("计算还款计划失败: " + err.Error())
		}
		return nil, fmt.Errorf("计算还款计划失败: %v", err)
	}

	if s.logger != nil {
		s.logger.Info("计算还款计划成功")
	}

	return schedule, nil
}
