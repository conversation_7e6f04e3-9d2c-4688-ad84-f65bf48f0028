package repayment

import (
	"errors"
	businessorder "fincore/app/business/order"
	"fincore/global"
	"fincore/model"
	"fincore/utils/config"
	"fincore/utils/gform"
	"fincore/utils/lock"
	"fincore/utils/log"
	"fincore/utils/shopspringutils"
	"fmt"
	"math/rand"
	"net"
	"strings"
	"time"

	// 引入高精度decimal库
	"github.com/dromara/carbon/v2"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/shopspring/decimal"
)

// PayOrderInfo 支付订单信息
type PayOrderInfo struct {
	OrderNo       string // 原始订单号
	TransactionNo string // 交易流水号
	WithholdType  string // 代扣类型：ASSET/GUARANTEE
}

// ParsePayOrderNo 解析支付订单号
// 从支付订单号中提取原始订单号、交易流水号和代扣类型
func ParsePayOrderNo(payOrderNo string) (*PayOrderInfo, error) {
	if payOrderNo == "" {
		return nil, errors.New("支付订单号不能为空")
	}

	// 按下划线分割
	parts := strings.Split(payOrderNo, "_")
	if len(parts) != 3 {
		return nil, fmt.Errorf("支付订单号格式错误，期望格式: orderNo_transactionNo_withholdType，实际: %s", payOrderNo)
	}

	orderNo := parts[0]
	transactionNo := parts[1]
	withholdType := parts[2]

	// 验证各部分是否为空
	if orderNo == "" {
		return nil, errors.New("订单号不能为空")
	}
	if transactionNo == "" {
		return nil, errors.New("交易流水号不能为空")
	}
	if withholdType == "" {
		return nil, errors.New("代扣类型不能为空")
	}

	// 验证代扣类型是否有效
	if withholdType != "ASSET" && withholdType != "GUARANTEE" {
		return nil, fmt.Errorf("无效的代扣类型: %s，有效值: ASSET, GUARANTEE", withholdType)
	}

	return &PayOrderInfo{
		OrderNo:       orderNo,
		TransactionNo: transactionNo,
		WithholdType:  withholdType,
	}, nil
}

// ParsePayOrderNoFields 从支付订单号中解析出三个字段
// 返回值：orderNo, transactionNo, withholdType, error
func ParsePayOrderNoFields(payOrderNo string) (string, string, string, error) {
	info, err := ParsePayOrderNo(payOrderNo)
	if err != nil {
		return "", "", "", err
	}
	return info.OrderNo, info.TransactionNo, info.WithholdType, nil
}

// ValidatePayOrderNo 验证支付订单号格式是否正确
func ValidatePayOrderNo(payOrderNo string) error {
	_, err := ParsePayOrderNo(payOrderNo)
	return err
}

// getStatusString 将状态码转换为状态字符串
func getStatusString(status int) string {
	switch status {
	case model.TransactionStatusPending:
		return "pending"
	case model.TransactionStatusSubmitted:
		return "submitted"
	case model.TransactionStatusSuccess:
		return "success"
	case model.TransactionStatusFailed:
		return "failed"
	default:
		return "unknown"
	}
}

// getSystemIP 获取系统IP地址
func getSystemIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "127.0.0.1"
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

// ceilToTwoDecimal 向上取整到两位小数（使用shopspringutils）
func ceilToTwoDecimal(value float64) float64 {
	return shopspringutils.CeilToTwoDecimal(value)
}

// GetMerNoByWithholdType 根据业务类型获取对应的商户配置
func GetMerNoByWithholdType(withholdType string) (config.Merchant, error) {
	cfg := global.App.Config
	withholdType = strings.ToUpper(withholdType)
	switch withholdType {
	case WithholdTypeAsset:
		return cfg.SumPay.AssetMerchant, nil
	case WithholdTypeGuarantee:
		return cfg.SumPay.GuaranteeMerchant, nil
	default:
		return config.Merchant{}, fmt.Errorf("无效的代扣类型: %s", withholdType)
	}
}

// generatePaymentTransactionNo 生成支付唯一流水号
// 格式: RP + 时间戳(毫秒) + 随机数，总长度不超过32字符
func generatePaymentTransactionNo() string {
	// 使用毫秒时间戳(13位) + 6位随机数，总长度: RP(2) + 13 + 6 = 21字符
	timestamp := time.Now().UnixNano() / 1000000 // 毫秒时间戳
	randomNum := rand.Intn(1000000)              // 6位随机数
	return fmt.Sprintf("RP%d%06d", timestamp, randomNum)
}

// getPendingWithholdBills 获取待代扣的账单
func (s *PaymentService) getPendingWithholdBills() ([]model.BusinessRepaymentBills, error) {
	// 查询状态为：0-待支付、2-逾期已支付、3-逾期待支付、7-部分还款的账单 9-逾期部分支付
	targetStatuses := []int{
		model.RepaymentBillStatusUnpaid,             // 0-待支付
		model.RepaymentBillStatusOverdueUnpaid,      // 3-逾期待支付
		model.RepaymentBillStatusPartialPaid,        // 7-部分还款
		model.RepaymentBillStatusOverduePartialPaid, // 9-逾期部分支付
	}

	return s.billModel.GetBillsByStatus(targetStatuses)
}

// checkRepaymentSuccess 检查还款响应是否成功
func (s *PaymentService) checkRepaymentSuccess(repaymentResponse *CreateRepaymentResponse) bool {
	if repaymentResponse == nil {
		return false
	}

	// 检查担保费支付结果
	if repaymentResponse.GuaranteePayment != nil {
		if repaymentResponse.GuaranteePayment.Status == "failed" {
			s.logger.WithFields(
				log.String("message", repaymentResponse.GuaranteePayment.Message),
				log.String("payment_type", "guarantee_fee"),
				log.String("action", "payment_failed"),
			).Error("担保费支付失败")
			return false
		}
	}

	// 检查资管费支付结果
	if repaymentResponse.AssetPayment != nil {
		if repaymentResponse.AssetPayment.Status == "failed" {
			s.logger.WithFields(
				log.String("message", repaymentResponse.AssetPayment.Message),
				log.String("payment_type", "asset_management"),
				log.String("action", "payment_failed"),
			).Error("资管费支付失败")
			return false
		}
	}

	// 如果有支付项目且都不是失败状态，则认为成功
	return true
}

// getPaymentData 获取支付相关数据
func (s *PaymentService) getPaymentData(billID, bankCardID int) (*model.BusinessRepaymentBills, *model.BusinessBankCards, *model.BusinessLoanOrders, error) {
	// 2. 查询账单信息
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("查询账单失败: %v", err)
	}
	// 3. 查询银行卡信息
	bankCard, err := s.bankCardModel.GetBankCardsByCondition(model.QueryBankCardsParams{
		CardID: bankCardID,
	})
	if err != nil {
		return nil, nil, nil, fmt.Errorf("查询银行卡失败: %v", err)
	}
	if bankCard == nil {
		return nil, nil, nil, fmt.Errorf("银行卡不存在")
	}

	// 4. 查询订单信息
	order, err := s.orderModel.GetOrderByID(nil, bill.OrderID)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("查询订单失败: %v", err)
	}

	if order.Status != model.OrderStatusDisbursed {
		return nil, nil, nil, fmt.Errorf("订单状态不是放款中")

	}

	return bill, bankCard, order, nil
}

// updateBillAndOrderPaidAmount 根据账单ID更新还款账单和订单的已还金额
func (s *PaymentService) updateBillAndOrderPaidAmount(tx gform.IOrm, orderID, billID int, paidAmount float64) (err error) {
	s.logger.WithFields(
		log.Int("bill_id", billID),
		log.Int("order_id", orderID),
		log.Float64("paid_amount", paidAmount),
		log.String("action", "update_bill_order_amount_start"),
	).Info("开始更新账单和订单已还金额")

	// 更新账单和订单前加锁
	statusLock, err := lock.GetBillOrderLock(billID, orderID, s.logger).Lock()
	if err != nil {
		return fmt.Errorf("获取账单订单锁失败: %v", err)
	}
	defer statusLock.Unlock()

	// 1. 根据账单ID查询账单
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		s.logger.WithFields(
			log.Int("bill_id", billID),
			log.String("action", "get_bill_failed"),
			log.ErrorField(err),
		).Error("查询还款账单失败")
		return fmt.Errorf("查询还款账单失败: %v", err)
	}
	if bill == nil {
		s.logger.WithFields(
			log.Int("bill_id", billID),
			log.String("action", "bill_not_found"),
		).Error("账单不存在")
		return fmt.Errorf("账单不存在")
	}

	// 2. 查询订单信息
	order, err := s.orderModel.GetOrderByID(nil, bill.OrderID)
	if err != nil {
		s.logger.WithFields(
			log.Int("bill_id", billID),
			log.Int("order_id", bill.OrderID),
			log.String("action", "get_order_failed"),
			log.ErrorField(err),
		).Error("查询订单失败")
		return fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		s.logger.WithFields(
			log.Int("bill_id", billID),
			log.Int("order_id", bill.OrderID),
			log.String("action", "order_not_found"),
		).Error("订单不存在")
		return fmt.Errorf("订单不存在")
	}

	// 3. 计算账单有效还款金额（已还金额 + 本次支付金额 + 减免金额）
	billEffectivePayment := shopspringutils.AddAmountsWithDecimal(float64(bill.PaidAmount), paidAmount)
	billEffectivePayment = shopspringutils.AddAmountsWithDecimal(billEffectivePayment, float64(bill.TotalWaiveAmount))

	// 计算账单新的已还金额（不包含减免金额）
	newBillPaidAmount := shopspringutils.AddAmountsWithDecimal(float64(bill.PaidAmount), paidAmount)
	if shopspringutils.CompareAmountsWithDecimal(newBillPaidAmount, float64(bill.TotalDueAmount)) > 0 {
		newBillPaidAmount = float64(bill.TotalDueAmount)
	}

	// 4. 获取订单总减免金额
	totalOrderWaiveAmount, err := s.billModel.GetTotalWaiveAmountByOrderID(bill.OrderID)
	if err != nil {
		s.logger.WithFields(
			log.Int("bill_id", billID),
			log.Int("order_id", bill.OrderID),
			log.String("action", "get_total_waive_amount_failed"),
			log.ErrorField(err),
		).Error("查询订单总减免金额失败")
		return fmt.Errorf("查询订单总减免金额失败: %v", err)
	}

	// 计算订单新的已还金额
	newOrderAmountPaid := shopspringutils.AddAmountsWithDecimal(float64(order.AmountPaid), paidAmount)
	if shopspringutils.CompareAmountsWithDecimal(newOrderAmountPaid, float64(order.TotalRepayableAmount)) > 0 {
		newOrderAmountPaid = float64(order.TotalRepayableAmount)
	}

	// 计算订单有效还款金额（已还金额 + 本次支付金额 + 总减免金额）
	orderEffectivePayment := shopspringutils.AddAmountsWithDecimal(newOrderAmountPaid, totalOrderWaiveAmount)

	// 5. 判断账单是否逾期
	now := time.Now()
	duTime := carbon.CreateFromStdTime(bill.DueDate).EndOfDay().StdTime()
	isOverdue := now.After(duTime)

	// 6. 根据逾期状态和有效还款金额确定账单新状态
	var newBillStatus int
	if shopspringutils.CompareAmountsWithDecimal(billEffectivePayment, float64(bill.TotalDueAmount)) >= 0 {
		// 完全还清
		if isOverdue {
			newBillStatus = model.RepaymentBillStatusOverduePaid // 逾期已支付
		} else {
			newBillStatus = model.RepaymentBillStatusPaid // 已支付
		}
	} else if shopspringutils.CompareAmountsWithDecimal(billEffectivePayment, 0) > 0 {
		// 部分还款
		if isOverdue {
			newBillStatus = model.RepaymentBillStatusOverduePartialPaid // 逾期部分支付
		} else {
			newBillStatus = model.RepaymentBillStatusPartialPaid // 部分还款
		}
	} else {
		// 未还款
		if isOverdue {
			newBillStatus = model.RepaymentBillStatusOverdueUnpaid // 逾期待支付
		} else {
			newBillStatus = model.RepaymentBillStatusUnpaid // 待支付
		}
	}

	// 7. 计算订单新状态
	newOrderStatus := order.Status
	if shopspringutils.CompareAmountsWithDecimal(orderEffectivePayment, float64(order.TotalRepayableAmount)) >= 0 {
		// 订单已完全还清
		newOrderStatus = model.OrderStatusCompleted // 交易完成
	}

	// 8. 开启事务，同时更新账单和订单的已还金额
	s.logger.WithFields(
		log.Int("bill_id", billID),
		log.Int("order_id", bill.OrderID),
		log.Int("new_bill_status", newBillStatus),
		log.Int("new_order_status", int(newOrderStatus)),
		log.String("action", "start_transaction_update"),
	).Info("开始事务更新账单和订单")

	// 更新还款账单的已还金额和状态
	billUpdateMap := map[string]interface{}{
		"paid_amount": model.Decimal(shopspringutils.CeilToTwoDecimal(newBillPaidAmount)),
		"status":      newBillStatus,
	}

	// 账单完结
	var billEnd bool
	if newBillStatus == model.RepaymentBillStatusPaid || newBillStatus == model.RepaymentBillStatusOverduePaid {
		billEnd = true
		billUpdateMap["paid_at"] = time.Now()
	}

	_, err = tx.Table("business_repayment_bills").Where("id", billID).Update(billUpdateMap)
	if err != nil {
		return fmt.Errorf("更新还款账单已还金额和状态失败: %v", err)
	}

	if billEnd {
		// 账单完结，更新用户额度
		var newReminderQuota float64
		userInfoMap, err := model.GetUserByID(int64(bill.UserID))
		if err != nil {
			return fmt.Errorf("查询用户剩余额度失败: %v", err)
		}

		if len(userInfoMap) == 0 {
			return fmt.Errorf("用户不存在")
		}

		// 总额度
		allQuota := gconv.Float64(userInfoMap["allQuota"])
		// 剩余额度
		reminderQuota := gconv.Float64(userInfoMap["reminderQuota"])
		// 已提升的额度
		amountOfPromotion := gconv.Float64(userInfoMap["amountOfPromotion"])

		// 查询用户初始渠道信息
		channelService := model.NewChannelService()
		channel, err := channelService.GetChannelByID(order.ChannelID)
		if err != nil {
			return fmt.Errorf("查询用户初始渠道信息失败: %v", err)
		}

		// 每次可提升额额度
		everPromotionAmount := channel.RiskControl1Limit
		// 可提升的额度上限
		everPromotionAmountLimit := channel.TotalAmount

		// 计算用户新额度
		newReminderQuota = shopspringutils.AddAmountsWithDecimal(reminderQuota, float64(bill.DuePrincipal))

		upMapData := map[string]interface{}{
			"reminderQuota": model.Decimal(newReminderQuota),
		}

		// 未达到可提额度上限,提升额度
		if shopspringutils.CompareAmountsWithDecimal(allQuota, everPromotionAmountLimit) < 0 {
			allQuota = shopspringutils.AddAmountsWithDecimal(allQuota, everPromotionAmount)
			amountOfPromotion = shopspringutils.AddAmountsWithDecimal(amountOfPromotion, everPromotionAmount)
			newReminderQuota = shopspringutils.AddAmountsWithDecimal(newReminderQuota, everPromotionAmount)
			upMapData["reminderQuota"] = model.Decimal(newReminderQuota)
			upMapData["allQuota"] = model.Decimal(allQuota)
			upMapData["amountOfPromotion"] = model.Decimal(amountOfPromotion)
		}

		_, err = tx.Table("business_app_account").Where("id", bill.UserID).Update(upMapData)

		if err != nil {
			return fmt.Errorf("更新用户额度失败: %v", err)
		}

	}

	// 更新订单的已还金额和状态
	orderUpdateMap := map[string]interface{}{
		"amount_paid": model.Decimal(shopspringutils.CeilToTwoDecimal(newOrderAmountPaid)),
		"status":      newOrderStatus,
	}

	// 订单完结
	if newOrderStatus == model.OrderStatusCompleted {
		orderUpdateMap["completed_at"] = time.Now()
	}

	_, err = tx.Table("business_loan_orders").Where("id", bill.OrderID).Update(orderUpdateMap)
	if err != nil {
		return fmt.Errorf("更新订单已还金额和状态失败: %v", err)
	}

	if err != nil {
		s.logger.WithFields(
			log.Int("bill_id", billID),
			log.Int("order_id", bill.OrderID),
			log.String("action", "transaction_failed"),
			log.ErrorField(err),
		).Error("事务执行失败")
		return fmt.Errorf("事务执行失败: %v", err)
	}

	s.logger.WithFields(
		log.Int("bill_id", billID),
		log.Int("order_id", bill.OrderID),
		log.Float64("paid_amount", paidAmount),
		log.String("action", "update_bill_order_amount_complete"),
	).Info("更新账单和订单已还金额完成")

	// 刷新风控数据
	if newOrderStatus == model.OrderStatusCompleted {
		err = businessorder.NewOrderServiceWithOptions(s.ctx, businessorder.WithRepository(), businessorder.WithOrderModel()).RefreshRiskData(bill.UserID)
		if err != nil {
			s.logger.WithFields(
				log.Int("bill_id", billID),
				log.Int("order_id", bill.OrderID),
				log.String("action", "refresh_risk_data_failed"),
				log.ErrorField(err),
			).Error("刷新风控数据失败")
		}
		return err
	}

	return nil
}

// ValidateRepaymentAmount 智能还款金额校验
func (s *PaymentService) ValidateRepaymentAmount(billID int, requestAmount float64, withholdType string) error {
	s.logger.WithFields(
		log.Int("bill_id", billID),
		log.Float64("request_amount", requestAmount),
		log.String("withhold_type", withholdType),
		log.String("action", "validate_repayment_amount"),
	).Info("开始校验还款金额")

	if requestAmount <= 0 {
		return fmt.Errorf("还款金额必须大于0")
	}
	// 1. 查询账单信息
	bill, err := s.billModel.GetBillByID(billID)
	if err != nil {
		return fmt.Errorf("查询账单失败: %v", err)
	}

	// 2. 检查账单状态（仅允许待支付和部分还款）
	allowedStatuses := []int{
		model.RepaymentBillStatusUnpaid,             // 0-待支付
		model.RepaymentBillStatusOverdueUnpaid,      // 3-逾期待支付
		model.RepaymentBillStatusPartialPaid,        // 7-部分还款
		model.RepaymentBillStatusOverduePartialPaid, // 9-逾期部分支付
	}
	statusAllowed := false
	for _, status := range allowedStatuses {
		if bill.Status == status {
			statusAllowed = true
			break
		}
	}
	if !statusAllowed {
		statusText := model.RepaymentBillStatusDescriptions[bill.Status]
		return fmt.Errorf("账单状态不允许还款，当前状态: %s", statusText)
	}

	// 3. 查询已提交流水总额（使用统一的计算逻辑）
	guaranteeAmount, assetAmount, err := shopspringutils.CalculateSubmittedAmounts(billID)
	if err != nil {
		return err
	}

	// 3.1 计算减免后的实际应还金额
	remainingGuaranteeAmount, remainingAssetAmount := shopspringutils.CalculateRemainingAmounts(
		float64(bill.TotalDueAmount),
		float64(bill.TotalWaiveAmount),
		float64(bill.DueGuaranteeFee),
		float64(bill.AssetManagementEntry),
	)

	// 4. 根据withholdType类型决定比较逻辑
	// 使用decimal进行高精度计算
	var remainingAmountDecimal, submittedAmountDecimal decimal.Decimal
	var remainingAmount, submittedAmount float64

	switch withholdType {
	case WithholdTypeGuarantee:
		// 担保费比较
		remainingAmountDecimal = decimal.NewFromFloat(remainingGuaranteeAmount)
		submittedAmountDecimal = decimal.NewFromFloat(guaranteeAmount)

		remainingAmount, _ = remainingAmountDecimal.Float64()
		submittedAmount, _ = submittedAmountDecimal.Float64()

		s.logger.WithFields(
			log.Float64("remaining_guarantee_amount_after_waive", remainingAmount),
			log.Float64("submitted_guarantee_amount", submittedAmount),
			log.String("action", "guarantee_fee_validation"),
		).Info("担保费校验")
	case WithholdTypeAsset:
		// 资管费比较
		remainingAmountDecimal = decimal.NewFromFloat(remainingAssetAmount)
		submittedAmountDecimal = decimal.NewFromFloat(assetAmount)

		remainingAmount, _ = remainingAmountDecimal.Float64()
		submittedAmount, _ = submittedAmountDecimal.Float64()

		s.logger.WithFields(
			log.Float64("remaining_asset_amount_after_waive", remainingAmount),
			log.Float64("submitted_asset_amount", submittedAmount),
			log.String("action", "asset_fee_validation"),
		).Info("资管费校验")
	default:
		// 其他类型使用总金额比较
		remainingAmountDecimal = decimal.NewFromFloat(remainingGuaranteeAmount).Add(decimal.NewFromFloat(remainingAssetAmount))
		submittedAmountDecimal = decimal.NewFromFloat(guaranteeAmount).Add(decimal.NewFromFloat(assetAmount))

		remainingAmount, _ = remainingAmountDecimal.Float64()
		submittedAmount, _ = submittedAmountDecimal.Float64()

		s.logger.WithFields(
			log.Float64("total_remaining_amount_after_waive", remainingAmount),
			log.Float64("total_submitted_amount", submittedAmount),
			log.String("action", "total_amount_validation"),
		).Info("总金额校验")
	}

	// 计算实际还需支付的金额（减免后应还金额 - 已支付金额）
	remainingAmountDecimal = remainingAmountDecimal.Sub(submittedAmountDecimal)
	remainingAmount, _ = remainingAmountDecimal.Float64()

	requestAmountDecimal := decimal.NewFromFloat(requestAmount)
	if requestAmountDecimal.GreaterThan(remainingAmountDecimal) {
		return fmt.Errorf("还款金额%.2f元超过剩余应还金额%.2f元", requestAmount, remainingAmount)
	}

	// 7. 如果剩余金额小于等于0，不允许继续还款
	if remainingAmountDecimal.LessThanOrEqual(decimal.Zero) {
		return fmt.Errorf("该账单已无剩余应还金额")
	}

	return nil
}

// getWithholdTypePtr 获取代扣类型指针
func getWithholdTypePtr(withholdType string) *string {
	if withholdType == "" {
		return nil
	}
	return &withholdType
}

// getBankCardIDPtr 获取银行卡ID指针
func getBankCardIDPtr(bankCard *model.BusinessBankCards) *int {
	if bankCard == nil {
		return nil
	}
	return &bankCard.ID
}
