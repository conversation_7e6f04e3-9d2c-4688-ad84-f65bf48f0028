package risk

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"fincore/app/business/blacklist"
	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/riskmodelservice"
	"fincore/utils/log"
	"fincore/utils/shopspringutils"
)

// LoanRule 放款规则结构
type LoanRule struct {
	RuleID       uint64  `json:"rule_id"`
	MinRiskScore float64 `json:"min_risk_score"`
	MaxRiskScore float64 `json:"max_risk_score"`
}

// GetCustomerIDFromData 从数据中获取客户ID
func GetCustomerIDFromData(data map[string]interface{}) (uint64, error) {
	if customerIDVal, ok := data["customer_id"]; ok {
		switch v := customerIDVal.(type) {
		case string:
			if customerID, err := strconv.ParseUint(v, 10, 64); err == nil {
				return customerID, nil
			}
		case float64:
			return uint64(v), nil
		case int:
			return uint64(v), nil
		case int64:
			return uint64(v), nil
		case uint64:
			return v, nil
		}
	}
	return 0, fmt.Errorf("客户ID不能为空或格式错误")
}

// GetChannelIDFromData 从数据中获取渠道ID
func GetChannelIDFromData(data map[string]interface{}) (uint64, error) {
	if channelIDVal, ok := data["channel_id"]; ok {
		switch v := channelIDVal.(type) {
		case string:
			if channelID, err := strconv.ParseUint(v, 10, 64); err == nil {
				return channelID, nil
			}
		case float64:
			return uint64(v), nil
		case int:
			return uint64(v), nil
		case uint64:
			return v, nil
		}
	}
	return 0, nil
}

// findProductByID 根据产品ID查找匹配的产品
func findProductByID(products []*model.ProductRules, productID int) *model.ProductRules {
	for _, product := range products {
		if product.ID == productID {
			return product
		}
	}
	return nil
}

// isEvaluationValid 检查评估是否仍然有效
func isEvaluationValid(evaluationTime time.Time) bool {
	// 从配置中获取评估有效期（小时）
	expiryHours := global.App.Config.RiskEvaluation.EvaluationExpiryHours
	if expiryHours <= 0 {
		// 如果配置无效，默认24小时
		expiryHours = 24
	}

	// 计算过期时间
	expiryTime := evaluationTime.Add(time.Duration(expiryHours) * time.Hour)

	// 检查是否还在有效期内
	return time.Now().Before(expiryTime)
}

// QuotaCalculationResult 额度计算结果
type QuotaCalculationResult struct {
	NewAllQuota           float64 // 新的总额度
	CurrentBorrowedAmount float64 // 当前在借额度
	AvailableQuota        float64 // 可用额度
}

// calculateQuotaInfo 计算额度信息的通用函数（使用decimal进行精确计算）
func calculateQuotaInfo(accountAllQuota, accountReminderQuota, overallCreditLimit float64) QuotaCalculationResult {
	newAllQuota := overallCreditLimit
	currentBorrowedAmount := 0.0

	// 如果用户已有额度记录，需要计算在借额度
	if shopspringutils.CompareAmountsWithDecimal(accountAllQuota, 0) > 0 {
		// 使用decimal计算目前在借额度 = 账户总额度 - 账户可用额度
		currentBorrowedAmount = shopspringutils.SubtractAmountsWithDecimal(accountAllQuota, accountReminderQuota)
		// 确保在借额度不为负数
		if shopspringutils.CompareAmountsWithDecimal(currentBorrowedAmount, 0) < 0 {
			currentBorrowedAmount = 0
		}
		// 新总额度以系统计算总额度为标准，不超过系统限制
		if shopspringutils.CompareAmountsWithDecimal(accountAllQuota, overallCreditLimit) < 0 {
			newAllQuota = accountAllQuota
		} else {
			newAllQuota = overallCreditLimit
		}
	}

	// 使用decimal计算可用额度 = 新总额度 - 目前在借额度
	availableQuota := shopspringutils.SubtractAmountsWithDecimal(newAllQuota, currentBorrowedAmount)

	// 确保可用额度不小于0
	if shopspringutils.CompareAmountsWithDecimal(availableQuota, 0) < 0 {
		availableQuota = 0
	}

	// 使用CeilToTwoDecimal处理最终结果
	return QuotaCalculationResult{
		NewAllQuota:           shopspringutils.CeilToTwoDecimal(newAllQuota),
		CurrentBorrowedAmount: shopspringutils.CeilToTwoDecimal(currentBorrowedAmount),
		AvailableQuota:        shopspringutils.CeilToTwoDecimal(availableQuota),
	}
}

func generateEvaluationID(customerID int) string {
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("EVAL_%d_%d", customerID, timestamp)
}

// adjustRiskResultAndScore 根据客户信息调整风险结果和分数
// 如果是线下渠道或者有可用产品ID，则设置为审核状态和最高风险分数
func adjustRiskResultAndScore(customerInfo *model.BusinessAppAccount, evaluation *model.RiskEvaluation, isFront bool) (int, int) {
	riskResult := evaluation.RiskResult
	riskScore := evaluation.RiskScore
	if customerInfo.ChannelID == model.OfflineChannel {
		riskResult = model.REVIEW
		riskScore = model.MaxRiskScore
		if isFront {
			riskResult = model.APPROVED
		}
	}
	if customerInfo.AvailableProductID != model.NoChange {
		riskResult = model.REVIEW
		if isFront {
			riskResult = model.APPROVED
		}
		riskScore = model.MaxRiskScore
	}

	return riskResult, riskScore
}

// handleRefreshRiskDataPromotion 额度提升逻辑
func (s *RiskService) handleRefreshRiskDataPromotion(customerInfo *model.BusinessAppAccount, riskResult int, quotaResult QuotaCalculationResult) (float64, float64) {
	if riskResult == model.REJECTED {
		return quotaResult.NewAllQuota, quotaResult.AvailableQuota
	}

	allQuote := quotaResult.NewAllQuota
	remainingQuota := quotaResult.AvailableQuota

	// 查询用户渠道信息
	channelService := model.NewChannelService()
	channel, err := channelService.GetChannelByID(customerInfo.ChannelID)
	if err != nil {
		s.logger.WithFields(
			log.Int64("customer_id", int64(customerInfo.ID)),
			log.Int("channel_id", customerInfo.ChannelID),
			log.String("error", err.Error()),
		).Error("查询渠道信息失败，跳过额度提升")
		return allQuote, remainingQuota
	}

	// 记录额度提升操作
	s.logger.WithFields(
		log.Int64("customer_id", int64(customerInfo.ID)),
		log.Float64("old_all_quota", allQuote),
		log.Float64("old_remaining_quota", remainingQuota),
		log.Float64("promotion_amount", customerInfo.AmountOfPromotion),
		log.Float64("channel_total_amount", channel.TotalAmount),
		log.Float64("current_borrowed_amount", quotaResult.CurrentBorrowedAmount),
	).Info("开始提升用户额度")

	// 计算提升后的额度
	newAllQuote := shopspringutils.AddAmountsWithDecimal(allQuote, customerInfo.AmountOfPromotion)
	newRemainingQuota := shopspringutils.AddAmountsWithDecimal(remainingQuota, customerInfo.AmountOfPromotion)

	// 检查提升后的总额度是否超过渠道限制
	if shopspringutils.CompareAmountsWithDecimal(newAllQuote, channel.TotalAmount) > 0 {
		// 如果超过渠道限制，将总额度设置为渠道限制值
		newAllQuote = channel.TotalAmount
		// 可借额度 = 渠道限制额度 - 正在借的额度
		if quotaResult.CurrentBorrowedAmount > 0 {
			newRemainingQuota = shopspringutils.SubtractAmountsWithDecimal(channel.TotalAmount, quotaResult.CurrentBorrowedAmount)
			// 确保可借额度不为负数
			if shopspringutils.CompareAmountsWithDecimal(newRemainingQuota, 0) < 0 {
				newRemainingQuota = 0
			}
		} else {
			newRemainingQuota = channel.TotalAmount
		}

		s.logger.WithFields(
			log.Int64("customer_id", int64(customerInfo.ID)),
			log.Float64("adjusted_all_quota", newAllQuote),
			log.Float64("adjusted_remaining_quota", newRemainingQuota),
			log.Float64("channel_total_amount", channel.TotalAmount),
			log.Float64("current_borrowed_amount", quotaResult.CurrentBorrowedAmount),
		).Info("提升后总额度超过渠道限制，调整为渠道限制值")
	}

	// 记录提升后的额度
	s.logger.WithFields(
		log.Int64("customer_id", int64(customerInfo.ID)),
		log.Float64("new_all_quota", newAllQuote),
		log.Float64("new_remaining_quota", newRemainingQuota),
	).Info("用户额度提升完成")

	return newAllQuote, newRemainingQuota
}

// ProductRuleFilter 定义产品规则过滤器函数类型
type ProductRuleFilter func(rule LoanRule) bool

// 常用过滤器
var (
	// AllProductsFilter 包含所有产品的过滤器
	AllProductsFilter ProductRuleFilter = func(rule LoanRule) bool {
		return true
	}
)

// CreateRiskScoreFilter 创建基于风险分数的过滤器
func CreateRiskScoreFilter(riskScore float64) ProductRuleFilter {
	return func(rule LoanRule) bool {
		return riskScore > rule.MinRiskScore
	}
}

// getProductRules 通用的产品规则获取方法，根据渠道和过滤器获取产品规则
func (s *RiskService) getProductRules(ctx context.Context, channelID uint64, filter ProductRuleFilter) ([]*model.ProductRules, error) {
	// 1. 从数据库获取渠道信息
	channelService := model.NewChannelService()
	var channels []model.Channel
	var err error

	if channelID == 0 {
		// 获取所有活跃渠道
		channels, err = channelService.GetActiveChannels()
	} else {
		// 获取指定渠道
		channel, err := channelService.GetChannelByID(int(channelID))
		if err != nil {
			return nil, fmt.Errorf("查询渠道失败: %v", err)
		}
		if channel != nil && channel.ChannelStatus == model.ChannelStatusEnabled {
			channels = append(channels, *channel)
		}
	}

	if err != nil {
		return nil, fmt.Errorf("查询渠道失败: %v", err)
	}

	if len(channels) == 0 {
		return []*model.ProductRules{}, nil
	}

	// 2. 根据渠道的loan_rules和过滤器匹配产品规则ID
	var matchedRuleIDs []uint64
	for _, channel := range channels {
		// 解析loan_rules字段
		if channel.LoanRules == "" {
			continue
		}
		var loanRules []LoanRule
		if err := json.Unmarshal([]byte(channel.LoanRules), &loanRules); err != nil {
			s.logger.WithError(err).WithFields(
				log.Int("channel_id", channel.ID),
				log.String("action", "parse_loan_rules_failed"),
			).Warn("解析渠道loan_rules失败")
			continue
		}
		// 根据过滤器筛选规则
		for _, rule := range loanRules {
			if filter(rule) {
				matchedRuleIDs = append(matchedRuleIDs, rule.RuleID)
			}
		}
	}

	if len(matchedRuleIDs) == 0 {
		s.logger.WithFields(
			log.Uint64("channel_id", channelID),
			log.String("action", "no_matching_rules"),
		).Info("未找到匹配的产品规则")
		return []*model.ProductRules{}, nil
	}

	// 3. 根据规则ID获取产品
	productRulesService := &model.ProductRulesService{}
	productRulesSlice, err := productRulesService.GetProductRulesByIDs(matchedRuleIDs)
	if err != nil {
		return nil, fmt.Errorf("查询产品规则失败: %v", err)
	}
	return productRulesSlice, nil
}

// getMatchingProductsByChannelIDAndRiskScore 获取匹配风险分数的贷款产品
func (s *RiskService) getMatchingProductsByChannelIDAndRiskScore(ctx context.Context, channelID uint64, riskScore float64) ([]*model.ProductRules, error) {
	return s.getProductRules(ctx, channelID, CreateRiskScoreFilter(riskScore))
}

// getAllProductsByChannelID 获取渠道下的所有产品规则，不受风控分数限制
func (s *RiskService) getAllProductsByChannelID(ctx context.Context, channelID uint64) ([]*model.ProductRules, error) {
	return s.getProductRules(ctx, channelID, AllProductsFilter)
}

// calculateMaxCreditFromProducts 从产品列表中计算最大授信额度
func (s *RiskService) calculateMaxCreditFromProducts(products []*model.ProductRules) float64 {
	var maxLoanAmount float64
	for _, product := range products {
		if product.LoanAmount > maxLoanAmount {
			maxLoanAmount = product.LoanAmount
		}
	}
	return maxLoanAmount
}

// filterProductsByQuota 根据可用额度过滤产品
func (s *RiskService) filterProductsByQuota(products []*model.ProductRules, availableQuota float64) ([]*model.ProductRules, float64) {
	filteredProducts := make([]*model.ProductRules, 0, len(products))
	var maxAvailableCredit float64

	for _, product := range products {
		if product.LoanAmount <= availableQuota {
			filteredProducts = append(filteredProducts, product)
			if maxAvailableCredit < product.LoanAmount {
				maxAvailableCredit = product.LoanAmount
			}
		}
	}

	return filteredProducts, maxAvailableCredit
}

// getProductByAvailableID 根据availableProductID获取单个产品
func (s *RiskService) getProductByAvailableID(availableProductID int) ([]*model.ProductRules, error) {
	productRulesService := &model.ProductRulesService{}
	product, err := productRulesService.GetProductRuleByID(availableProductID)
	if err != nil {
		return nil, fmt.Errorf("根据availableProductID查询产品失败: %v", err)
	}
	return []*model.ProductRules{product}, nil
}

// getAvailableProductsForCustomer 获取客户可用的产品（统一入口）
func (s *RiskService) getAvailableProductsForCustomer(ctx context.Context, customerInfo *model.BusinessAppAccount, riskScore *int) ([]*model.ProductRules, error) {
	// 2. 如果availableProductID不为0，直接使用该产品ID匹配产品
	if customerInfo.AvailableProductID != 0 {
		return s.getProductByAvailableID(customerInfo.AvailableProductID)
	}

	// 3. 根据是否有风控分数决定获取方式
	if riskScore != nil {
		// 根据风控分数获取匹配的产品
		return s.getMatchingProductsByChannelIDAndRiskScore(ctx, uint64(customerInfo.ChannelID), float64(*riskScore))
	} else {
		// 获取渠道下的所有产品
		return s.getAllProductsByChannelID(ctx, uint64(customerInfo.ChannelID))
	}
}

// getMaxCreditByScore 通过风险分数获取对应的最大额度
func (s *RiskService) getMaxCreditByRiskScore(ctx context.Context, customerInfo *model.BusinessAppAccount, evaluation *model.RiskEvaluation) (MaxCredit float64, err error) {
	riskResult, riskScore := adjustRiskResultAndScore(customerInfo, evaluation, false)
	// 检查风险评估结果是否为拒绝，如果为拒绝，额度直接给0，产品也为0
	if evaluation != nil && riskResult == model.REJECTED {
		s.logger.WithFields(
			log.Int("customer_id", evaluation.CustomerID),
			log.Int("risk_result", riskResult),
			log.String("action", "risk_evaluation_rejected"),
		).Info("风控结果为拒绝，额度和产品设置为0")
		return 0, nil

	}
	// 获取可用产品
	products, err := s.getAvailableProductsForCustomer(ctx, customerInfo, &riskScore)
	if err != nil {
		return 0, fmt.Errorf("查询产品失败: %v", err)
	}

	// 计算总授信额度
	maxLoanAmount := s.calculateMaxCreditFromProducts(products)
	s.logger.WithFields(
		log.Float64("从产品中匹配到的最大授信额度", maxLoanAmount),
		log.String("action", "calculate_max_credit"),
	).Info("计算最大授信额度")

	return maxLoanAmount, nil
}

// getCustomerInfo 从数据库获取客户信息
func (s *RiskService) GetCustomerInfo(ctx context.Context, customerID int64) (*model.BusinessAppAccount, error) {
	// 从数据库查询用户信息
	customerInfo, err := s.businessAppAccountService.GetAccountByID(customerID)
	if err != nil {
		return nil, fmt.Errorf("查询账户失败: %v", err)
	}

	return customerInfo, nil
}

// 检查渠道id等于0
func (s *RiskService) checkChannelIDEqualZero(ctx context.Context, customerInfo *model.BusinessAppAccount) *RiskEvaluationResponse {
	if customerInfo.ChannelID == 0 {
		s.logger.WithFields(
			log.Uint64("customer_id", uint64(customerInfo.ID)),
			log.Int("status", customerInfo.Status),
			log.String("action", "user_no_channel_id"),
		).Warn("用户没有渠道id")
		//构造返回结果
		failureType := string(blacklist.ReasonNoChannelID)
		failureReason := "用户没有渠道id"
		// 构造评估记录
		evaluationID := generateEvaluationID(int(customerInfo.ID))
		evaluation := &model.RiskEvaluation{
			EvaluationID:     evaluationID,
			CustomerID:       int(customerInfo.ID),
			RiskScore:        0,
			RiskResult:       model.REJECTED,
			FailureType:      &failureType,
			FailureReason:    &failureReason,
			EvaluationTime:   time.Now(),
			EvaluationSource: model.EvaluationSourceThirdParty,
		}
		s.riskEvalService.CreateEvaluation(evaluation)
		return &RiskEvaluationResponse{
			RiskScore:      0,
			RiskResult:     model.REJECTED,
			FailureType:    &failureType,
			FailureReason:  &failureReason,
			RiskReportID:   evaluationID,
			EvaluationTime: time.Now().Format("2006-01-02 15:04:05"),
		}
	}
	return nil
}

// 检查用户状态是否为2 是否是手动拉黑黑名单
func (s *RiskService) checkBlacklistManually(ctx context.Context, customerInfo *model.BusinessAppAccount) *RiskEvaluationResponse {
	// 检查用户状态是否为2（黑名单）
	if customerInfo.Status == model.StatusTypeBlacklist {
		s.logger.WithFields(
			log.Uint64("customer_id", uint64(customerInfo.ID)),
			log.Int("status", customerInfo.Status),
			log.String("action", "user_manually_blacklisted"),
		).Warn("用户状态为手动拉黑黑名单")
		//构造返回结果
		failureType := string(blacklist.ReasonBlacklist)
		failureReason := "用户状态为手动拉黑黑名单"

		// 构造评估记录
		evaluationID := generateEvaluationID(int(customerInfo.ID))
		evaluation := &model.RiskEvaluation{
			EvaluationID:     evaluationID,
			CustomerID:       int(customerInfo.ID),
			RiskScore:        0,
			RiskResult:       model.REJECTED,
			FailureType:      &failureType,
			FailureReason:    &failureReason,
			EvaluationTime:   time.Now(),
			EvaluationSource: model.EvaluationSourceThirdParty,
		}

		s.riskEvalService.CreateEvaluation(evaluation)
		return &RiskEvaluationResponse{
			RiskScore:      0,
			RiskResult:     model.REJECTED,
			FailureType:    &failureType,
			FailureReason:  &failureReason,
			RiskReportID:   evaluationID,
			EvaluationTime: time.Now().Format("2006-01-02 15:04:05"),
		}
	}
	return nil
}

// checkRiskCoreExternalBlacklist 检查外部风险核心黑名单
func (s *RiskService) checkRiskCoreExternalBlacklist(ctx context.Context, customerInfo *model.BusinessAppAccount) *RiskEvaluationResponse {
	// 记录开始检查外部黑名单
	s.logger.WithFields(
		log.String("action", "check_external_blacklist_start"),
		log.String("id_card", customerInfo.IDCard),
		log.String("customer_name", customerInfo.Name),
	).Info("开始检查外部风险核心黑名单")

	// 调用风控模型服务的黑名单检查接口
	blacklistResponse, err := s.riskModelService.CallBlacklistCheckAPI(ctx, customerInfo.IDCard)
	if err != nil {
		// 记录调用失败日志
		s.logger.WithFields(
			log.String("action", "check_external_blacklist_failed"),
			log.String("error_type", "api_call_failed"),
			log.String("id_card", customerInfo.IDCard),
			log.String("customer_name", customerInfo.Name),
			log.ErrorField(err),
		).Error("调用外部风险核心黑名单检查接口失败")
		return nil // 接口调用失败时不阻断流程
	}

	// 检查是否命中黑名单
	if blacklistResponse.Code == riskmodelservice.SuccessRiskModel && blacklistResponse.Data != nil && blacklistResponse.Data.IsBlacklisted {
		// 记录命中黑名单日志
		s.logger.WithFields(
			log.String("action", "external_blacklist_hit"),
			log.String("id_card", customerInfo.IDCard),
			log.String("customer_name", customerInfo.Name),
			log.String("risk_msg", blacklistResponse.Data.RiskMsg),
		).Warn("用户命中外部风险核心黑名单")

		// 构造返回结果
		failureReason := blacklistResponse.Data.RiskMsg
		if failureReason == "" {
			failureReason = "用户在外部风险核心黑名单"
		}
		failureType := riskmodelservice.FailureTypeRiskCoreBlacklist

		// 构造评估记录
		evaluationID := generateEvaluationID(int(customerInfo.ID))
		evaluation := &model.RiskEvaluation{
			EvaluationID:     evaluationID,
			CustomerID:       int(customerInfo.ID),
			RiskScore:        model.MinRiskScore,
			RiskResult:       model.REJECTED,
			FailureType:      &failureType,
			FailureReason:    &failureReason,
			EvaluationTime:   time.Now(),
			EvaluationSource: model.EvaluationSourceThirdParty,
		}

		s.riskEvalService.CreateEvaluation(evaluation)
		return &RiskEvaluationResponse{
			RiskReportID:         evaluationID,
			RiskScore:            model.MinRiskScore,
			RiskResult:           model.REJECTED,
			AvailableCreditLimit: 0,
			EvaluationTime:       time.Now().Format("2006-01-02 15:04:05"),
			FailureType:          &failureType,
			FailureReason:        &failureReason,
		}
	}

	// 记录检查通过日志
	s.logger.WithFields(
		log.String("action", "external_blacklist_pass"),
		log.String("id_card", customerInfo.IDCard),
		log.String("customer_name", customerInfo.Name),
		log.String("blacklist_status", "not_blacklisted"),
	).Info("外部黑名单检查通过")

	return nil // 未命中黑名单，继续后续流程
}
