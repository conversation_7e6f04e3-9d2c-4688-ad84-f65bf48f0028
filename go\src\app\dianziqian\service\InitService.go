package service

import (
	<PERSON>an<PERSON><PERSON><PERSON>_Config "fincore/app/dianziqian/config"
	"fincore/global"
	"fincore/model"
	"fincore/utils/utilstool/goredis"
	"time"

	"github.com/dromara/carbon/v2"
)

/* gosdk中的硬编码
const(
	host  ="https://prev.asign.cn/" //测试环境 "https://oapi.asign.cn/" //正式环境
	appId="***"
	privateKey="MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAmt2jO6Z5uwRFM+bdRBXoHcjpdWpUDvwI05py3kZvfcg7ahUJkz4cQvCUaLVMFvH4LyefiA5ER2q7S87653yYMQIDAQABAkBzD4Eb7JA89utDqJ902qHen0t1RU6242LbdMErjEGBvXjzOvLpigUaB8gSR+o1r+damPQJdZWbR4+8EBvK1FORAiEA9EgQVjzmhFXEjsCLo/gWlbnjQhyByV7ZEotosBlm/bsCIQCiS32HTC4AwvBT1gzyr11zWmjxizYtYwV6NryvUE1tAwIgQjh+5UHhI6K0hBZCRJLuXGxl5PghXtttcQ+Fs6dPOh0CIGAq/10WpQPKf4IOCmobw/JAloLajOXkETDUEoaHvPllAiAsYPUVC4fmNW53YeDJiCcQ9yl/WbH6mktfzVl011KBcA=="
)
*/

func InitService() {
	config := Dianziqian_Config.GetInitConfig()
	host := global.App.Config.Aiqian_envs.Pro_host
	app_id := global.App.Config.Aiqian_envs.App_id
	privateKey := global.App.Config.Aiqian_envs.PrivateKey
	config.SetHost(host)
	config.SetAppId(app_id)
	config.SetPrivateKey(privateKey)
	// 初始化 carbon 包时区
	carbon.SetTimezone(carbon.PRC)
	// 初始化 time 包时区
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		loc = time.FixedZone("CST", 8*60*60) // 备选方案
	}
	time.Local = loc
	// 初始化 redis
	global.App.Log.Info("初始化 redis 连接")
	goredis.InitRedisClient()

	appName := "fincore"
	if global.App.Config.App.AppName != "" {
		appName = global.App.Config.App.AppName
	}
	model.MyInit(appName) //初始化数据
}
