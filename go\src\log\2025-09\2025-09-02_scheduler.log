{"level":"dev.info","ts":"[2025-09-02 15:21:06.212]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.215]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.215]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.215]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.219]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.220]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.221]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.221]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.222]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.222]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.222]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.222]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.222]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.223]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.223]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.223]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.223]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:21:06.223]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.583]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.584]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.584]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.584]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.587]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.588]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.589]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.590]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.590]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.591]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.591]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.591]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.591]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.592]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.592]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.592]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.592]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:22:24.592]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.124]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.126]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.126]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.126]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.129]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.130]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.131]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.131]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.131]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.131]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.132]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.132]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.132]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.132]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.132]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.132]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.132]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:22:41.132]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:23:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:23:00.042]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0403135,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 15:23:00.042]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.352]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.353]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.353]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.353]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.355]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:32:47.356]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:33:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:33:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:33:00.024]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0234997,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 15:33:00.024]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:33:00.206]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.2058588,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 15:33:00.206]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.452]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.454]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.454]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.454]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.458]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:34:12.459]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.466]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.469]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.469]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.469]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.471]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.472]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.473]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.473]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.473]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.473]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.473]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.473]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:34:46.473]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.209]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.210]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.210]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.210]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.213]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.213]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.213]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.213]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.213]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.213]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.213]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.214]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.215]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.215]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.215]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.215]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.215]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.216]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.216]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.217]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.217]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.217]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.217]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:36:09.217]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.206]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.208]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.208]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.208]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.211]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.212]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.213]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.213]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.213]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.213]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.213]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.213]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.214]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.214]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.214]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:36:54.214]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:37:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.error","ts":"[2025-09-02 15:37:00.000]","caller":"engine/executor.go:202","msg":"任务执行发生panic","task_name":"refund-status-sync-compensation","panic":"runtime error: invalid memory address or nil pointer dereference","stack":"goroutine 30 [running]:\nruntime/debug.Stack()\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/debug/stack.go:26 +0x6b\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:205 +0x2da\npanic({0x229ca60?, 0x3c3f7f0?})\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792 +0x136\nfincore/utils/gform.(*Engin).GetExecuteDB(0xc00009b9c0)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114 +0x2b\nfincore/utils/gform.NewSession({0x32ecae0, 0xc00009b9c0})\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38 +0xa5\nfincore/utils/gform.NewOrm({0x32ecae0, 0xc00009b9c0})\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22 +0x45\nfincore/utils/gform.(*Engin).NewOrm(0xc00009b9c0)\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243 +0x2a\nfincore/model.DB({0xc000f402e8, 0x1, 0x1})\n\tD:/work/code/fincore/go/src/model/model.go:58 +0x53\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions(0xc000d8ca20)\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174 +0xf6\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute(0xc000d8ca20, {0x32eac98, 0xc000858d20})\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59 +0x395\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt(0xc0001122c0, 0xc000864600)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222 +0x5a5\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry(0xc0001122c0, 0xc000864600)\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144 +0x5ff\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask(0xc0001122c0, {0x32ee388, 0xc000d8ca20})\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92 +0x4cc\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1()\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194 +0x33e\nreflect.Value.call({0x21f9780, 0xc000c84240, 0x13}, {0x245a954, 0x4}, {0x3cec2e0, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 +0xec5\nreflect.Value.Call({0x21f9780, 0xc000c84240, 0x13}, {0x3cec2e0, 0x0, 0x0})\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 +0xb3\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams({0x21f9780, 0xc000c84240}, {0x0, 0x0, 0x0})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28 +0x2e5\ngithub.com/go-co-op/gocron/v2.(*executor).runJob(_, {{0x32eac28, 0xc000d182d0}, {0x32eac28, 0xc000130280}, 0xc00108c470, {0xc6, 0xbe, 0xdf, 0x93, ...}, ...}, ...)\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432 +0xf97\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1({{0x32eac28, 0xc000d182d0}, {0x32eac28, 0xc000130280}, 0xc00108c470, {0xc6, 0xbe, 0xdf, 0x93, 0x72, ...}, ...})\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228 +0x8f\ncreated by github.com/go-co-op/gocron/v2.(*executor).start.func1 in goroutine 29\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:227 +0xa29\n","stacktrace":"fincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:202\nruntime.gopanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:792\nruntime.panicmem\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/panic.go:262\nruntime.sigpanic\n\tC:/Users/<USER>/scoop/apps/go/current/src/runtime/signal_windows.go:401\nfincore/utils/gform.(*Engin).GetExecuteDB\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:114\nfincore/utils/gform.NewSession\n\tD:/work/code/fincore/go/src/utils/gform/session.go:38\nfincore/utils/gform.NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/orm.go:22\nfincore/utils/gform.(*Engin).NewOrm\n\tD:/work/code/fincore/go/src/utils/gform/engin.go:243\nfincore/model.DB\n\tD:/work/code/fincore/go/src/model/model.go:58\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).getPendingRefundTransactions\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:174\nfincore/app/scheduler/tasks/orders.(*RefundStatusSyncCompensationTask).Execute\n\tD:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:59\nfincore/app/scheduler/engine.(*TaskExecutor).executeSingleAttempt\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:222\nfincore/app/scheduler/engine.(*TaskExecutor).executeWithRetry\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:144\nfincore/app/scheduler/engine.(*TaskExecutor).ExecuteTask\n\tD:/work/code/fincore/go/src/app/scheduler/engine/executor.go:92\nfincore/app/scheduler/engine.(*ScheduleEngine).createJobFunc.func1\n\tD:/work/code/fincore/go/src/app/scheduler/engine/scheduler.go:194\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\ngithub.com/go-co-op/gocron/v2.callJobFuncWithParams\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/util.go:28\ngithub.com/go-co-op/gocron/v2.(*executor).runJob\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:432\ngithub.com/go-co-op/gocron/v2.(*executor).start.func1.1\n\tC:/Users/<USER>/go/pkg/mod/github.com/go-co-op/gocron/v2@v2.16.2/executor.go:228"}
{"level":"dev.info","ts":"[2025-09-02 15:37:00.001]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.001342,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 15:37:00.001]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.302]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.304]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.304]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.304]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.309]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.309]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.309]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.309]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.309]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.309]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.310]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.311]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.311]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.312]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.312]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.312]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.312]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.312]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.312]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.312]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.312]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.313]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:39:21.313]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.251]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.252]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.252]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.252]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.256]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.257]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.257]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.257]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.257]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.257]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.258]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.258]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.258]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.258]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.258]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.259]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.259]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.259]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:40:06.259]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.968]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.969]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.969]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.969]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.970]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.972]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.973]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.973]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.973]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.973]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:40:40.973]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 15:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:41:00.025]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0248943,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 15:41:00.025]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.110]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":4,"duration":0.11028,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.110]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.111]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.1107902,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.111]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.137]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.1362301,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.137]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.152]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.1518486,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.152]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.109]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.110]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.110]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.110]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.112]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.113]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.114]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.114]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.114]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.114]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.114]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.114]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 15:43:27.114]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.003]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.006]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.006]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.006]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.008]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.008]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.009]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.010]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.010]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.010]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.010]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.010]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.010]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.011]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.011]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.011]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.011]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.011]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 16:08:41.011]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 16:09:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:09:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:09:00.039]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0388236,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:09:00.039]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:09:00.158]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.157162,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:09:00.158]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.056]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.0560565,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.056]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.056]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":5,"duration":0.0557445,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.056]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.056]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":4,"duration":0.0557445,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.056]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.418]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.420]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.420]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.420]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.422]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.422]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.423]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.424]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.424]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.424]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.424]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.424]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.425]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.425]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.425]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.425]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.425]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.425]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 16:14:52.425]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 16:15:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:15:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:15:00.034]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":1,"duration":0.0337539,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:15:00.034]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:15:00.172]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":2,"duration":0.1726705,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:15:00.172]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.315]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.317]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.317]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.317]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.322]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.323]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.323]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.323]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.323]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.323]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.324]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.324]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.324]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.324]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.324]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 16:17:20.324]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.875]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.876]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.876]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.876]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.878]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.879]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.879]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.879]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.879]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.880]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.880]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.880]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.880]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.880]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.880]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.880]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 16:25:42.880]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.035]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.0347047,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.035]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.105]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.104769,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.105]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.126]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":1,"duration":0.125622,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.126]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.977]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.978]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.978]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.978]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.981]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 16:27:19.982]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.089]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.0894947,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.089]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.096]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0961659,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.096]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":1,"duration":0.0961659,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.096]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.096]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.633]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.637]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.637]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.637]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.640]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.643]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.643]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.643]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.643]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.643]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.644]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.644]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.644]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.644]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.644]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.644]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 22:43:31.644]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.716]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.719]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.719]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.719]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.722]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.723]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.723]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.723]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.723]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.723]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.723]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.723]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.724]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.724]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.724]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.724]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 23:33:44.724]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.045]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.0444103,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.045]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.092]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":3,"duration":0.0908636,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.092]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.092]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":1,"duration":0.0920056,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.092]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.586]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.587]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.587]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.587]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.589]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.589]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.589]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.589]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.590]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.591]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.591]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.591]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.591]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.591]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.592]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.592]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.592]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 23:38:14.592]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
{"level":"dev.info","ts":"[2025-09-02 23:39:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:39:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:39:00.037]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":2,"duration":0.037077,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:39:00.037]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:39:00.112]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":1,"duration":0.111819,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:39:00.113]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.065]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":3,"duration":0.0648069,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.065]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.072]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":5,"duration":0.0714964,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.072]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.084]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":4,"duration":0.0840543,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.084]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:41:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:41:00.066]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":6,"duration":0.0666675,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:41:00.066]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":10,"duration":0.0666087,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":7,"duration":0.066882,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":9,"duration":0.0672828,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.103]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":8,"duration":0.1037103,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.103]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:43:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:43:00.057]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":11,"duration":0.0568345,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:43:00.057]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.001]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.002]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.002]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.099]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":13,"duration":0.0969312,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.099]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.099]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":14,"duration":0.097453,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.099]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.099]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":12,"duration":0.0985284,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.099]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:45:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:45:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:45:00.071]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":15,"duration":0.0707353,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:45:00.071]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:45:00.107]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"auto-disbursement-compensation","job_id":16,"duration":0.107427,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:45:00.107]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"auto-disbursement-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.000]","caller":"engine/scheduler.go:190","msg":"开始执行任务","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.051]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation","job_id":17,"duration":0.0512623,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.051]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"disbursement-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.077]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"refund-status-sync-compensation","job_id":18,"duration":0.0769659,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.077]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"refund-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.078]","caller":"engine/executor.go:365","msg":"任务执行成功","task_name":"repayment-status-sync-compensation","job_id":19,"duration":0.0774726,"retry_count":0,"success":true}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.078]","caller":"engine/scheduler.go:200","msg":"任务执行成功","task_name":"repayment-status-sync-compensation"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.005]","caller":"scheduler/scheduler.go:31","msg":"正在初始化调度器模块"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.006]","caller":"scheduler/manager.go:106","msg":"调度器管理器创建成功","scheduler_name":"fincore-scheduler-dev","timezone":"Asia/Shanghai","max_concurrent_jobs":5}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.006]","caller":"scheduler/scheduler.go:224","msg":"正在注册内置任务"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.006]","caller":"scheduler/scheduler.go:233","msg":"没有内置任务需要注册"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.008]","caller":"registry/discovery.go:47","msg":"开始自动发现任务","task_count":9}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.008]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-disbursement-compensation","description":"自动放款补偿任务 - 处理创建订单后自动放款异常或失败的补偿","schedule":"0 */3 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.009]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-disbursement-compensation","task_type":"orders.AutoDisbursementCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.009]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"disbursement-status-sync-compensation","description":"放款状态同步补偿任务 - 处理订单状态为待放款但已有已提交放款流水的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"disbursement-status-sync-compensation","task_type":"orders.DisbursementStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"refund-status-sync-compensation","description":"退款状态同步补偿任务 - 处理已提交退款流水的状态同步","schedule":"0 */1 * * * *","concurrency_mode":"singleton","timeout":300,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"refund-status-sync-compensation","task_type":"orders.RefundStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"auto-withhold-task","description":"系统自动代扣任务 - 自动处理待代扣账单","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton","timeout":1800,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"auto-withhold-task","task_type":"repayment.AutoWithholdTask"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"repayment-status-sync-compensation","description":"还款状态同步补偿任务 - 处理还款账单状态与支付流水状态不一致的补偿场景","schedule":"0 */2 * * * *","concurrency_mode":"singleton","timeout":600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"repayment-status-sync-compensation","task_type":"repayment.RepaymentStatusSyncCompensationTask"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-ahead","description":"账单到期提前通知任务 - 处理账单到期前一天，短信通知客户","schedule":"0 0 9 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-ahead","task_type":"notice.BillNoticeAheadTask"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"bill-notice-overdue","description":"账单逾期通知任务","schedule":"0 0 10 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"bill-notice-overdue","task_type":"notice.BillNoticeOverdueTask"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"available_quota_notice","description":"额度未使用通知任务","schedule":"0 0 11 * * *","concurrency_mode":"singleton","timeout":3600,"retry_count":3}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"available_quota_notice","task_type":"notice.AvailableQuotaNoticeTask"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.010]","caller":"registry/registry.go:68","msg":"任务注册成功","task_name":"channel_statistics_task","description":"渠道统计任务：统计各渠道的新用户数、实名通过数、成交数","schedule":"0 0 */2 * * *","concurrency_mode":"singleton","timeout":600,"retry_count":2}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.011]","caller":"registry/discovery.go:73","msg":"任务发现并注册成功","task_name":"channel_statistics_task","task_type":"statistics.ChannelStatisticsTask"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.011]","caller":"registry/discovery.go:80","msg":"任务自动发现完成","total":9,"success":9,"failure":0}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.011]","caller":"scheduler/scheduler.go:51","msg":"调度器模块初始化成功"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.011]","caller":"scheduler/scheduler.go:61","msg":"正在启动调度器"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.011]","caller":"scheduler/manager.go:124","msg":"正在启动调度器管理器"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.011]","caller":"engine/scheduler.go:72","msg":"正在启动调度引擎"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.011]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"refund-status-sync-compensation","schedule":"0 */1 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.012]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"available_quota_notice","schedule":"0 0 11 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.014]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-ahead","schedule":"0 0 9 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.014]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"channel_statistics_task","schedule":"0 0 */2 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.015]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"disbursement-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.015]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"repayment-status-sync-compensation","schedule":"0 */2 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.016]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-disbursement-compensation","schedule":"0 */3 * * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.016]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"auto-withhold-task","schedule":"0 0 0,8,15,20 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.017]","caller":"engine/scheduler.go:163","msg":"任务调度注册成功","task_name":"bill-notice-overdue","schedule":"0 0 10 * * *","concurrency_mode":"singleton"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.017]","caller":"engine/scheduler.go:83","msg":"调度引擎启动成功","registered_jobs":9,"timezone":"Asia/Shanghai"}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.017]","caller":"scheduler/manager.go:139","msg":"调度器管理器启动成功","scheduler_name":"fincore-scheduler-dev","registered_tasks":9}
{"level":"dev.info","ts":"[2025-09-02 23:50:07.017]","caller":"scheduler/scheduler.go:68","msg":"调度器启动成功"}
