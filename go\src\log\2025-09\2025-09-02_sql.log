{"level":"dev.info","ts":"[2025-09-02 15:23:00.041]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"37.6919ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-02 15:33:00.022]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"19.6364ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 15:33:00.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"124.0205ms","duration_ms":124}
{"level":"dev.info","ts":"[2025-09-02 15:33:00.205]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"77.8883ms","duration_ms":77}
{"level":"dev.info","ts":"[2025-09-02 15:41:00.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"23.7878ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.110]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902154200","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"110.28ms","duration_ms":110}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.110]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902154200","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"107.125ms","duration_ms":107}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.110]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902154200","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"107.125ms","duration_ms":107}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.110]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902154200","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"106.581ms","duration_ms":106}
{"level":"dev.info","ts":"[2025-09-02 15:42:00.152]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"41.0584ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.624]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,accountID,businessID,password,salt,name,username,dept_id,remark,(select role_id from business_auth_role_access where uid = id ) as role_id FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"17.4601ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.658]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_account` SET `lastLoginTime` = ?,`lastLoginIp` = ?,`loginstatus` = ? WHERE `id` = ?, [**********  1 1]","duration":"16.9867ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.676]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"18.5237ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.696]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"18.9792ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.712]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `admin_operate_log` (`result`,`detail`,`ip`,`region`,`uid`,`operate_name`,`created_at`) VALUES (?,?,?,?,?,?,?), [成功 成功  未知 1 登录 2025-09-02 15:42:04.6973765 +0800 CST m=+83.*********]","duration":"14.7583ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.768]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"26.3551ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.768]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"25.8245ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.798]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"29.2647ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.818]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"20.5577ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.861]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"43.2476ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.880]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"18.7921ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.897]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"16.4885ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.915]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"17.3005ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.938]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"22.8545ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 15:42:04.961]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"23.628ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.016]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"54.7613ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.033]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"15.9279ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.054]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"21.1501ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.084]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 474]","duration":"29.884ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.102]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"17.9958ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"28.5677ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.161]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"29.6794ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.179]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"18.0174ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"27.6313ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.226]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"18.4838ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.253]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"27.7006ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.289]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"35.6813ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.307]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"17.7672ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.332]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"25.226ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.355]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"22.7425ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.377]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"21.7013ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.397]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"19.9749ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.413]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"15.965ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.450]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"36.1944ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.469]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"19.4514ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.487]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"17.4767ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.511]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"24.0223ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.541]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 472]","duration":"30.4537ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.584]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 473]","duration":"42.6012ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.619]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"35.5775ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.639]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"19.8535ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.657]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"18.3298ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.676]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"18.7017ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.696]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"19.9579ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.710]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"14.1075ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.729]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"15.2488ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.748]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"18.973ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.768]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"19.7198ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.804]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"36.3276ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.847]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"41.701ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.859]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"12.2749ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-09-02 15:42:05.894]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"35.5284ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.807]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"54.1133ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.807]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"54.1133ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.807]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"54.1133ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.808]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"54.0888ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.861]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"54.1561ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.861]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"53.6498ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.861]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"53.6498ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.872]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"22.4952ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.872]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-09-02]","duration":"118.0916ms","duration_ms":118}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.882]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"127.8224ms","duration_ms":127}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.882]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"127.0086ms","duration_ms":127}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.882]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"20.6225ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.883]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"127.5991ms","duration_ms":127}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.883]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ecc53a6da6","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"128.1077ms","duration_ms":128}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.883]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ec62d7a498","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"128.1077ms","duration_ms":128}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.889]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ec62d7a498","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-09-02]","duration":"16.9455ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.889]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d993ec62d7a498","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"16.9455ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.899]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d16a78b4ceb558","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` LIMIT 1, []","duration":"15.6125ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.899]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d16a78b4ceb558","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"16.1231ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-02 15:42:06.920]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616750e2d16a78b4ceb558","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"30.304ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 15:42:08.788]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"26.764ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-02 15:42:08.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"56.7955ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-09-02 15:42:08.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"56.7955ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-09-02 15:42:08.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"57.3008ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-09-02 15:42:08.820]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ?, [1]","duration":"58.1127ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-09-02 15:42:08.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"57.3008ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-09-02 15:42:08.841]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"52.7177ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-09-02 15:42:08.847]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 20 OFFSET 0, [1]","duration":"27.3718ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 15:42:09.805]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"SELECT id, name FROM `business_account` WHERE `status` = ?, [0]","duration":"18.1707ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:09.805]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167515aa3c5a085f081f2","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 ORDER BY baa.createtime DESC) as count_query, []","duration":"18.1707ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 15:42:09.868]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ?, [1]","duration":"62.6081ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-09-02 15:42:09.887]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, []","duration":"82.0381ms","duration_ms":82}
{"level":"dev.info","ts":"[2025-09-02 15:42:09.927]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, MAX(CASE WHEN paid_at IS NOT NULL THEN UNIX_TIMESTAMP(paid_at) ELSE NULL END) as last_repay_time, MIN(CASE WHEN status = 0 THEN UNIX_TIMESTAMP(due_date) ELSE NULL END) as bill_due_time FROM `business_repayment_bills` WHERE `user_id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) GROUP BY user_id, [31 30 29 28 26 25 24 23 22 21 20 19 18 17 16 15 14 13 12 11]","duration":"39.6071ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 15:42:09.928]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) GROUP BY user_id, [31 30 29 28 26 25 24 23 22 21 20 19 18 17 16 15 14 13 12 11]","duration":"40.1937ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-09-02 15:42:11.086]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ? ORDER BY channel_name ASC, [1]","duration":"45.5761ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-09-02 15:42:11.087]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"46.6188ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-09-02 15:42:11.123]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"35.3496ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 15:42:11.149]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"25.4324ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-02 15:42:11.185]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"35.773ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 15:42:11.704]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"23.6353ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 15:42:11.713]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"33.0911ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 15:42:11.744]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"39.924ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 15:42:11.763]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tbrb.order_id as order_id,\n\t\tblo.order_no as order_no,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tpr.total_periods as total_periods,\n\t\t(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE brb.user_id = business_loan_orders.user_id) as repeat_buy_num FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? ORDER BY brb.id DESC LIMIT 20, [2025-09-02 2025-09-02]","duration":"48.6118ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-09-02 15:42:12.180]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"132.9502ms","duration_ms":132}
{"level":"dev.info","ts":"[2025-09-02 15:42:12.180]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) LIMIT 1, [2025-09-01 2025-09-01]","duration":"132.9502ms","duration_ms":132}
{"level":"dev.info","ts":"[2025-09-02 15:42:12.211]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"30.8397ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 15:42:12.211]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tblo.order_no as order_no,\n\t\tbrb.order_id as order_id,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbaa.idCard as id_card,\n\t\tFROM_UNIXTIME(baa.lastLoginTime, '%Y-%m-%d %H:%i:%s') as last_login_time,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tbrb.due_principal as due_principal,\n\t\tbrb.total_due_amount - brb.paid_amount as diff_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tDATE_FORMAT(brb.created_at,  '%Y-%m-%d %H:%i:%s') as created_at,\n\t\tpr.total_periods as total_periods,\n\t\tIFNULL((SELECT region FROM user_operate_log WHERE uid = brb.user_id ORDER BY created_at DESC LIMIT 1 ), '未知' ) as region,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id AND business_repayment_bills.status in (1,2)) as paid_periods FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) ORDER BY brb.id DESC LIMIT 20, [2025-09-01 2025-09-01]","duration":"31.345ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-02 15:42:13.421]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `product_rules` LIMIT 1, []","duration":"33.2902ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 15:42:13.454]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id, rule_name, loan_amount, loan_period, total_periods, guarantee_fee, annual_interest_rate, other_fees, rule_category FROM `product_rules` ORDER BY id desc LIMIT 10, []","duration":"32.365ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 15:42:14.708]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616752b9f1ccf47304ecd0","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?,?,?), [1 0 3 7 9]","duration":"51.4645ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-09-02 15:42:14.708]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18616752b9f1ccf47304ecd0","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?), [1 3 9]","duration":"51.4645ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-09-02 15:42:16.572]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167532b0354d023324e70","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"19.4464ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 15:42:16.572]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167532b0354d023324e70","sql":"SELECT COALESCE(SUM(bpt.amount), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT bpt.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(bpt.completed_at) = CURDATE() THEN bpt.amount ELSE 0 END), 0) as today_expense FROM business_payment_transactions bpt LEFT JOIN business_app_account baa ON bpt.user_id = baa.id  INNER JOIN business_loan_orders blo ON bpt.order_id = blo.id WHERE bpt.status = ? and bpt.type = ? and (bpt.completed_at IS NOT NULL) LIMIT 1, [2 DISBURSEMENT]","duration":"19.4464ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 15:42:16.572]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167532b0354d023324e70","sql":"SELECT count(*) as count FROM business_payment_transactions bpt LEFT JOIN business_app_account baa ON bpt.user_id = baa.id  INNER JOIN business_loan_orders blo ON bpt.order_id = blo.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE bpt.status = ? and bpt.type = ? and (bpt.completed_at IS NOT NULL) ORDER BY bpt.id DESC LIMIT 1, [2 DISBURSEMENT]","duration":"19.4464ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 15:42:16.602]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167532b0354d023324e70","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"30.1154ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 15:42:16.617]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167532b0354d023324e70","sql":"SELECT blo.order_no,bpt.amount,baa.name as user_name,baa.mobile,bbc.bank_card_no,DATE_FORMAT(bpt.completed_at, '%Y-%m-%d %H:%i:%s') AS completed_at,bpt.user_id FROM business_payment_transactions bpt LEFT JOIN business_app_account baa ON bpt.user_id = baa.id  INNER JOIN business_loan_orders blo ON bpt.order_id = blo.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE bpt.status = ? and bpt.type = ? and (bpt.completed_at IS NOT NULL) ORDER BY bpt.id DESC LIMIT 20, [2 DISBURSEMENT]","duration":"44.4786ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-09-02 15:42:16.617]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186167532b0354d023324e70","sql":"SELECT bpt.user_id,SUM(bpt.amount) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = bpt.user_id) as user_order_count FROM business_payment_transactions bpt LEFT JOIN business_app_account baa ON bpt.user_id = baa.id  INNER JOIN business_loan_orders blo ON bpt.order_id = blo.id WHERE bpt.status = ? and bpt.type = ? and (bpt.completed_at IS NOT NULL) GROUP BY bpt.user_id, [2 DISBURSEMENT]","duration":"44.9841ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-09-02 15:42:17.111]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861675340c8d6002c0ae452","sql":"SELECT count(*) as count FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?,?) LIMIT 1, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT REFUND]","duration":"191.7805ms","duration_ms":191}
{"level":"dev.info","ts":"[2025-09-02 15:42:17.111]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861675340c8d6002c0ae452","sql":"SELECT \n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,\n\t\tSUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) = rb.due_date THEN pt.amount ELSE 0 END) as due_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) < rb.due_date THEN pt.amount ELSE 0 END) as early_income,\n\t\tCOUNT(DISTINCT pt.user_id) as total_customers,\n\t\tCOUNT(DISTINCT pt.order_id) as total_orders\n\t FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?,?) LIMIT 1, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT REFUND]","duration":"192.289ms","duration_ms":192}
{"level":"dev.info","ts":"[2025-09-02 15:42:17.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861675340c8d6002c0ae452","sql":"SELECT \n\t\t\tpt.id,\n\t\t\tpt.transaction_no,\n\t\t\tlo.order_no,\n\t\t\taa.name as user_name,\n\t\t\taa.mobile,\n\t\t\tpt.type,\n\t\t\tpt.withhold_type,\n\t\t\tpt.offline_payment_channel_detail,\n\t\t\tpt.amount,\n\t\t\trb.period_number,\n\t\t\tpt.completed_at,\n\t\t\trb.due_date\n\t\t FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?,?) ORDER BY pt.id DESC LIMIT 20, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT REFUND]","duration":"95.8257ms","duration_ms":95}
{"level":"dev.info","ts":"[2025-09-02 16:09:00.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"35.6511ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 16:09:00.133]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"129.1259ms","duration_ms":129}
{"level":"dev.info","ts":"[2025-09-02 16:09:00.157]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"23.8638ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 16:09:37.202]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,accountID,businessID,password,salt,name,username,dept_id,remark,(select role_id from business_auth_role_access where uid = id ) as role_id FROM `business_account` WHERE (`username` = ? or `email` = ?) and `mobile` = ? LIMIT 1, [fincore fincore ***********]","duration":"833.7991ms","duration_ms":833}
{"level":"dev.info","ts":"[2025-09-02 16:09:37.534]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"UPDATE `business_account` SET `lastLoginTime` = ?,`lastLoginIp` = ?,`loginstatus` = ? WHERE `id` = ?, [**********  1 1]","duration":"76.1353ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.004]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"469.8581ms","duration_ms":469}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.141]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"137.0571ms","duration_ms":137}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.444]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"INSERT INTO `admin_operate_log` (`uid`,`operate_name`,`created_at`,`result`,`detail`,`ip`,`region`) VALUES (?,?,?,?,?,?,?), [1 登录 2025-09-02 16:09:38.1421105 +0800 CST m=+57.********* 成功 成功  未知]","duration":"301.8059ms","duration_ms":301}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.539]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"77.3339ms","duration_ms":77}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.658]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"197.5217ms","duration_ms":197}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.673]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"133.4146ms","duration_ms":133}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.690]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"31.1601ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"129.2283ms","duration_ms":129}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.874]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"53.7648ms","duration_ms":53}
{"level":"dev.info","ts":"[2025-09-02 16:09:38.978]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"104.8347ms","duration_ms":104}
{"level":"dev.info","ts":"[2025-09-02 16:09:39.042]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"63.5339ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-09-02 16:09:39.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"49.8502ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-09-02 16:09:39.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"39.0942ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 16:09:39.180]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"48.4975ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-09-02 16:09:39.228]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"48.3852ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-09-02 16:09:39.560]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"331.4234ms","duration_ms":331}
{"level":"dev.info","ts":"[2025-09-02 16:09:39.627]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 474]","duration":"67.0927ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-09-02 16:09:39.706]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"79.3797ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-09-02 16:09:39.828]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"121.5782ms","duration_ms":121}
{"level":"dev.info","ts":"[2025-09-02 16:09:40.032]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"204.3366ms","duration_ms":204}
{"level":"dev.info","ts":"[2025-09-02 16:09:40.103]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"71.1507ms","duration_ms":71}
{"level":"dev.info","ts":"[2025-09-02 16:09:40.170]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"66.5564ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-09-02 16:09:40.213]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"42.8675ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-09-02 16:09:40.280]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"66.9594ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-09-02 16:09:40.321]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"41.2894ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-09-02 16:09:40.371]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"49.8095ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-09-02 16:09:40.800]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"429.3185ms","duration_ms":429}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.140]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"339.3034ms","duration_ms":339}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.209]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"68.4153ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.247]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"38.0096ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.350]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"103.1953ms","duration_ms":103}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"27.8896ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.458]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"79.8492ms","duration_ms":79}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.501]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"42.8127ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.538]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"37.3847ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.567]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 472]","duration":"27.9495ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.597]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 473]","duration":"29.8598ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.681]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"84.088ms","duration_ms":84}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.767]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"85.6732ms","duration_ms":85}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.844]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"76.3627ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-09-02 16:09:41.923]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"78.5834ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-09-02 16:09:42.052]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"128.5722ms","duration_ms":128}
{"level":"dev.info","ts":"[2025-09-02 16:09:42.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"27.4351ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 16:09:42.107]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"27.4454ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 16:09:42.147]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"38.7024ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 16:09:42.219]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"72.2734ms","duration_ms":72}
{"level":"dev.info","ts":"[2025-09-02 16:09:42.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"47.099ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-02 16:09:42.280]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"13.6609ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-02 16:09:42.316]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"35.6318ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 16:09:42.333]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"17.3038ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.204]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"62.7463ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.259]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"55.1389ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.318]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"176.8286ms","duration_ms":176}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.318]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"177.357ms","duration_ms":177}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.318]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"175.7267ms","duration_ms":175}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.318]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"175.1984ms","duration_ms":175}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.319]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"177.3307ms","duration_ms":177}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.320]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"177.2235ms","duration_ms":177}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.327]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-09-02]","duration":"184.2864ms","duration_ms":184}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.328]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"184.7928ms","duration_ms":184}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.328]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"184.7928ms","duration_ms":184}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.359]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"39.8679ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.359]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"39.4702ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.390]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-09-02]","duration":"62.8316ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.391]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"71.4968ms","duration_ms":71}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.391]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"63.3992ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.547]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"186.7627ms","duration_ms":186}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.547]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b377e50df7732a6","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"186.7627ms","duration_ms":186}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.590]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"43.7648ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-02 16:09:43.590]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` LIMIT 1, []","duration":"43.7648ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-02 16:09:47.570]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"33.7264ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 16:09:47.570]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ? ORDER BY channel_name ASC, [1]","duration":"33.7264ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 16:09:47.622]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"50.9119ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-09-02 16:09:47.658]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"35.5726ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 16:09:47.679]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"21.0027ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 16:09:48.227]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"21.4287ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 16:09:48.227]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"21.9404ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 16:09:48.289]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"62.7735ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-09-02 16:09:48.290]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tbrb.order_id as order_id,\n\t\tblo.order_no as order_no,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tpr.total_periods as total_periods,\n\t\t(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE brb.user_id = business_loan_orders.user_id) as repeat_buy_num FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? ORDER BY brb.id DESC LIMIT 20, [2025-09-02 2025-09-02]","duration":"63.8143ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-09-02 16:09:49.556]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"23.9715ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 16:09:49.556]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ? ORDER BY channel_name ASC, [1]","duration":"23.9715ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 16:09:49.588]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"32.4312ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 16:09:49.625]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"36.2108ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-09-02 16:09:49.654]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"28.5649ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-09-02 16:09:51.588]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"32.8574ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 16:09:51.588]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"33.3765ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 16:09:51.637]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"48.51ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-09-02 16:09:51.646]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tbrb.order_id as order_id,\n\t\tblo.order_no as order_no,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tpr.total_periods as total_periods,\n\t\t(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE brb.user_id = business_loan_orders.user_id) as repeat_buy_num FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? ORDER BY brb.id DESC LIMIT 20, [2025-09-02 2025-09-02]","duration":"58.1942ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-09-02 16:09:52.032]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"39.9633ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 16:09:52.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) LIMIT 1, [2025-09-01 2025-09-01]","duration":"46.5739ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-09-02 16:09:52.074]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"40.803ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-09-02 16:09:52.075]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tblo.order_no as order_no,\n\t\tbrb.order_id as order_id,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbaa.idCard as id_card,\n\t\tFROM_UNIXTIME(baa.lastLoginTime, '%Y-%m-%d %H:%i:%s') as last_login_time,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tbrb.due_principal as due_principal,\n\t\tbrb.total_due_amount - brb.paid_amount as diff_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tDATE_FORMAT(brb.created_at,  '%Y-%m-%d %H:%i:%s') as created_at,\n\t\tpr.total_periods as total_periods,\n\t\tIFNULL((SELECT region FROM user_operate_log WHERE uid = brb.user_id ORDER BY created_at DESC LIMIT 1 ), '未知' ) as region,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id AND business_repayment_bills.status in (1,2)) as paid_periods FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) ORDER BY brb.id DESC LIMIT 20, [2025-09-01 2025-09-01]","duration":"35.4494ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 16:09:52.870]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ? ORDER BY channel_name ASC, [1]","duration":"29.7109ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 16:09:52.870]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"31.0859ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-02 16:09:52.925]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"54.7571ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-02 16:09:52.949]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"23.4625ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 16:09:52.981]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"30.9009ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 16:09:55.911]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT count(*) as count FROM `product_rules` LIMIT 1, []","duration":"70.3902ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-09-02 16:09:55.954]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d28b279fd0a1570b13","sql":"SELECT id, rule_name, loan_amount, loan_period, total_periods, guarantee_fee, annual_interest_rate, other_fees, rule_category FROM `product_rules` ORDER BY id desc LIMIT 10, []","duration":"40.3242ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-09-02 16:09:59.617]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d65ffb701c941f3bd4","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [27]","duration":"22.4165ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902161000","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"47.8727ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902161000","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"55.7445ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-09-02 16:10:00.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902161000","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"47.8727ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-02 16:10:02.538]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186168d70d411c2c7b424b3b","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [27]","duration":"37.4243ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-02 16:15:00.033]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"32.2105ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 16:15:00.109]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"108.6588ms","duration_ms":108}
{"level":"dev.info","ts":"[2025-09-02 16:15:00.172]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"62.3355ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.035]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902162600","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"34.1918ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.105]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902162600","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"104.2649ms","duration_ms":104}
{"level":"dev.info","ts":"[2025-09-02 16:26:00.126]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902162600","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"125.1179ms","duration_ms":125}
{"level":"dev.info","ts":"[2025-09-02 16:27:35.489]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"23.4785ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 16:27:35.516]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"26.2744ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-02 16:27:35.749]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"65.7342ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-09-02 16:27:35.791]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"40.6605ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-09-02 16:27:35.837]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"45.0598ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-09-02 16:27:35.878]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"40.4652ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-09-02 16:27:35.957]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"78.9324ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-09-02 16:27:35.980]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"22.617ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.033]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"52.5452ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.065]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"30.7406ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.102]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"35.6996ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.163]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"58.8902ms","duration_ms":58}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.205]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"39.1518ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.246]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 474]","duration":"39.815ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.285]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"38.9191ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.320]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"33.2205ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.389]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"65.8357ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.464]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"74.2905ms","duration_ms":74}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.481]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"17.2988ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.542]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"59.6085ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.562]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"20.0643ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.598]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"35.2928ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.672]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"73.9545ms","duration_ms":73}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.722]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"50.6777ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.747]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"24.5499ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.895]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"148.4964ms","duration_ms":148}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.957]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"61.4647ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-09-02 16:27:36.986]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"29.684ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.030]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"43.4238ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"40.1972ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.109]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"36.5125ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.140]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"31.5371ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.158]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 472]","duration":"17.7698ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.186]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 473]","duration":"27.9242ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.218]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"31.7104ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.252]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"34.3708ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.337]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"85.4412ms","duration_ms":85}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.376]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"38.4672ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.423]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"47.4752ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.445]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"21.9637ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.464]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"18.2739ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.487]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"23.6945ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.511]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"24.2343ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.529]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"16.9648ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.574]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"44.6124ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.593]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"19.4781ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 16:27:37.637]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"42.6347ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-09-02 16:27:39.526]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `product_rules` LIMIT 1, []","duration":"734.1091ms","duration_ms":734}
{"level":"dev.info","ts":"[2025-09-02 16:27:40.587]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id, rule_name, loan_amount, loan_period, total_periods, guarantee_fee, annual_interest_rate, other_fees, rule_category FROM `product_rules` ORDER BY id desc LIMIT 10, []","duration":"1.0619338s","duration_ms":1061}
{"level":"dev.info","ts":"[2025-09-02 16:27:42.203]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cdc0a30fa4717d469e","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?,?,?), [1 0 3 7 9]","duration":"127.1993ms","duration_ms":127}
{"level":"dev.info","ts":"[2025-09-02 16:27:42.290]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cdc0a30fa4717d469e","sql":"SELECT brb.due_guarantee_fee,\n                brb.asset_management_entry,\n                brb.paid_amount,\n                brb.total_waive_amount,\n                brb.total_due_amount,\n                brb.status FROM business_repayment_bills brb LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id WHERE blo.status = ? and brb.status IN (?,?), [1 3 9]","duration":"214.3998ms","duration_ms":214}
{"level":"dev.info","ts":"[2025-09-02 16:27:44.787]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169ce5eb4dc9047574ae3","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"63.2587ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-09-02 16:27:44.788]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169ce5eb4dc9047574ae3","sql":"SELECT COALESCE(SUM(bpt.amount), 0) as total_expense,COUNT(*) as expense_count,COUNT(DISTINCT bpt.user_id) as expense_users,COALESCE(SUM(CASE WHEN DATE(bpt.completed_at) = CURDATE() THEN bpt.amount ELSE 0 END), 0) as today_expense FROM business_payment_transactions bpt LEFT JOIN business_app_account baa ON bpt.user_id = baa.id  INNER JOIN business_loan_orders blo ON bpt.order_id = blo.id WHERE bpt.status = ? and bpt.type = ? and (bpt.completed_at IS NOT NULL) LIMIT 1, [2 DISBURSEMENT]","duration":"63.5055ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-09-02 16:27:44.825]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169ce5eb4dc9047574ae3","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"36.1699ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-09-02 16:27:44.836]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169ce5eb4dc9047574ae3","sql":"SELECT count(*) as count FROM business_payment_transactions bpt LEFT JOIN business_app_account baa ON bpt.user_id = baa.id  INNER JOIN business_loan_orders blo ON bpt.order_id = blo.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE bpt.status = ? and bpt.type = ? and (bpt.completed_at IS NOT NULL) ORDER BY bpt.id DESC LIMIT 1, [2 DISBURSEMENT]","duration":"111.555ms","duration_ms":111}
{"level":"dev.info","ts":"[2025-09-02 16:27:44.868]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169ce5eb4dc9047574ae3","sql":"SELECT bpt.user_id,SUM(bpt.amount) as user_expense,(SELECT COUNT(*) FROM business_loan_orders sub WHERE sub.user_id = bpt.user_id) as user_order_count FROM business_payment_transactions bpt LEFT JOIN business_app_account baa ON bpt.user_id = baa.id  INNER JOIN business_loan_orders blo ON bpt.order_id = blo.id WHERE bpt.status = ? and bpt.type = ? and (bpt.completed_at IS NOT NULL) GROUP BY bpt.user_id, [2 DISBURSEMENT]","duration":"43.5554ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-02 16:27:44.927]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169ce5eb4dc9047574ae3","sql":"SELECT blo.order_no,bpt.amount,baa.name as user_name,baa.mobile,bbc.bank_card_no,DATE_FORMAT(bpt.completed_at, '%Y-%m-%d %H:%i:%s') AS completed_at,bpt.user_id FROM business_payment_transactions bpt LEFT JOIN business_app_account baa ON bpt.user_id = baa.id  INNER JOIN business_loan_orders blo ON bpt.order_id = blo.id  LEFT JOIN contracts c ON blo.contract_id = c.id  LEFT JOIN business_bank_cards bbc ON c.bank_card_id = bbc.id WHERE bpt.status = ? and bpt.type = ? and (bpt.completed_at IS NOT NULL) ORDER BY bpt.id DESC LIMIT 20, [2 DISBURSEMENT]","duration":"90.4172ms","duration_ms":90}
{"level":"dev.info","ts":"[2025-09-02 16:27:45.859]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cea0b7a66801fe2a87","sql":"SELECT \n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT') THEN pt.amount ELSE 0 END) as total_income,\n\t\tSUM(CASE WHEN pt.type = 'REFUND' THEN pt.amount ELSE 0 END) as total_refund,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) = rb.due_date THEN pt.amount ELSE 0 END) as due_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) > rb.due_date THEN pt.amount ELSE 0 END) as overdue_income,\n\t\tSUM(CASE WHEN pt.type IN ('REPAYMENT', 'WITHHOLD', 'MANUAL_WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT')\n\t\t\t AND DATE(pt.completed_at) < rb.due_date THEN pt.amount ELSE 0 END) as early_income,\n\t\tCOUNT(DISTINCT pt.user_id) as total_customers,\n\t\tCOUNT(DISTINCT pt.order_id) as total_orders\n\t FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?,?) LIMIT 1, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT REFUND]","duration":"27.6593ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 16:27:45.859]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cea0b7a66801fe2a87","sql":"SELECT count(*) as count FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?,?) LIMIT 1, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT REFUND]","duration":"27.6593ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 16:27:45.880]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cea0b7a66801fe2a87","sql":"SELECT \n\t\t\tpt.id,\n\t\t\tpt.transaction_no,\n\t\t\tlo.order_no,\n\t\t\taa.name as user_name,\n\t\t\taa.mobile,\n\t\t\tpt.type,\n\t\t\tpt.withhold_type,\n\t\t\tpt.offline_payment_channel_detail,\n\t\t\tpt.amount,\n\t\t\trb.period_number,\n\t\t\tpt.completed_at,\n\t\t\trb.due_date\n\t\t FROM business_payment_transactions pt INNER JOIN business_repayment_bills rb ON pt.bill_id = rb.id  INNER JOIN business_loan_orders lo ON pt.order_id = lo.id  LEFT JOIN business_app_account aa ON pt.user_id = aa.id WHERE pt.status = ? and (pt.deleted_at IS NULL) and pt.type IN (?,?,?,?,?) ORDER BY pt.id DESC LIMIT 20, [2 REPAYMENT WITHHOLD MANUAL_WITHHOLD PARTIAL_OFFLINE_REPAYMENT REFUND]","duration":"20.0654ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-02 16:27:47.795]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cea0b7a66801fe2a87","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"20.898ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-02 16:27:47.823]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cea0b7a66801fe2a87","sql":"SELECT * FROM `product_rules` ORDER BY id ASC, []","duration":"46.763ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-09-02 16:27:47.824]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cea0b7a66801fe2a87","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 10, []","duration":"29.1011ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 16:27:48.941]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT count(*) as count FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? LIMIT 1, [2025-09-02]","duration":"43.2007ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-02 16:27:49.010]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT cs.id, cs.channel_id, c.channel_name, c.channel_code, cs.new_customer_reg_num, cs.real_name_num, cs.number_of_transactions, cs.created_at,cs.updated_at FROM channel_statistics cs LEFT JOIN channel c ON cs.channel_id = c.id WHERE (cs.deleted_at IS NULL) and DATE(cs.created_at) = ? ORDER BY cs.created_at DESC LIMIT 10, [2025-09-02]","duration":"68.5081ms","duration_ms":68}
{"level":"dev.info","ts":"[2025-09-02 16:27:51.444]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ? ORDER BY channel_name ASC, [1]","duration":"29.699ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 16:27:51.444]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"30.5641ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 16:27:51.494]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\tpr.loan_period as loan_period, pr.total_periods as total_periods,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"49.1064ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-09-02 16:27:51.549]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"54.4608ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-02 16:27:51.691]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"142.4291ms","duration_ms":142}
{"level":"dev.info","ts":"[2025-09-02 16:27:52.534]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"395.3475ms","duration_ms":395}
{"level":"dev.info","ts":"[2025-09-02 16:27:52.534]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"395.6911ms","duration_ms":395}
{"level":"dev.info","ts":"[2025-09-02 16:27:52.652]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"118.2658ms","duration_ms":118}
{"level":"dev.info","ts":"[2025-09-02 16:27:52.654]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tbrb.order_id as order_id,\n\t\tblo.order_no as order_no,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tpr.total_periods as total_periods,\n\t\t(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE brb.user_id = business_loan_orders.user_id) as repeat_buy_num FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? ORDER BY brb.id DESC LIMIT 20, [2025-09-02 2025-09-02]","duration":"119.6757ms","duration_ms":119}
{"level":"dev.info","ts":"[2025-09-02 16:27:53.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"21.961ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 16:27:53.038]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) LIMIT 1, [2025-09-01 2025-09-01]","duration":"23.0293ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 16:27:53.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tblo.order_no as order_no,\n\t\tbrb.order_id as order_id,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbaa.idCard as id_card,\n\t\tFROM_UNIXTIME(baa.lastLoginTime, '%Y-%m-%d %H:%i:%s') as last_login_time,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tbrb.due_principal as due_principal,\n\t\tbrb.total_due_amount - brb.paid_amount as diff_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tDATE_FORMAT(brb.created_at,  '%Y-%m-%d %H:%i:%s') as created_at,\n\t\tpr.total_periods as total_periods,\n\t\tIFNULL((SELECT region FROM user_operate_log WHERE uid = brb.user_id ORDER BY created_at DESC LIMIT 1 ), '未知' ) as region,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id AND business_repayment_bills.status in (1,2)) as paid_periods FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) ORDER BY brb.id DESC LIMIT 20, [2025-09-01 2025-09-01]","duration":"37.6536ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-02 16:27:53.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169cf577af4cc60d576e3","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"39.2779ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 16:27:54.489]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0a0ff314864bdef7b","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"63.4323ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-09-02 16:27:54.553]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0a0ff314864bdef7b","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(CASE WHEN uoc.order_count = 1 THEN b.id END) as new_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN b.total_due_amount END), 0) as new_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count = 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as new_user_paid_amount,\n\t\t\tCOUNT(CASE WHEN uoc.order_count > 1 THEN b.id END) as old_user_bills,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN b.total_due_amount END), 0) as old_user_due_amount,\n\t\t\tCOALESCE(SUM(CASE WHEN uoc.order_count > 1 THEN (b.paid_amount + b.total_waive_amount) END), 0) as old_user_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"127.3147ms","duration_ms":127}
{"level":"dev.info","ts":"[2025-09-02 16:27:54.553]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0a0ff314864bdef7b","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT o.user_id) as repayment_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? AND pt.status = 2 AND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD') GROUP BY c.id ORDER BY c.id, [1]","duration":"127.3147ms","duration_ms":127}
{"level":"dev.info","ts":"[2025-09-02 16:27:54.712]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0a0ff314864bdef7b","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"222.6928ms","duration_ms":222}
{"level":"dev.info","ts":"[2025-09-02 16:27:54.748]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0a0ff314864bdef7b","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t),\n\t\trepayment_users AS (\n\t\t\tSELECT DISTINCT o.user_id, o.channel_id\n\t\t\tFROM business_loan_orders o\n\t\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\t\tINNER JOIN business_payment_transactions pt ON b.id = pt.bill_id\n\t\t\tWHERE pt.status = 2\n\t\t\tAND pt.type IN ('REPAYMENT', 'WITHHOLD', 'PARTIAL_OFFLINE_REPAYMENT', 'MANUAL_WITHHOLD')\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN ru.user_id IS NOT NULL AND uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as repeat_purchase_users,\n\t\t\tCOUNT(DISTINCT o.user_id) as total_order_users,\n\t\t\tCOUNT(DISTINCT CASE\n\t\t\t\tWHEN uoc.order_count > 1\n\t\t\t\tTHEN o.user_id\n\t\t\tEND) as total_repurchase_users\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tINNER JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tINNER JOIN user_order_counts uoc ON o.user_id = uoc.user_id\n\t\tLEFT JOIN repayment_users ru ON o.user_id = ru.user_id AND o.channel_id = ru.channel_id WHERE c.channel_status = ? GROUP BY c.id ORDER BY c.id, [1]","duration":"322.0311ms","duration_ms":322}
{"level":"dev.info","ts":"[2025-09-02 16:27:54.748]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0a0ff314864bdef7b","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT\n\t\t\tc.id as channel_id,\n\t\t\tc.channel_name,\n\t\t\tCOUNT(b.id) as total_bills,\n\t\t\tCOALESCE(SUM(b.total_due_amount), 0) as total_due_amount,\n\t\t\tCOALESCE(SUM(b.paid_amount + b.total_waive_amount), 0) as total_paid_amount\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id, [1]","duration":"322.0311ms","duration_ms":322}
{"level":"dev.info","ts":"[2025-09-02 16:27:54.759]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0a0ff314864bdef7b","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT COUNT(DISTINCT c.id) as count\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ?, [1]","duration":"332.9457ms","duration_ms":332}
{"level":"dev.info","ts":"[2025-09-02 16:27:54.818]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0a0ff314864bdef7b","sql":"\n\t\tWITH user_order_counts AS (\n\t\t\tSELECT user_id, COUNT(*) as order_count\n\t\t\tFROM business_loan_orders\n\t\t\tGROUP BY user_id\n\t\t)\n\t\tSELECT DISTINCT c.id as channel_id, c.channel_name\n\t\tFROM channel c\n\t\tLEFT JOIN business_loan_orders o ON c.id = o.channel_id\n\t\tLEFT JOIN business_repayment_bills b ON o.id = b.order_id\n\t\tLEFT JOIN user_order_counts uoc ON o.user_id = uoc.user_id WHERE c.channel_status = ? GROUP BY c.id, c.channel_name ORDER BY c.id LIMIT 20 OFFSET 0, [1]","duration":"59.236ms","duration_ms":59}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"33.3475ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"33.3475ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"32.8224ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.378]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"33.3475ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.402]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"57.0814ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.402]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"57.0814ms","duration_ms":57}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.418]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"39.6195ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.455]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"109.9336ms","duration_ms":109}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.455]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"52.8732ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.455]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"52.8732ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.467]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"122.0537ms","duration_ms":122}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.567]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-09-02]","duration":"221.7818ms","duration_ms":221}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.567]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"221.7818ms","duration_ms":221}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.584]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"128.8476ms","duration_ms":128}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.613]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b8c5d4aa68","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"157.2798ms","duration_ms":157}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.623]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b8c5d4aa68","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-09-02]","duration":"55.6476ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.623]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b8c5d4aa68","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"55.6476ms","duration_ms":55}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.638]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b8c5d4aa68","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` LIMIT 1, []","duration":"54.3691ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.669]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b8c5d4aa68","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"30.2006ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 16:27:55.690]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"186169d0d7bc13b86b646e08","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"21.3295ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"88.8951ms","duration_ms":88}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.094]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"93.2148ms","duration_ms":93}
{"level":"dev.info","ts":"[2025-09-02 16:28:00.096]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"95.3282ms","duration_ms":95}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.043]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"42.4028ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.091]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"89.6431ms","duration_ms":89}
{"level":"dev.info","ts":"[2025-09-02 23:34:00.092]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"91.3838ms","duration_ms":91}
{"level":"dev.info","ts":"[2025-09-02 23:34:01.686]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"23.796ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 23:34:01.705]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"19.4033ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 23:34:01.866]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"24.2575ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 23:34:01.896]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"30.3119ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 23:34:01.988]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"91.0101ms","duration_ms":91}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.061]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"72.8329ms","duration_ms":72}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"27.9837ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.112]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"22.6825ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.137]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"25.0275ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.164]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"26.2995ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.202]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"38.516ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"63.5718ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.299]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"32.9319ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.347]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 474]","duration":"48.0592ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.394]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"46.8186ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.429]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"33.9029ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.458]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"29.5265ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.482]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"23.3192ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.527]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"45.8483ms","duration_ms":45}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.553]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"25.9665ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.592]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"38.124ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.626]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"33.2616ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.663]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"36.7128ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.694]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"30.0705ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.737]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"43.1737ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.811]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"72.6681ms","duration_ms":72}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.838]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"26.3525ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.871]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"33.2865ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.924]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"51.6509ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.943]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"18.2129ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.970]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"27.0425ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 23:34:02.988]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"17.4228ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.017]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 472]","duration":"29.2702ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.051]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 473]","duration":"33.0552ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"12.8315ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"34.2339ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.112]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"13.5958ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"19.8596ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.177]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"43.6748ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.202]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"24.5159ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.223]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"19.7645ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.249]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"25.8163ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.288]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"38.7048ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.338]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"50.9662ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.351]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"12.4702ms","duration_ms":12}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.370]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"17.4029ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 23:34:03.390]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"20.1538ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.306]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"64.6752ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.306]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"64.6752ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.316]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"\n\t\tSELECT\n\t\t\tDATE(FROM_UNIXTIME(createtime)) as date,\n\t\t\tCOUNT(*) as registration_count\n\t\tFROM business_app_account\n\t\tWHERE createtime >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL ? DAY))\n\t\t\tAND createtime < UNIX_TIMESTAMP(CURDATE())\n\t\tGROUP BY DATE(FROM_UNIXTIME(createtime))\n\t\tORDER BY date\n\t, [7]","duration":"74.9733ms","duration_ms":74}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.372]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"129.8247ms","duration_ms":129}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.372]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"65.1527ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.372]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"\n\t\tSELECT\n\t\t\tDATE(completed_at) as date,\n\t\t\tCOALESCE(SUM(amount), 0) as disbursement_amount\n\t\tFROM business_payment_transactions\n\t\tWHERE status = ?\n\t\t\tAND type = ?\n\t\t\tAND DATE(completed_at) >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND DATE(completed_at) < CURDATE()\n\t\tGROUP BY DATE(completed_at)\n\t\tORDER BY date\n\t, [2 DISBURSEMENT 7]","duration":"130.3613ms","duration_ms":130}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.372]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COALESCE(SUM(total_due_amount - paid_amount - total_waive_amount), 0) as overdue_amount FROM `business_repayment_bills` WHERE DATE(due_date) < ? and `status` IN (?,?,?) LIMIT 1, [2025-09-02 3 9 2]","duration":"65.6893ms","duration_ms":65}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.372]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(paid_amount), 0) as repayment_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE paid_amount > 0\n\t\t\tAND due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"130.3613ms","duration_ms":130}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.372]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COALESCE(SUM(total_due_amount - total_waive_amount), 0) as due_amount,COALESCE(SUM(due_principal), 0) as due_principal_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-09-02]","duration":"129.8338ms","duration_ms":129}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.372]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ?, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"130.8896ms","duration_ms":130}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.372]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"\n\t\tSELECT\n\t\t\tDATE(due_date) as date,\n\t\t\tCOALESCE(SUM(total_due_amount), 0) as due_amount\n\t\tFROM business_repayment_bills\n\t\tWHERE due_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)\n\t\t\tAND due_date < CURDATE()\n\t\tGROUP BY DATE(due_date)\n\t\tORDER BY date\n\t, [7]","duration":"130.3613ms","duration_ms":130}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.373]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT * FROM `business_payment_transactions` WHERE `status` = ? and `type` = ?, [2 DISBURSEMENT]","duration":"132.0045ms","duration_ms":132}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.411]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"39.0219ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.411]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COALESCE(SUM(paid_amount), 0) as repayment_amount FROM `business_repayment_bills` WHERE DATE(due_date) <= ? LIMIT 1, [2025-09-02]","duration":"38.5277ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.411]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"37.9407ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.411]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COALESCE(SUM(amount), 0) as total_amount, COUNT(*) as order_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"38.5277ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.443]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15059ea1eb5","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? and DATE(completed_at) BETWEEN ? and ? LIMIT 1, [2 DISBURSEMENT 2025-09-02 2025-09-02]","duration":"30.3998ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.443]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"18618112109b20f44723bd89","sql":"SELECT COUNT(DISTINCT user_id) as customer_count FROM `business_payment_transactions` WHERE `status` = ? and `type` = ? LIMIT 1, [2 DISBURSEMENT]","duration":"30.9103ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.471]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` WHERE DATE(disbursed_at) BETWEEN ? and ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"28.1499ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-09-02 23:34:04.476]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT COALESCE(SUM(principal), 0) as principal_amount FROM `business_loan_orders` LIMIT 1, []","duration":"32.24ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 23:34:06.939]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"31.0235ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-02 23:34:06.939]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ? ORDER BY channel_name ASC, [1]","duration":"31.0235ms","duration_ms":31}
{"level":"dev.error","ts":"[2025-09-02 23:34:06.956]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","request_id":"1861811210a2d15077a2f19f","error_message":"Error 1140 (42000): In aggregated query without GROUP BY, expression #1 of SELECT list contains nonaggregated column 'fincore.brb.created_at'; this is incompatible with sql_mode=only_full_group_by","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).Get\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:49\nfincore/utils/pagination.PaginateWithCustomQuery\n\tD:/work/code/fincore/go/src/utils/pagination/pagination.go:125\nfincore/app/business/order.(*Repository).GetOrderListWithFilters\n\tD:/work/code/fincore/go/src/app/business/order/repository.go:556\nfincore/app/business/order.(*Service).GetOrderList\n\tD:/work/code/fincore/go/src/app/business/order/query_service.go:46\nfincore/app/business/order.(*Manager).ListOrders\n\tD:/work/code/fincore/go/src/app/business/order/controller.go:51\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.ValidityAPi.func1\n\tD:/work/code/fincore/go/src/route/middleware/validityAPi.go:19\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:31\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.ErrorLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.OperateLogHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/operate_log.go:464\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.ShortUrlHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/short_url.go:19\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.426]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"32.3316ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.474]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"47.7301ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.523]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"17.1099ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.566]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"43.2779ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.614]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"47.3909ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.641]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"27.0005ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.659]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"17.4224ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.699]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"38.9532ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.719]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"19.9135ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.750]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"30.0446ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.776]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"25.2532ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.807]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"30.5825ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.842]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"34.6789ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.859]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 474]","duration":"16.1604ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.881]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"20.8162ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.908]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"27.2312ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.932]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"23.7293ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.950]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"17.4841ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.969]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"19.1765ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 23:34:11.992]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"23.0333ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.006]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"13.4223ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.030]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"24.4792ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.044]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"13.5569ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"19.2678ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.094]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"29.5921ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.138]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"44.0784ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.191]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"51.7426ms","duration_ms":51}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.224]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"33.176ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.250]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"25.4005ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.263]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"13.4297ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.282]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"18.3783ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.318]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"35.4952ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.352]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 472]","duration":"33.8463ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.379]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 473]","duration":"26.4154ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.404]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"24.1326ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.443]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"39.0694ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.482]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"37.9467ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.506]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"22.8417ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.535]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"28.9622ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.554]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"18.8445ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.580]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"25.8754ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.622]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"41.4225ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.636]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"13.312ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.669]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"32.8633ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.694]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"24.7093ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.717]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"22.4897ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 23:34:12.743]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"26.8287ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-02 23:34:13.133]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT id, channel_name FROM `channel` WHERE `channel_status` = ? ORDER BY channel_name ASC, [1]","duration":"40.2826ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-09-02 23:34:13.133]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861811210a2d15077a2f19f","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"40.2826ms","duration_ms":40}
{"level":"dev.error","ts":"[2025-09-02 23:34:13.149]","caller":"gform/sql_logger.go:139","msg":"SQL执行错误","request_id":"1861811210a2d15077a2f19f","error_message":"Error 1140 (42000): In aggregated query without GROUP BY, expression #1 of SELECT list contains nonaggregated column 'fincore.brb.created_at'; this is incompatible with sql_mode=only_full_group_by","stacktrace":"fincore/utils/gform.(*SQLLogger).Error\n\tD:/work/code/fincore/go/src/utils/gform/sql_logger.go:139\nfincore/utils/gform.(*Session).Query\n\tD:/work/code/fincore/go/src/utils/gform/session.go:165\nfincore/utils/gform.(*Orm).Select\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:23\nfincore/utils/gform.(*Orm).Get\n\tD:/work/code/fincore/go/src/utils/gform/orm_query.go:49\nfincore/utils/pagination.PaginateWithCustomQuery\n\tD:/work/code/fincore/go/src/utils/pagination/pagination.go:125\nfincore/app/business/order.(*Repository).GetOrderListWithFilters\n\tD:/work/code/fincore/go/src/app/business/order/repository.go:556\nfincore/app/business/order.(*Service).GetOrderList\n\tD:/work/code/fincore/go/src/app/business/order/query_service.go:46\nfincore/app/business/order.(*Manager).ListOrders\n\tD:/work/code/fincore/go/src/app/business/order/controller.go:51\nreflect.Value.call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584\nreflect.Value.Call\n\tC:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368\nfincore/utils/gf.match.func1\n\tD:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.ValidityAPi.func1\n\tD:/work/code/fincore/go/src/route/middleware/validityAPi.go:19\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.LimitHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/LimitHandler.go:31\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.ErrorLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:176\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.OperateLogHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/operate_log.go:464\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:142\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.ShortUrlHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/short_url.go:19\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-09-02 23:38:20.207]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"22.3886ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 23:38:20.285]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t (SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as total_periods,\n\t\t -- 借款周期天数,取第一期账单到期时间 due_date(date) - 创建时间 created_at(timestamp) 天数差 \n\t\t(SELECT DATEDIFF(MIN(brb.due_date), MIN(brb.created_at)) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as loan_period,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"77.6982ms","duration_ms":77}
{"level":"dev.info","ts":"[2025-09-02 23:38:20.306]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"20.4932ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-02 23:38:20.330]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"24.5706ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 23:38:21.047]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"13.6911ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-02 23:38:21.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t (SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as total_periods,\n\t\t -- 借款周期天数,取第一期账单到期时间 due_date(date) - 创建时间 created_at(timestamp) 天数差 \n\t\t(SELECT DATEDIFF(MIN(brb.due_date), MIN(brb.created_at)) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as loan_period,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"18.9915ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 23:38:21.084]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"16.9638ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-02 23:38:21.118]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"33.3226ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-02 23:38:22.589]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"22.2718ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 23:38:22.618]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t (SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as total_periods,\n\t\t -- 借款周期天数,取第一期账单到期时间 due_date(date) - 创建时间 created_at(timestamp) 天数差 \n\t\t(SELECT DATEDIFF(MIN(brb.due_date), MIN(brb.created_at)) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as loan_period,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"28.8237ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-09-02 23:38:22.649]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"29.8255ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-02 23:38:22.676]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"26.2135ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-02 23:38:23.445]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"21.5064ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 23:38:23.468]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t (SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as total_periods,\n\t\t -- 借款周期天数,取第一期账单到期时间 due_date(date) - 创建时间 created_at(timestamp) 天数差 \n\t\t(SELECT DATEDIFF(MIN(brb.due_date), MIN(brb.created_at)) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as loan_period,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"21.8803ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 23:38:23.504]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"35.6721ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 23:38:23.520]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"15.5304ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-02 23:39:00.037]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"36.5303ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-09-02 23:39:00.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"88.6455ms","duration_ms":88}
{"level":"dev.info","ts":"[2025-09-02 23:39:00.112]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"21.5785ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.064]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"64.1296ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"70.5171ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-09-02 23:40:00.084]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"83.5835ms","duration_ms":83}
{"level":"dev.info","ts":"[2025-09-02 23:41:00.066]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"66.6675ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"66.104ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"66.6087ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"67.2828ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"67.2828ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-09-02 23:42:00.103]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"35.6466ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-02 23:43:00.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"56.3199ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.098]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"96.4063ms","duration_ms":96}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"96.9312ms","duration_ms":96}
{"level":"dev.info","ts":"[2025-09-02 23:44:00.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"97.4827ms","duration_ms":97}
{"level":"dev.info","ts":"[2025-09-02 23:45:00.071]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"70.229ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-09-02 23:45:00.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"88.6351ms","duration_ms":88}
{"level":"dev.info","ts":"[2025-09-02 23:45:00.107]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"18.2856ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.051]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902234600","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"50.7559ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902234600","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"76.9659ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-09-02 23:46:00.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250902234600","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"76.9659ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-09-02 23:50:15.252]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) LIMIT 1, []","duration":"14.6556ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-09-02 23:50:15.275]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t    blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at,blo.completed_at, blo.contract_id,\n\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\tc2.channel_name as initial_order_channel_name,\n\t\t sales_user.name as sales_assignee_name, collection_user.name as collection_assignee_name,\n\t\t (SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as total_periods,\n\t\t(SELECT DATEDIFF(MIN(brb.due_date), MIN(brb.created_at)) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as loan_period,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills brb WHERE brb.order_id = blo.id AND brb.status = 1) as paid_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON blo.collection_assignee_id = collection_user.id WHERE (baa.deletedAt = 0) ORDER BY blo.id DESC LIMIT 20, []","duration":"22.8235ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-02 23:50:15.313]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"37.7062ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-02 23:50:15.337]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"23.6781ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 23:50:21.252]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"19.5971ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 23:50:21.299]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tbrb.order_id as order_id,\n\t\tblo.order_no as order_no,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\t(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE brb.user_id = business_loan_orders.user_id) as repeat_buy_num,\n\t\t(SELECT DATEDIFF(MIN(brb.due_date), MIN(brb.created_at)) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as loan_period FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? ORDER BY brb.id DESC LIMIT 20, [2025-09-02 2025-09-02]","duration":"46.012ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-09-02 23:50:21.325]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"91.3035ms","duration_ms":91}
{"level":"dev.info","ts":"[2025-09-02 23:50:21.367]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"41.895ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-09-02 23:50:22.248]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"14.9859ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-09-02 23:50:22.249]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) LIMIT 1, [2025-09-01 2025-09-01]","duration":"15.4993ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-02 23:50:22.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"17.9576ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-02 23:50:22.267]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tblo.order_no as order_no,\n\t\tbrb.order_id as order_id,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbaa.idCard as id_card,\n\t\tFROM_UNIXTIME(baa.lastLoginTime, '%Y-%m-%d %H:%i:%s') as last_login_time,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tbrb.due_principal as due_principal,\n\t\tbrb.total_due_amount - brb.paid_amount as diff_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tDATE_FORMAT(brb.created_at,  '%Y-%m-%d %H:%i:%s') as created_at,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id) as total_periods,\n\t\tIFNULL((SELECT region FROM user_operate_log WHERE uid = brb.user_id ORDER BY created_at DESC LIMIT 1 ), '未知' ) as region,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id AND business_repayment_bills.status in (1,2)) as paid_periods FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) ORDER BY brb.id DESC LIMIT 20, [2025-09-01 2025-09-01]","duration":"18.4991ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-02 23:50:23.432]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"19.9986ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-02 23:50:23.450]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"37.6857ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-02 23:50:23.471]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tbrb.order_id as order_id,\n\t\tblo.order_no as order_no,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\t(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE brb.user_id = business_loan_orders.user_id) as repeat_buy_num,\n\t\t(SELECT DATEDIFF(MIN(brb.due_date), MIN(brb.created_at)) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as loan_period FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? ORDER BY brb.id DESC LIMIT 20, [2025-09-02 2025-09-02]","duration":"38.9694ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 23:50:23.484]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"32.7902ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 23:50:24.401]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"23.5986ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-02 23:50:24.402]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) LIMIT 1, [2025-09-01 2025-09-01]","duration":"24.6638ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-02 23:50:24.429]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"26.8403ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-02 23:50:24.439]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tblo.order_no as order_no,\n\t\tbrb.order_id as order_id,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbaa.idCard as id_card,\n\t\tFROM_UNIXTIME(baa.lastLoginTime, '%Y-%m-%d %H:%i:%s') as last_login_time,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tbrb.due_principal as due_principal,\n\t\tbrb.total_due_amount - brb.paid_amount as diff_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tDATE_FORMAT(brb.created_at,  '%Y-%m-%d %H:%i:%s') as created_at,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id) as total_periods,\n\t\tIFNULL((SELECT region FROM user_operate_log WHERE uid = brb.user_id ORDER BY created_at DESC LIMIT 1 ), '未知' ) as region,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id AND business_repayment_bills.status in (1,2)) as paid_periods FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) ORDER BY brb.id DESC LIMIT 20, [2025-09-01 2025-09-01]","duration":"36.8961ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-09-02 23:50:25.569]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) LIMIT 1, [2025-09-01 2025-09-01]","duration":"13.1924ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-02 23:50:25.608]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tblo.order_no as order_no,\n\t\tbrb.order_id as order_id,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbaa.idCard as id_card,\n\t\tFROM_UNIXTIME(baa.lastLoginTime, '%Y-%m-%d %H:%i:%s') as last_login_time,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tbrb.due_principal as due_principal,\n\t\tbrb.total_due_amount - brb.paid_amount as diff_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\tDATE_FORMAT(brb.created_at,  '%Y-%m-%d %H:%i:%s') as created_at,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id) as total_periods,\n\t\tIFNULL((SELECT region FROM user_operate_log WHERE uid = brb.user_id ORDER BY created_at DESC LIMIT 1 ), '未知' ) as region,\n\t\t(SELECT COUNT(*) FROM business_repayment_bills WHERE business_repayment_bills.order_id = brb.order_id AND business_repayment_bills.user_id = brb.user_id AND business_repayment_bills.status in (1,2)) as paid_periods FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? and (brb.status in (3, 9)) ORDER BY brb.id DESC LIMIT 20, [2025-09-01 2025-09-01]","duration":"38.735ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-02 23:50:27.019]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"28.7738ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-09-02 23:50:27.056]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `channel` LIMIT 1, []","duration":"64.2779ms","duration_ms":64}
{"level":"dev.info","ts":"[2025-09-02 23:50:27.107]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tbrb.order_id as order_id,\n\t\tblo.order_no as order_no,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\t(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE brb.user_id = business_loan_orders.user_id) as repeat_buy_num,\n\t\t(SELECT DATEDIFF(MIN(brb.due_date), MIN(brb.created_at)) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as loan_period FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? ORDER BY brb.id DESC LIMIT 20, [2025-09-02 2025-09-02]","duration":"87.3118ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-09-02 23:50:27.109]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `channel` ORDER BY create_time DESC LIMIT 1000, []","duration":"52.6111ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-09-02 23:50:28.027]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? LIMIT 1, [2025-09-02 2025-09-02]","duration":"32.5977ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-02 23:50:28.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT brb.id as id, \n\t\tbrb.user_id as user_id, \n\t\tbrb.order_id as order_id,\n\t\tblo.order_no as order_no,\n\t\tc.channel_name as channel_name, \n\t\tsales_user.username as sales_assignee_name,\n\t\tcollection_user.username as collection_assignee_name,\n\t\tbaa.name as user_name, \n\t\tbaa.mobile as user_mobile,\n\t\tbrb.total_due_amount as total_due_amount,\n\t\tbrb.paid_amount as paid_amount,\n\t\tDATE_FORMAT(brb.paid_at,  '%Y-%m-%d %H:%i:%s') as paid_at,\n\t\tDATE_FORMAT(brb.due_date, '%Y-%m-%d') as due_date,\n\t\tbrb.status as status,\n\t\tbrb.period_number as period_number,\n\t\t(SELECT COUNT(*) - 1 FROM business_loan_orders WHERE brb.user_id = business_loan_orders.user_id) as repeat_buy_num,\n\t\t(SELECT DATEDIFF(MIN(brb.due_date), MIN(brb.created_at)) FROM business_repayment_bills brb WHERE brb.order_id = blo.id) as loan_period FROM business_repayment_bills brb LEFT JOIN business_app_account baa ON brb.user_id = baa.id  LEFT JOIN business_loan_orders blo ON brb.order_id = blo.id  LEFT JOIN channel c ON blo.channel_id = c.id  LEFT JOIN business_account sales_user ON blo.sales_assignee_id = sales_user.id  LEFT JOIN business_account collection_user ON brb.collection_assignee_id = collection_user.id WHERE brb.due_date >= ? and brb.due_date <= ? ORDER BY brb.id DESC LIMIT 20, [2025-09-02 2025-09-02]","duration":"26.0701ms","duration_ms":26}
