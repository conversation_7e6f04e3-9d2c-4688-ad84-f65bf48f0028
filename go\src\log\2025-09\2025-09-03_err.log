

[31m2025/09/03 15:29:31 [Recovery] 2025/09/03 - 15:29:31 panic recovered:
POST /uniapp/order/createOrder HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 119
Content-Type: application/json
User-Agent: Apifox/1.0.0 (https://apifox.com)


token contains an invalid number of segments
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:131 (0x170d136)
	ParseToken: panic(err)
D:/work/code/fincore/go/src/app/uniapp/order/controller.go:107 (0x23ab206)
	(*Index).CreateOrder: claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xb1d344)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xb1c2f2)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x153c9c4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:31 (0x170d924)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x1316ca9)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x119e9bc)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x119d285)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/operate_log.go:464 (0x17133d8)
	OperateLogHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x13170c5)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x1318235)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/short_url.go:19 (0x171326b)
	ShortUrlHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x119e9bc)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x119d285)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x118b479)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x119b369)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x119ae3b)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xeb3536)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xe803f4)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0xa020e0)
	goexit: BYTE	$0x90	// NOP
[0m
