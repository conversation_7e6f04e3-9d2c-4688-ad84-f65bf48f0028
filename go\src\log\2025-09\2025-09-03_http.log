{"level":"dev.info","ts":"[2025-09-03 11:27:36.136]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a801fc7c4bf4a41f8959","method":"GET","url":"/business/customer/customercontroller/getCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/getCustomerOptions","query":"_t=1756870043763"}
{"level":"dev.info","ts":"[2025-09-03 11:27:36.216]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a801fc7c4bf4a41f8959","method":"GET","url":"/business/customer/customercontroller/getCustomerOptions","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0810425,"response_size":737}
{"level":"dev.info","ts":"[2025-09-03 11:27:36.352]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a802096b9374e8564544","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:27:36.469]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a802096b9374e8564544","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1172489,"response_size":20418}
{"level":"dev.info","ts":"[2025-09-03 11:28:14.203]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a80ad97f816ccee0ff56","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:28:14.296]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a80ad97f816ccee0ff56","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0928001,"response_size":508}
{"level":"dev.info","ts":"[2025-09-03 11:28:16.235]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a80b529e1e001b303a00","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:28:16.332]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a80b529e1e001b303a00","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0974331,"response_size":508}
{"level":"dev.info","ts":"[2025-09-03 11:28:21.058]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a80c721c9ae4d309a65f","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:28:36.253]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a80c721c9ae4d309a65f","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":15.1951557,"response_size":508}
{"level":"dev.info","ts":"[2025-09-03 11:29:34.647]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a81d945a2b2441ddb368","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:29:39.705]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a81d945a2b2441ddb368","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":5.0579794,"response_size":508}
{"level":"dev.info","ts":"[2025-09-03 11:30:17.135]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a82778ce73d829217d9b","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:30:19.557]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a82778ce73d829217d9b","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.4224496,"response_size":508}
{"level":"dev.info","ts":"[2025-09-03 11:49:17.094]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a930e3a91924de760eff","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:49:19.521]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a930e3a91924de760eff","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":2.4273017,"response_size":508}
{"level":"dev.info","ts":"[2025-09-03 11:49:22.891]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a9323d32d088cb68c064","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:49:35.632]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a9323d32d088cb68c064","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":12.7412155,"response_size":508}
{"level":"dev.info","ts":"[2025-09-03 11:52:04.900]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a957f5a67348f1e460ea","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:52:04.979]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a957f5a67348f1e460ea","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0797943,"response_size":508}
{"level":"dev.info","ts":"[2025-09-03 11:52:05.706]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a95825b4d124c0b41ce8","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:52:05.771]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a95825b4d124c0b41ce8","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0652278,"response_size":508}
{"level":"dev.info","ts":"[2025-09-03 11:52:41.118]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a960645b204c5363cf6e","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:52:41.279]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a960645b204c5363cf6e","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1623856,"response_size":1504}
{"level":"dev.info","ts":"[2025-09-03 11:52:41.935]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a960951fbd3ccce4f88a","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:52:42.041]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a960951fbd3ccce4f88a","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.1061385,"response_size":1504}
{"level":"dev.info","ts":"[2025-09-03 11:53:03.876]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861a965b0eac3cc4e81c4aa","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/customer/customercontroller/listCustomers","query":""}
{"level":"dev.info","ts":"[2025-09-03 11:53:03.990]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861a965b0eac3cc4e81c4aa","method":"POST","url":"/business/customer/customercontroller/listCustomers","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.114726,"response_size":20418}
{"level":"dev.info","ts":"[2025-09-03 12:08:48.759]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861aa41b04f05cc601b8b3e","method":"GET","url":"/business/productrules/list/getRules","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRules","query":"page=1&limit=10&rule_name=&rule_category=&repayment_method=&_t=1756872528680"}
{"level":"dev.info","ts":"[2025-09-03 12:08:48.817]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861aa41b04f05cc601b8b3e","method":"GET","url":"/business/productrules/list/getRules","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0584753,"response_size":1402}
{"level":"dev.info","ts":"[2025-09-03 12:08:50.400]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861aa42122e9be0a0a2b482","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRuleDetail","query":"id=27&_t=1756872530381"}
{"level":"dev.info","ts":"[2025-09-03 12:08:50.455]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861aa42122e9be0a0a2b482","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0544137,"response_size":2509}
{"level":"dev.info","ts":"[2025-09-03 12:08:56.145]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861aa436898eb3828860d8f","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-09-03 12:10:51.005]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861aa5e26b8d50039cb11bd","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRuleDetail","query":"id=27&_t=1756872650992"}
{"level":"dev.info","ts":"[2025-09-03 12:10:51.038]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861aa5e26b8d50039cb11bd","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0342668,"response_size":2509}
{"level":"dev.info","ts":"[2025-09-03 12:10:54.100]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861aa5edf485208dc6bbcaa","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-09-03 12:50:12.859]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac84102bbe10e46e76b4","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/get_userinfo","query":"_t=1756875012848"}
{"level":"dev.info","ts":"[2025-09-03 12:50:12.936]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac84102bbe10e46e76b4","method":"GET","url":"/business/user/get_userinfo","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.078171,"response_size":725}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.078]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac841d4a686cfbe7d448","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/user/account/get_menu","query":"_t=*************"}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.591]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac841d4a686cfbe7d448","method":"GET","url":"/business/user/account/get_menu","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":1.51364,"response_size":8354}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.960]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac848d74f8f037faa9f3","method":"GET","url":"/business/productrules/list/getRules","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRules","query":"page=1&limit=10&rule_name=&rule_category=&repayment_method=&_t=*************"}
{"level":"dev.info","ts":"[2025-09-03 12:50:15.058]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac848d74f8f037faa9f3","method":"GET","url":"/business/productrules/list/getRules","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0982251,"response_size":1402}
{"level":"dev.info","ts":"[2025-09-03 12:50:16.936]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac8503401bc8c9664f67","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRuleDetail","query":"id=19&_t=1756875016926"}
{"level":"dev.info","ts":"[2025-09-03 12:50:16.979]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac8503401bc8c9664f67","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0431931,"response_size":2507}
{"level":"dev.info","ts":"[2025-09-03 12:51:00.469]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac8f2602b940043ab1c2","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRuleDetail","query":"id=27&_t=1756875060456"}
{"level":"dev.info","ts":"[2025-09-03 12:51:00.542]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac8f2602b940043ab1c2","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0729888,"response_size":2509}
{"level":"dev.info","ts":"[2025-09-03 12:51:05.081]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac9038f06ce069330b80","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-09-03 12:51:05.084]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac9038f06ce069330b80","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0026901,"response_size":1176}
{"level":"dev.info","ts":"[2025-09-03 12:51:07.119]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac90b26531b47c975468","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-09-03 12:51:07.120]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac90b26531b47c975468","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.001079,"response_size":1188}
{"level":"dev.info","ts":"[2025-09-03 12:51:09.689]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac914b90a81470b9282f","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-09-03 12:51:09.690]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac914b90a81470b9282f","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0015285,"response_size":1176}
{"level":"dev.info","ts":"[2025-09-03 12:51:12.608]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac91f991ef189ea9830c","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-09-03 12:51:12.609]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac91f991ef189ea9830c","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0011171,"response_size":1188}
{"level":"dev.info","ts":"[2025-09-03 12:51:18.211]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac93478c15449e5ba677","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRuleDetail","query":"id=26&_t=1756875078199"}
{"level":"dev.info","ts":"[2025-09-03 12:51:18.266]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac93478c15449e5ba677","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0551495,"response_size":2099}
{"level":"dev.info","ts":"[2025-09-03 12:51:26.291]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac95292ac83cfe026537","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-09-03 12:51:26.293]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac95292ac83cfe026537","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0010345,"response_size":1008}
{"level":"dev.info","ts":"[2025-09-03 12:51:27.375]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac9569c2fc98225f6f19","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/payment/manager/calculateRepaymentSchedule","query":""}
{"level":"dev.info","ts":"[2025-09-03 12:51:27.377]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861ac9569c2fc98225f6f19","method":"POST","url":"/business/payment/manager/calculateRepaymentSchedule","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0015889,"response_size":1001}
{"level":"dev.info","ts":"[2025-09-03 12:51:45.571]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ac99a64a8eacd57ba236","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRuleDetail","query":"id=26&_t=1756875105559"}
{"level":"dev.info","ts":"[2025-09-03 12:53:47.415]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861acb604bfdfe882f21cf7","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRuleDetail","query":"id=26&_t=1756875227402"}
{"level":"dev.info","ts":"[2025-09-03 12:53:47.443]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861acb604bfdfe882f21cf7","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0288731,"response_size":2099}
{"level":"dev.info","ts":"[2025-09-03 12:53:50.328]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861acb6b269a6ec5776b518","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRuleDetail","query":"id=26&_t=1756875230312"}
{"level":"dev.info","ts":"[2025-09-03 12:53:50.351]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861acb6b269a6ec5776b518","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0229989,"response_size":2099}
{"level":"dev.info","ts":"[2025-09-03 12:54:18.503]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861acbd41c44d50a13151b6","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/business/productrules/list/getRuleDetail","query":"id=26&_t=1756875258492"}
{"level":"dev.info","ts":"[2025-09-03 12:54:18.543]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861acbd41c44d50a13151b6","method":"GET","url":"/business/productrules/list/getRuleDetail","ip":"************","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","status_code":200,"response_time":0.0397829,"response_size":2099}
{"level":"dev.info","ts":"[2025-09-03 15:28:42.657]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861b52a3c4da3384e3a6091","method":"POST","url":"/business/order/manager/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/order/manager/createOrder","query":""}
{"level":"dev.warn","ts":"[2025-09-03 15:28:42.659]","caller":"log/middleware.go:61","msg":"请求完成","request_id":"1861b52a3c4da3384e3a6091","method":"POST","url":"/business/order/manager/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":404,"response_time":0.002858,"response_size":94}
{"level":"dev.info","ts":"[2025-09-03 15:29:10.027]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861b5309bc97a5c4be3224a","method":"POST","url":"/uniapp/order/manager/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/order/manager/createOrder","query":""}
{"level":"dev.warn","ts":"[2025-09-03 15:29:10.027]","caller":"log/middleware.go:61","msg":"请求完成","request_id":"1861b5309bc97a5c4be3224a","method":"POST","url":"/uniapp/order/manager/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":404,"response_time":0,"response_size":92}
{"level":"dev.info","ts":"[2025-09-03 15:29:31.746]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861b535aa4d5760c5f79d26","method":"POST","url":"/uniapp/order/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/order/createOrder","query":""}
{"level":"dev.error","ts":"[2025-09-03 15:29:31.756]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1861b535aa4d5760c5f79d26","method":"POST","url":"/uniapp/order/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0102288,"response_size":112,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.ShortUrlHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/short_url.go:19\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-09-03 15:31:42.035]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861b5540018f9b0c8147b78","method":"POST","url":"/uniapp/order/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/order/createOrder","query":""}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.952]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861b5540018f9b0c8147b78","method":"POST","url":"/uniapp/order/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":45.9177311,"response_size":916}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.413]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861b576508bde6896667eee","method":"POST","url":"/uniapp/order/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/uniapp/order/createOrder","query":""}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.298]","caller":"log/middleware.go:65","msg":"请求完成","request_id":"1861b576508bde6896667eee","method":"POST","url":"/uniapp/order/createOrder","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.884798,"response_size":916}
{"level":"dev.info","ts":"[2025-09-03 17:00:15.677]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ba292ddbfc10644a6d7f","method":"GET","url":"/business/channel/channelcontroller/checkUser","ip":"127.0.0.1","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/channel/channelcontroller/checkUser","query":""}
{"level":"dev.warn","ts":"[2025-09-03 17:00:15.678]","caller":"log/middleware.go:61","msg":"请求完成","request_id":"1861ba292ddbfc10644a6d7f","method":"GET","url":"/business/channel/channelcontroller/checkUser","ip":"127.0.0.1","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":404,"response_time":0.0015532,"response_size":103}
{"level":"dev.info","ts":"[2025-09-03 17:00:28.370]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ba2c226dccfc04e4565a","method":"POST","url":"/business/channel/channelcontroller/checkUser","ip":"127.0.0.1","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/channel/channelcontroller/checkUser","query":""}
{"level":"dev.info","ts":"[2025-09-03 17:01:18.866]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ba37e432855cd3f0d047","method":"POST","url":"/business/channel/channelcontroller/checkUser","ip":"127.0.0.1","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/channel/channelcontroller/checkUser","query":""}
{"level":"dev.info","ts":"[2025-09-03 17:02:47.790]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ba4c986cb8f82d9a4f92","method":"POST","url":"/business/channel/channelcontroller/checkUser","ip":"127.0.0.1","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/channel/channelcontroller/checkUser","query":""}
{"level":"dev.info","ts":"[2025-09-03 17:04:44.392]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861ba67be72108cb7dba814","method":"POST","url":"/business/channel/channelcontroller/checkUser","ip":"127.0.0.1","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/channel/channelcontroller/checkUser","query":""}
{"level":"dev.error","ts":"[2025-09-03 17:04:55.295]","caller":"log/middleware.go:63","msg":"请求完成","request_id":"1861ba67be72108cb7dba814","method":"POST","url":"/business/channel/channelcontroller/checkUser","ip":"127.0.0.1","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":10.9040221,"response_size":129,"stacktrace":"fincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:63\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/route/middleware.ShortUrlHandler.func1\n\tD:/work/code/fincore/go/src/route/middleware/short_url.go:19\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-09-03 17:09:02.596]","caller":"log/middleware.go:34","msg":"请求开始","request_id":"1861baa3dcb2840074125cee","method":"POST","url":"/business/channel/channelcontroller/checkUser","ip":"127.0.0.1","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/channel/channelcontroller/checkUser","query":""}
