{"level":"dev.info","ts":"[2025-09-03 11:30:19.525]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= UNIX_TIMESTAMP(?) AND baa.createtime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC) as count_query, [2024-01-17 2024-01-17]","duration":"38.756ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-03 11:30:19.556]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= UNIX_TIMESTAMP(?) AND baa.createtime <= UNIX_TIMESTAMP(?) ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2024-01-17 2024-01-17]","duration":"27.0674ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-03 11:31:00.049]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"47.4976ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-03 11:32:00.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"78.6686ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-09-03 11:32:00.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"88.5548ms","duration_ms":88}
{"level":"dev.info","ts":"[2025-09-03 11:32:00.089]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"87.9952ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-09-03 11:33:00.074]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"71.0097ms","duration_ms":71}
{"level":"dev.info","ts":"[2025-09-03 11:33:00.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"78.7342ms","duration_ms":78}
{"level":"dev.info","ts":"[2025-09-03 11:33:00.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"16.4828ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-03 11:34:00.069]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903113400","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"69.1496ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-09-03 11:34:00.098]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903113400","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"97.6612ms","duration_ms":97}
{"level":"dev.info","ts":"[2025-09-03 11:34:00.098]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903113400","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"98.2022ms","duration_ms":98}
{"level":"dev.info","ts":"[2025-09-03 11:35:00.053]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"52.6659ms","duration_ms":52}
{"level":"dev.info","ts":"[2025-09-03 11:49:19.488]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND DATE(baa.createtime) >= ? AND DATE(baa.createtime) <= ? ORDER BY baa.createtime DESC) as count_query, [2024-01-17 2024-01-17]","duration":"15.9558ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-03 11:49:19.519]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND DATE(baa.createtime) >= ? AND DATE(baa.createtime) <= ? ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2024-01-17 2024-01-17]","duration":"30.2402ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-03 11:49:35.601]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND DATE(baa.createtime) >= ? AND DATE(baa.createtime) <= ? ORDER BY baa.createtime DESC) as count_query, [2024-01-17 2024-01-17]","duration":"29.6192ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-03 11:49:35.632]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND DATE(baa.createtime) >= ? AND DATE(baa.createtime) <= ? ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2024-01-17 2024-01-17]","duration":"29.6247ms","duration_ms":29}
{"level":"dev.info","ts":"[2025-09-03 11:50:00.062]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903115000","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"61.2373ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-09-03 11:50:00.105]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903115000","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"104.2962ms","duration_ms":104}
{"level":"dev.info","ts":"[2025-09-03 11:50:00.105]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903115000","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"104.6487ms","duration_ms":104}
{"level":"dev.info","ts":"[2025-09-03 11:51:00.264]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"262.3459ms","duration_ms":262}
{"level":"dev.info","ts":"[2025-09-03 11:51:00.264]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"263.9793ms","duration_ms":263}
{"level":"dev.info","ts":"[2025-09-03 11:51:00.598]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"333.8669ms","duration_ms":333}
{"level":"dev.info","ts":"[2025-09-03 11:52:00.029]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"27.4471ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-03 11:52:00.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"87.9522ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-09-03 11:52:00.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"87.9522ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-09-03 11:52:04.941]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= ? AND baa.createtime <= ? ORDER BY baa.createtime DESC) as count_query, [2024-01-17 00:00:00 2024-01-17 23:59:59]","duration":"38.2756ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-03 11:52:04.977]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= ? AND baa.createtime <= ? ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2024-01-17 00:00:00 2024-01-17 23:59:59]","duration":"35.3915ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-03 11:52:05.744]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= ? AND baa.createtime <= ? ORDER BY baa.createtime DESC) as count_query, [2024-01-17 00:00:00 2024-01-17 23:59:59]","duration":"37.2058ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-03 11:52:05.770]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= ? AND baa.createtime <= ? ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [2024-01-17 00:00:00 2024-01-17 23:59:59]","duration":"25.3699ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-03 11:52:41.143]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= ? AND baa.createtime <= ? ORDER BY baa.createtime DESC) as count_query, [********** **********]","duration":"19.568ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 11:52:41.185]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= ? AND baa.createtime <= ? ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [********** **********]","duration":"40.288ms","duration_ms":40}
{"level":"dev.info","ts":"[2025-09-03 11:52:41.230]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, MAX(CASE WHEN paid_at IS NOT NULL THEN UNIX_TIMESTAMP(paid_at) ELSE NULL END) as last_repay_time, MIN(CASE WHEN status = 0 THEN UNIX_TIMESTAMP(due_date) ELSE NULL END) as bill_due_time FROM `business_repayment_bills` WHERE `user_id` IN (?) GROUP BY user_id, [17]","duration":"42.4773ms","duration_ms":42}
{"level":"dev.info","ts":"[2025-09-03 11:52:41.278]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [17]","duration":"90.3507ms","duration_ms":90}
{"level":"dev.info","ts":"[2025-09-03 11:52:41.962]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= ? AND baa.createtime <= ? ORDER BY baa.createtime DESC) as count_query, [********** **********]","duration":"25.1228ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-03 11:52:41.999]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 AND baa.createtime >= ? AND baa.createtime <= ? ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, [********** **********]","duration":"37.3141ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-03 11:52:42.039]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, MAX(CASE WHEN paid_at IS NOT NULL THEN UNIX_TIMESTAMP(paid_at) ELSE NULL END) as last_repay_time, MIN(CASE WHEN status = 0 THEN UNIX_TIMESTAMP(due_date) ELSE NULL END) as bill_due_time FROM `business_repayment_bills` WHERE `user_id` IN (?) GROUP BY user_id, [17]","duration":"39.1415ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-03 11:52:42.040]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?) GROUP BY user_id, [17]","duration":"39.6479ms","duration_ms":39}
{"level":"dev.info","ts":"[2025-09-03 11:53:00.025]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"24.837ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-03 11:53:03.898]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT COUNT(*) as total FROM (\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 ORDER BY baa.createtime DESC) as count_query, []","duration":"21.4163ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-03 11:53:03.945]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT\n\t\t\tbaa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,\n\t\t\tbaa.deviceSource, baa.userRemark, baa.createtime,\n\t\t\tbaa.identityStatus, baa.complaintStatus,\n\t\t\tbaa.allQuota, baa.reminderQuota, baa.riskScore,\n\t\t\tCOALESCE(c.channel_name, '') as channel_name,\n\t\t\tCOALESCE(ba.name, '超级管理员') as reviewer_name,\n\t\t\tbaa.idCardFrontUrl, baa.idCardBackUrl,\n\t\t\tCOALESCE(order_filter.orderStatus, 0) as orderStatus,\n\t\t\tCOALESCE(order_filter.loanCount, 0) as loanCount,\n\t\t\tCOALESCE(order_filter.loanTime, 0) as loanTime\n\t\tFROM business_app_account baa\n\t\tLEFT JOIN channel c ON baa.channelId = c.id\n\t\tLEFT JOIN business_account ba ON baa.reviewer = ba.id\n\t\tLEFT JOIN (\n\t\t\tSELECT\n\t\t\t\tuser_id,\n\t\t\t\tFIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,\n\t\t\t\tSUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,\n\t\t\t\tCOUNT(*) OVER (PARTITION BY user_id) as orderCount,\n\t\t\t\tFIRST_VALUE(UNIX_TIMESTAMP(created_at)) OVER (PARTITION BY user_id ORDER BY created_at DESC) as loanTime,\n\t\t\t\tROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn\n\t\t\tFROM business_loan_orders\n\t\t) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1\n\t\tWHERE 1=1 AND baa.deletedAt = 0 ORDER BY baa.createtime DESC LIMIT 20 OFFSET 0, []","duration":"47.5538ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-03 11:53:03.989]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, MAX(CASE WHEN paid_at IS NOT NULL THEN UNIX_TIMESTAMP(paid_at) ELSE NULL END) as last_repay_time, MIN(CASE WHEN status = 0 THEN UNIX_TIMESTAMP(due_date) ELSE NULL END) as bill_due_time FROM `business_repayment_bills` WHERE `user_id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) GROUP BY user_id, [34 33 32 31 30 29 28 26 25 24 23 22 21 20 19 18 17 16 15 14]","duration":"43.655ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-03 11:53:03.989]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count FROM `business_loan_orders` WHERE `user_id` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) GROUP BY user_id, [34 33 32 31 30 29 28 26 25 24 23 22 21 20 19 18 17 16 15 14]","duration":"43.655ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-03 12:08:48.782]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `product_rules` LIMIT 1, []","duration":"21.6178ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-03 12:08:48.815]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id, rule_name, loan_amount, loan_period, total_periods, guarantee_fee, annual_interest_rate, other_fees, rule_category FROM `product_rules` ORDER BY id desc LIMIT 10, []","duration":"32.7116ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-03 12:08:50.452]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861aa42122e9be0a0a2b482","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [27]","duration":"50.5955ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-09-03 12:10:51.033]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861aa5e26b8d50039cb11bd","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [27]","duration":"26.2029ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-03 12:50:12.908]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id,businessID,username,name,nickname,city,company,avatar,status FROM `business_account` WHERE `id` = ? LIMIT 1, [1]","duration":"47.7458ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-03 12:50:12.932]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `common_config` WHERE `keyname` = ? LIMIT 1, [rooturl]","duration":"21.8942ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.097]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role_access` WHERE `uid` = ?, [1]","duration":"18.3868ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_role` WHERE `id` IN (?), [1]","duration":"168.6489ms","duration_ms":168}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.302]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `status` = ? and `type` IN (?,?) ORDER BY orderNo asc, [0 0 1]","duration":"35.2469ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.322]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 8]","duration":"19.7172ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.347]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 70]","duration":"24.5459ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.380]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 69]","duration":"32.7811ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.406]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 68]","duration":"25.6576ms","duration_ms":25}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.438]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 463]","duration":"32.0364ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.466]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 460]","duration":"27.7334ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.491]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 451]","duration":"24.9121ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.528]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 458]","duration":"37.0032ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.555]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 474]","duration":"26.5035ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.575]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 450]","duration":"19.5624ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.598]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 457]","duration":"22.9755ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.613]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 456]","duration":"14.6936ms","duration_ms":14}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.638]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 464]","duration":"24.8568ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.665]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 465]","duration":"26.4592ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.687]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 455]","duration":"22.3516ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.705]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 440]","duration":"17.4521ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.742]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 439]","duration":"37.1343ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.778]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 467]","duration":"35.6766ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.811]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 468]","duration":"32.137ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.888]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 469]","duration":"77.0718ms","duration_ms":77}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.927]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 466]","duration":"38.4726ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-03 12:50:13.974]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 453]","duration":"46.9974ms","duration_ms":46}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.005]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 459]","duration":"30.6525ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.029]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 452]","duration":"23.9608ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 438]","duration":"50.1035ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.099]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 437]","duration":"18.5384ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.121]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 471]","duration":"21.6181ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.140]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 472]","duration":"19.2482ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.161]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 473]","duration":"20.3335ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.244]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 470]","duration":"83.0352ms","duration_ms":83}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.289]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 435]","duration":"43.2897ms","duration_ms":43}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.303]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 436]","duration":"13.9541ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.323]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 383]","duration":"19.8859ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.354]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 11]","duration":"30.5466ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.391]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 12]","duration":"36.5804ms","duration_ms":36}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.421]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 63]","duration":"28.2697ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.441]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 13]","duration":"19.2567ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.472]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 61]","duration":"30.3322ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.517]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 75]","duration":"44.7701ms","duration_ms":44}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.544]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 97]","duration":"27.4024ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.565]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 374]","duration":"20.8041ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-03 12:50:14.590]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_auth_rule` WHERE `type` = ? and `pid` = ?, [2 74]","duration":"24.4515ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-03 12:50:15.008]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT count(*) as count FROM `product_rules` LIMIT 1, []","duration":"47.4316ms","duration_ms":47}
{"level":"dev.info","ts":"[2025-09-03 12:50:15.057]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id, rule_name, loan_amount, loan_period, total_periods, guarantee_fee, annual_interest_rate, other_fees, rule_category FROM `product_rules` ORDER BY id desc LIMIT 10, []","duration":"49.2164ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-09-03 12:50:16.978]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861ac8503401bc8c9664f67","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [19]","duration":"41.1061ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-09-03 12:51:00.077]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"76.1313ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-09-03 12:51:00.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"106.6361ms","duration_ms":106}
{"level":"dev.info","ts":"[2025-09-03 12:51:00.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"19.0436ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 12:51:00.541]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861ac8f2602b940043ab1c2","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [27]","duration":"71.9613ms","duration_ms":71}
{"level":"dev.info","ts":"[2025-09-03 12:51:18.266]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861ac93478c15449e5ba677","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [26]","duration":"54.1405ms","duration_ms":54}
{"level":"dev.info","ts":"[2025-09-03 12:51:45.603]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861ac99a64a8eacd57ba236","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [26]","duration":"31.4164ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-03 12:53:47.440]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861acb604bfdfe882f21cf7","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [26]","duration":"23.5712ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-03 12:53:50.350]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861acb6b269a6ec5776b518","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [26]","duration":"21.9873ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-03 12:54:00.022]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"21.7529ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-03 12:54:00.087]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"86.2006ms","duration_ms":86}
{"level":"dev.info","ts":"[2025-09-03 12:54:00.087]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"85.0864ms","duration_ms":85}
{"level":"dev.info","ts":"[2025-09-03 12:54:00.107]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"104.618ms","duration_ms":104}
{"level":"dev.info","ts":"[2025-09-03 12:54:00.131]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"24.7767ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-03 12:54:18.542]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861acbd41c44d50a13151b6","sql":"SELECT * FROM `product_rules` WHERE `id` = ? and (deleted_at IS NULL) LIMIT 1, [26]","duration":"37.6948ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-03 12:55:00.117]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"116.486ms","duration_ms":116}
{"level":"dev.info","ts":"[2025-09-03 12:56:00.138]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"138.0388ms","duration_ms":138}
{"level":"dev.info","ts":"[2025-09-03 12:56:00.138]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"137.945ms","duration_ms":137}
{"level":"dev.info","ts":"[2025-09-03 12:56:00.147]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"146.6189ms","duration_ms":146}
{"level":"dev.info","ts":"[2025-09-03 12:57:00.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"80.0126ms","duration_ms":80}
{"level":"dev.info","ts":"[2025-09-03 12:57:00.090]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"89.3498ms","duration_ms":89}
{"level":"dev.info","ts":"[2025-09-03 12:57:00.116]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"23.7706ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-03 12:58:00.111]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903125800","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"111.3276ms","duration_ms":111}
{"level":"dev.info","ts":"[2025-09-03 12:58:00.142]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903125800","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"142.181ms","duration_ms":142}
{"level":"dev.info","ts":"[2025-09-03 12:58:00.142]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903125800","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"142.6963ms","duration_ms":142}
{"level":"dev.info","ts":"[2025-09-03 12:59:00.107]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"106.3134ms","duration_ms":106}
{"level":"dev.info","ts":"[2025-09-03 13:00:00.088]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"86.9866ms","duration_ms":86}
{"level":"dev.info","ts":"[2025-09-03 13:00:00.088]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"87.654ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-09-03 13:00:00.088]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"87.654ms","duration_ms":87}
{"level":"dev.info","ts":"[2025-09-03 13:00:00.103]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"102.3207ms","duration_ms":102}
{"level":"dev.info","ts":"[2025-09-03 13:00:00.137]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"33.9669ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-03 13:01:00.109]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"109.5624ms","duration_ms":109}
{"level":"dev.info","ts":"[2025-09-03 13:02:00.100]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"98.917ms","duration_ms":98}
{"level":"dev.info","ts":"[2025-09-03 13:02:00.100]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"99.6285ms","duration_ms":99}
{"level":"dev.info","ts":"[2025-09-03 13:02:00.101]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"100.0816ms","duration_ms":100}
{"level":"dev.info","ts":"[2025-09-03 13:03:00.741]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"740.5256ms","duration_ms":740}
{"level":"dev.info","ts":"[2025-09-03 13:03:00.749]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"747.9141ms","duration_ms":747}
{"level":"dev.info","ts":"[2025-09-03 13:03:00.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"70.7413ms","duration_ms":70}
{"level":"dev.info","ts":"[2025-09-03 13:04:00.150]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903130400","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"149.6138ms","duration_ms":149}
{"level":"dev.info","ts":"[2025-09-03 13:04:00.177]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903130400","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"176.1929ms","duration_ms":176}
{"level":"dev.info","ts":"[2025-09-03 13:04:00.177]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903130400","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"176.1929ms","duration_ms":176}
{"level":"dev.info","ts":"[2025-09-03 13:05:00.076]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"76.5085ms","duration_ms":76}
{"level":"dev.info","ts":"[2025-09-03 13:06:00.108]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"108.2987ms","duration_ms":108}
{"level":"dev.info","ts":"[2025-09-03 13:06:00.118]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"118.4532ms","duration_ms":118}
{"level":"dev.info","ts":"[2025-09-03 13:06:00.119]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"118.4458ms","duration_ms":118}
{"level":"dev.info","ts":"[2025-09-03 13:06:00.119]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"118.4458ms","duration_ms":118}
{"level":"dev.info","ts":"[2025-09-03 13:06:00.140]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"21.5093ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-03 13:07:00.105]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"103.4748ms","duration_ms":103}
{"level":"dev.info","ts":"[2025-09-03 13:08:00.068]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903130800","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"67.4607ms","duration_ms":67}
{"level":"dev.info","ts":"[2025-09-03 13:08:00.086]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903130800","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"85.0388ms","duration_ms":85}
{"level":"dev.info","ts":"[2025-09-03 13:08:00.086]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903130800","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"85.0388ms","duration_ms":85}
{"level":"dev.info","ts":"[2025-09-03 13:09:00.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"127.016ms","duration_ms":127}
{"level":"dev.info","ts":"[2025-09-03 13:09:00.127]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"127.9073ms","duration_ms":127}
{"level":"dev.info","ts":"[2025-09-03 13:09:00.145]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"17.1481ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-03 13:10:00.233]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"233.237ms","duration_ms":233}
{"level":"dev.info","ts":"[2025-09-03 13:10:00.255]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"254.6205ms","duration_ms":254}
{"level":"dev.info","ts":"[2025-09-03 13:10:00.255]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"254.6205ms","duration_ms":254}
{"level":"dev.info","ts":"[2025-09-03 13:11:00.067]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"66.3719ms","duration_ms":66}
{"level":"dev.info","ts":"[2025-09-03 13:12:00.238]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"238.378ms","duration_ms":238}
{"level":"dev.info","ts":"[2025-09-03 13:12:00.239]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"238.235ms","duration_ms":238}
{"level":"dev.info","ts":"[2025-09-03 13:12:00.239]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"238.235ms","duration_ms":238}
{"level":"dev.info","ts":"[2025-09-03 13:12:00.239]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"239.0049ms","duration_ms":239}
{"level":"dev.info","ts":"[2025-09-03 13:12:00.272]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"33.5744ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-03 15:29:00.145]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"144.5232ms","duration_ms":144}
{"level":"dev.info","ts":"[2025-09-03 15:30:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"62.847ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-09-03 15:30:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"61.1807ms","duration_ms":61}
{"level":"dev.info","ts":"[2025-09-03 15:30:00.063]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"62.2728ms","duration_ms":62}
{"level":"dev.info","ts":"[2025-09-03 15:30:00.072]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"69.749ms","duration_ms":69}
{"level":"dev.info","ts":"[2025-09-03 15:30:00.105]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"41.733ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-09-03 15:31:00.138]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"137.7105ms","duration_ms":137}
{"level":"dev.info","ts":"[2025-09-03 15:31:44.359]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [27]","duration":"31.9848ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-03 15:31:56.826]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id FROM `business_loan_orders` WHERE `user_id` = ? and `status` IN (?,?), [7 1 3]","duration":"31.699ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-03 15:31:56.864]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT channelId FROM `business_app_account` WHERE `id` = ? LIMIT 1, [7]","duration":"37.7424ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-03 15:31:56.892]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [27]","duration":"26.7865ms","duration_ms":26}
{"level":"dev.info","ts":"[2025-09-03 15:31:56.923]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `business_loan_orders` WHERE `contract_id` = ? LIMIT 1, [0]","duration":"31.2995ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-03 15:31:56.982]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"90.1227ms","duration_ms":90}
{"level":"dev.info","ts":"[2025-09-03 15:31:56.983]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `business_payment_channels` WHERE `id` = ? LIMIT 1, [1]","duration":"91.115ms","duration_ms":91}
{"level":"dev.info","ts":"[2025-09-03 15:31:56.991]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `business_loan_orders` WHERE `user_id` = ? and `status` IN (?,?), [7 0 1]","duration":"98.6792ms","duration_ms":98}
{"level":"dev.info","ts":"[2025-09-03 15:31:56.992]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `channel` WHERE `channel_code` = ? LIMIT 1, [TIAAWOLFPQ]","duration":"100.1837ms","duration_ms":100}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.486]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"32.8804ms","duration_ms":32}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.487]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"\n\t\tSELECT DISTINCT\n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.channel_transaction_no,\n\t\t\tbpt.status as transaction_status\n\t\tFROM business_loan_orders blo\n\t\tINNER JOIN business_payment_transactions bpt ON blo.order_no = bpt.order_no\n\t\tWHERE blo.status = ?\n\t\t  AND bpt.type = ?\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.channel_transaction_no != ''\n\t\t  AND bpt.channel_transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0 DISBURSEMENT 1]","duration":"33.3992ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.487]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"INSERT INTO `business_loan_orders` (`channel_id`,`created_at`,`order_no`,`status`,`user_id`,`total_interest`,`initial_order_channel_id`,`product_rule_id`,`principal`,`payment_channel_id`,`is_freeze`,`loan_amount`,`customer_origin`,`contract_id`,`total_other_fees`,`review_status`,`updated_at`,`total_guarantee_fee`,`total_repayable_amount`,`is_refund_needed`,`amount_paid`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [3 2025-09-03 15:32:27.4633792 +0800 CST m=+55.423879501 ****************** 0 7 0.03 3 27 28.00 1 0 30.00 H5 0 0.00 0 2025-09-03 15:32:27.4633792 +0800 CST m=+55.423879501 4.00 32.03 0 0.00]","duration":"23.4964ms","duration_ms":23}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.487]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"\n\t\tSELECT DISTINCT\n\t\t\tbrb.id as bill_id,\n\t\t\tbrb.order_id,\n\t\t\tbrb.user_id,\n\t\t\tbrb.period_number,\n\t\t\tbrb.status as bill_status,\n\t\t\tbrb.total_due_amount,\n\t\t\tbrb.paid_amount,\n\t\t\tbpt.transaction_no,\n\t\t\tbpt.type as transaction_type,\n\t\t\tbpt.status as transaction_status,\n\t\t\tbpt.amount,\n\t\t\tbpt.third_party_order_no,\n\t\t\tbpt.created_at as transaction_created_at\n\t\tFROM business_repayment_bills brb\n\t\tINNER JOIN business_payment_transactions bpt ON brb.id = bpt.bill_id\n\t\tWHERE brb.status IN (?, ?, ?, ?)\n\t\t  AND bpt.status = ?\n\t\t  AND bpt.type IN (?, ?, ?)\n\t\t  AND bpt.transaction_no != ''\n\t\t  AND bpt.transaction_no IS NOT NULL\n\t\t  AND bpt.deleted_at IS NULL\n\t\tORDER BY bpt.created_at ASC\n\t\tLIMIT 100\n\t, [0 3 7 9 1 REPAYMENT WITHHOLD MANUAL_WITHHOLD]","duration":"33.3992ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.525]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"INSERT INTO `business_repayment_bills` (`due_date`,`created_at`,`due_interest`,`period_number`,`due_other_fees`,`user_id`,`late_fee`,`updated_at`,`asset_management_entry`,`due_principal`,`paid_amount`,`deduct_retry_count`,`due_guarantee_fee`,`order_id`,`status`,`total_due_amount`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [2025-09-06 2025-09-03 15:32:27.4881421 +0800 CST m=+55.********* 0.01 1 0 7 0 2025-09-03 15:32:27.4881421 +0800 CST m=+55.********* 9.35 9.34 0 0 1.34 27 0 10.69]","duration":"37.3397ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.544]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"INSERT INTO `business_repayment_bills` (`due_interest`,`deduct_retry_count`,`due_other_fees`,`late_fee`,`created_at`,`order_id`,`asset_management_entry`,`period_number`,`user_id`,`due_guarantee_fee`,`status`,`updated_at`,`due_date`,`due_principal`,`total_due_amount`,`paid_amount`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [0.01 0 0 0 2025-09-03 15:32:27.4881421 +0800 CST m=+55.********* 27 9.35 2 7 1.34 0 2025-09-03 15:32:27.4881421 +0800 CST m=+55.********* 2025-09-09 9.34 10.69 0]","duration":"18.3254ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.575]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"INSERT INTO `business_repayment_bills` (`due_interest`,`due_guarantee_fee`,`late_fee`,`due_other_fees`,`due_date`,`due_principal`,`created_at`,`order_id`,`asset_management_entry`,`updated_at`,`deduct_retry_count`,`paid_amount`,`period_number`,`status`,`total_due_amount`,`user_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [0.01 1.32 0 0 2025-09-12 9.32 2025-09-03 15:32:27.4881421 +0800 CST m=+55.********* 27 9.33 2025-09-03 15:32:27.4881421 +0800 CST m=+55.********* 0 0 3 0 10.65 7]","duration":"30.3887ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.595]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"SELECT * FROM `business_app_account` WHERE `id` = ? LIMIT 1, [7]","duration":"19.1495ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.636]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"SELECT * FROM `risk_evaluations` WHERE `customer_id` = ? and `evaluation_source` = ? ORDER BY evaluation_time DESC LIMIT 1, [7 third_party]","duration":"37.2523ms","duration_ms":37}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.656]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"UPDATE `business_app_account` SET `reminderQuota` = ?,`riskScore` = ?,`allQuota` = ? WHERE `id` = ?, [0 0 0 7]","duration":"19.3448ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.681]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"UPDATE `business_loan_orders` SET `risk_control_results` = ?,`updated_at` = ? WHERE `id` = ?, [2 2025-09-03 15:32:27.6647522 +0800 CST m=+55.********* 27]","duration":"16.9274ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.701]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"task_repayment-status-sync-compensation_20250903153227","sql":"INSERT INTO `business_order_operation_logs` (`operator_name`,`action`,`details`,`created_at`,`order_id`,`operator_id`) VALUES (?,?,?,?,?,?), [系统 创建订单 订单编号: ******************, 申请金额: 30.00 2025-09-03 15:32:27.6822268 +0800 CST m=+55.********* 27 0]","duration":"19.0778ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.730]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [27]","duration":"19.2722ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.731]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"INSERT INTO `business_order_operation_logs` (`created_at`,`order_id`,`operator_id`,`operator_name`,`action`,`details`) VALUES (?,?,?,?,?,?), [2025-09-03 15:32:27.710767 +0800 CST m=+55.671267301 27 0 系统 创建订单 订单编号: ******************, 产品规则: csfg1, 放款金额: 32.03 (本金+利息+担保费+其他费用), 还款金额: 4.00 (资管费+担保费), 渠道ID: 3, 初始渠道ID: 3]","duration":"19.861ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.759]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `channel` WHERE `id` = ? LIMIT 1, [3]","duration":"28.2102ms","duration_ms":28}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.799]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [27]","duration":"38.3134ms","duration_ms":38}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.826]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"UPDATE `business_loan_orders` SET `reason_for_closure` = ?,`closure_remarks` = ?,`completed_at` = ?,`updated_at` = ?,`status` = ? WHERE `id` = ?, [-1 风控未通过，系统关单 - 2 2025-09-03 15:32:27.8077727 +0800 CST m=+55.768273001 2025-09-03 15:32:27.8077727 +0800 CST m=+55.768273001 2 27]","duration":"19.2262ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.849]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"UPDATE `business_repayment_bills` SET `status` = ?,`updated_at` = ? WHERE `order_id` = ?, [4 2025-09-03 15:32:27.8269989 +0800 CST m=+55.********* 27]","duration":"22.4556ms","duration_ms":22}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.870]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `business_app_account` WHERE `id` = ? LIMIT 1, [7]","duration":"21.4791ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.919]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `channel` WHERE `id` = ? LIMIT 1, [3]","duration":"48.1726ms","duration_ms":48}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.933]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"UPDATE `business_app_account` SET `reminderQuota` = ? WHERE `id` = ?, [30 7]","duration":"13.8909ms","duration_ms":13}
{"level":"dev.info","ts":"[2025-09-03 15:32:27.992]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [27]","duration":"41.3845ms","duration_ms":41}
{"level":"dev.info","ts":"[2025-09-03 15:32:28.023]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b5540018f9b0c8147b78","sql":"INSERT INTO `business_order_operation_logs` (`created_at`,`order_id`,`operator_id`,`operator_name`,`action`,`details`) VALUES (?,?,?,?,?,?), [2025-09-03 15:32:27.9933108 +0800 CST m=+55.********* 27 0 系统 关闭订单 订单号: ******************, 状态变更: 交易关闭 -> 交易关闭, 关闭原因: 风控不通过(-1), 备注: 风控未通过，系统关单 - 2, 操作人: 系统(ID:0)]","duration":"30.4912ms","duration_ms":30}
{"level":"dev.info","ts":"[2025-09-03 15:33:00.137]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `business_payment_transactions` WHERE `type` = ? ORDER BY id DESC LIMIT 1, [DISBURSEMENT]","duration":"136.3665ms","duration_ms":136}
{"level":"dev.info","ts":"[2025-09-03 15:33:00.182]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tid,\n\t\t\ttransaction_no,\n\t\t\torder_no,\n\t\t\tamount,\n\t\t\tcreated_at\n\t\tFROM business_payment_transactions \n\t\tWHERE type = ?\n\t\t  AND status = ?\n\t\t  AND deleted_at IS NULL\n\t\tORDER BY id ASC \n\t\tLIMIT 100\n\t, [REFUND 1]","duration":"181.9614ms","duration_ms":181}
{"level":"dev.info","ts":"[2025-09-03 15:33:00.200]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"\n\t\tSELECT \n\t\t\tblo.id,\n\t\t\tblo.order_no,\n\t\t\tblo.user_id,\n\t\t\tblo.channel_id,\n\t\t\tblo.loan_amount,\n\t\t\tblo.created_at,\n\t\t\tc.channel_name,\n\t\t\tc.auto_label\n\t\tFROM business_loan_orders blo\n\t\tLEFT JOIN channel c ON blo.channel_id = c.id\n\t\tWHERE blo.status = ?\n\t\t  AND blo.risk_control_results = 0\n\t\t  AND c.auto_label = 0\n\t\t  AND c.channel_status = 1\n\t\tORDER BY blo.created_at ASC\n\t\tLIMIT 100\n\t, [0]","duration":"63.8615ms","duration_ms":63}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.430]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [27]","duration":"15.7836ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.449]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT id FROM `business_loan_orders` WHERE `user_id` = ? and `status` IN (?,?), [7 1 3]","duration":"17.4976ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.468]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT channelId FROM `business_app_account` WHERE `id` = ? LIMIT 1, [7]","duration":"18.9331ms","duration_ms":18}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.503]","caller":"gform/sql_logger.go:114","msg":"SQL执行","sql":"SELECT * FROM `product_rules` WHERE `id` = ? LIMIT 1, [27]","duration":"35.1938ms","duration_ms":35}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.560]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `business_loan_orders` WHERE `contract_id` = ? LIMIT 1, [0]","duration":"56.904ms","duration_ms":56}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.620]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `channel` WHERE `channel_code` = ? LIMIT 1, [TIAAWOLFPQ]","duration":"115.7938ms","duration_ms":115}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.714]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `business_loan_orders` WHERE `order_no` = ? LIMIT 1, [******************]","duration":"210.3673ms","duration_ms":210}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.715]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `business_payment_channels` WHERE `id` = ? LIMIT 1, [1]","duration":"211.8435ms","duration_ms":211}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.715]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `business_loan_orders` WHERE `user_id` = ? and `status` IN (?,?), [7 0 1]","duration":"211.3379ms","duration_ms":211}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.749]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"INSERT INTO `business_loan_orders` (`product_rule_id`,`user_id`,`updated_at`,`pre_guarantee_fee`,`initial_order_channel_id`,`is_freeze`,`review_status`,`customer_origin`,`channel_id`,`pre_interest`,`created_at`,`total_repayable_amount`,`total_other_fees`,`payment_channel_id`,`status`,`order_no`,`amount_paid`,`contract_id`,`loan_amount`,`pre_principal`,`is_refund_needed`,`total_guarantee_fee`,`principal`,`total_interest`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [27 7 2025-09-03 15:34:09.726623 +0800 CST m=+3.008677001 1.00 3 0 0 H5 3 0.20 2025-09-03 15:34:09.726623 +0800 CST m=+3.008677001 32.03 0.00 1 0 ****************** 0.00 0 30.00 2.00 0 4.00 28.00 0.03]","duration":"21.9262ms","duration_ms":21}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.783]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"INSERT INTO `business_repayment_bills` (`late_fee`,`order_id`,`total_due_amount`,`paid_amount`,`status`,`asset_management_entry`,`due_date`,`due_other_fees`,`user_id`,`due_interest`,`due_guarantee_fee`,`due_principal`,`created_at`,`updated_at`,`deduct_retry_count`,`period_number`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [0 28 10.69 0 0 9.35 2025-09-06 0 7 0.01 1.34 9.34 2025-09-03 15:34:09.7496331 +0800 CST m=+3.********* 2025-09-03 15:34:09.7496331 +0800 CST m=+3.********* 0 1]","duration":"34.1118ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.802]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"INSERT INTO `business_repayment_bills` (`updated_at`,`order_id`,`user_id`,`late_fee`,`total_due_amount`,`due_date`,`due_principal`,`deduct_retry_count`,`created_at`,`status`,`due_interest`,`paid_amount`,`due_guarantee_fee`,`due_other_fees`,`period_number`,`asset_management_entry`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [2025-09-03 15:34:09.7496331 +0800 CST m=+3.********* 28 7 0 10.69 2025-09-09 9.34 0 2025-09-03 15:34:09.7496331 +0800 CST m=+3.********* 0 0.01 0 1.34 0 2 9.35]","duration":"17.7695ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.819]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"INSERT INTO `business_repayment_bills` (`due_guarantee_fee`,`due_other_fees`,`due_interest`,`updated_at`,`asset_management_entry`,`created_at`,`status`,`deduct_retry_count`,`period_number`,`paid_amount`,`due_principal`,`late_fee`,`due_date`,`order_id`,`total_due_amount`,`user_id`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?), [1.32 0 0.01 2025-09-03 15:34:09.7496331 +0800 CST m=+3.********* 9.33 2025-09-03 15:34:09.7496331 +0800 CST m=+3.********* 0 0 3 0 9.32 0 2025-09-12 28 10.65 7]","duration":"15.8664ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.845]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `business_app_account` WHERE `id` = ? LIMIT 1, [7]","duration":"24.5765ms","duration_ms":24}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.896]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `risk_evaluations` WHERE `customer_id` = ? and `evaluation_source` = ? ORDER BY evaluation_time DESC LIMIT 1, [7 third_party]","duration":"49.8542ms","duration_ms":49}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.930]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"UPDATE `business_app_account` SET `riskScore` = ?,`allQuota` = ?,`reminderQuota` = ? WHERE `id` = ?, [0 0 0 7]","duration":"33.4296ms","duration_ms":33}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.951]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"UPDATE `business_loan_orders` SET `risk_control_results` = ?,`updated_at` = ? WHERE `id` = ?, [2 2025-09-03 15:34:09.9307033 +0800 CST m=+3.********* 28]","duration":"19.8486ms","duration_ms":19}
{"level":"dev.info","ts":"[2025-09-03 15:34:09.968]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"INSERT INTO `business_order_operation_logs` (`details`,`created_at`,`order_id`,`operator_id`,`operator_name`,`action`) VALUES (?,?,?,?,?,?), [订单编号: ******************, 申请金额: 30.00 2025-09-03 15:34:09.9515273 +0800 CST m=+3.********* 28 0 系统 创建订单]","duration":"16.9389ms","duration_ms":16}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.079]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [28]","duration":"50.1678ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.080]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"INSERT INTO `business_order_operation_logs` (`operator_name`,`action`,`details`,`created_at`,`order_id`,`operator_id`) VALUES (?,?,?,?,?,?), [系统 创建订单 订单编号: ******************, 产品规则: csfg1, 放款金额: 32.03 (本金+利息+担保费+其他费用), 还款金额: 4.00 (资管费+担保费), 渠道ID: 3, 初始渠道ID: 3 2025-09-03 15:34:10.0295993 +0800 CST m=+3.311653301 28 0]","duration":"50.6768ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.114]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `channel` WHERE `id` = ? LIMIT 1, [3]","duration":"34.4572ms","duration_ms":34}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.132]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [28]","duration":"17.7051ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.170]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"UPDATE `business_loan_orders` SET `completed_at` = ?,`updated_at` = ?,`status` = ?,`reason_for_closure` = ?,`closure_remarks` = ? WHERE `id` = ?, [2025-09-03 15:34:10.1551311 +0800 CST m=+3.437185101 2025-09-03 15:34:10.1551311 +0800 CST m=+3.437185101 2 -1 风控未通过，系统关单 - 2 28]","duration":"15.5928ms","duration_ms":15}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.189]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"UPDATE `business_repayment_bills` SET `status` = ?,`updated_at` = ? WHERE `order_id` = ?, [4 2025-09-03 15:34:10.1707239 +0800 CST m=+3.********* 28]","duration":"17.7795ms","duration_ms":17}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.240]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `business_app_account` WHERE `id` = ? LIMIT 1, [7]","duration":"50.4306ms","duration_ms":50}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.267]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `channel` WHERE `id` = ? LIMIT 1, [3]","duration":"27.5254ms","duration_ms":27}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.289]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"UPDATE `business_app_account` SET `reminderQuota` = ? WHERE `id` = ?, [30 7]","duration":"20.9725ms","duration_ms":20}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.328]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"SELECT * FROM `business_loan_orders` WHERE `id` = ? LIMIT 1, [28]","duration":"31.3336ms","duration_ms":31}
{"level":"dev.info","ts":"[2025-09-03 15:34:10.346]","caller":"gform/sql_logger.go:114","msg":"SQL执行","request_id":"1861b576508bde6896667eee","sql":"INSERT INTO `business_order_operation_logs` (`details`,`created_at`,`order_id`,`operator_id`,`operator_name`,`action`) VALUES (?,?,?,?,?,?), [订单号: ******************, 状态变更: 交易关闭 -> 交易关闭, 关闭原因: 风控不通过(-1), 备注: 风控未通过，系统关单 - 2, 操作人: 系统(ID:0) 2025-09-03 15:34:10.3284382 +0800 CST m=+3.********* 28 0 系统 关闭订单]","duration":"15.8607ms","duration_ms":15}
