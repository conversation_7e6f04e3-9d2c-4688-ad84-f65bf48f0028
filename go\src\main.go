package main

import (
	"context"
	Service "fincore/app/dianziqian/service"
	"fincore/app/scheduler"
	"fincore/migrate"
	"fincore/utils/config"
	"fincore/utils/ipUtil"

	// "fincore/app/scheduler"
	"fincore/bootstrap"
	"fincore/global"
	"runtime"
	"strconv"

	"golang.org/x/sync/errgroup"
)

func main() {
	// 初始化临时日志
	global.App.Log = bootstrap.InitializeLog()
	defer global.App.Log.Sync()

	// 初始化配置
	global.App.Config = config.InitializeConfig("fincore")

	// 在配置加载后重新初始化日志
	global.App.Log = bootstrap.ReinitializeLog()

	var (
		g   *errgroup.Group
		err error
		p   any
	)

	g, _ = errgroup.WithContext(context.Background())

	// 进行数据库迁移
	g.Go(func() error {
		defer func() {
			if p = recover(); err != nil {
				global.App.Log.Error("数据库迁移失败: " + p.(string))
			}
		}()
		migrate.Migrate()
		return nil
	})

	// 初始化IP地址解析工具
	g.Go(func() error {
		if err = ipUtil.InitIPUtil("resource/developer/qqwry.dat"); err != nil {
			global.App.Log.Error("初始化IP工具失败: " + err.Error())
		}
		return err
	})

	Service.InitService()

	g.Go(func() error {
		// 初始化定时任务调度器
		if err = scheduler.Initialize(); err != nil {
			global.App.Log.Error("初始化定时任务调度器失败: " + err.Error())
		}

		// 启动定时任务调度器
		if err = scheduler.Start(); err != nil {
			global.App.Log.Error("启动定时任务调度器失败: " + err.Error())
		}

		return err
	})

	if err = g.Wait(); err != nil {
		panic(err)
	}

	if p != nil {
		panic(p)
	}

	//加载配置
	cpu_num, _ := strconv.Atoi(global.App.Config.App.CPUnum)
	mycpu := runtime.NumCPU()
	if cpu_num > mycpu { //如果配置cpu核数大于当前计算机核数，则等当前计算机核数
		cpu_num = mycpu
	}
	if cpu_num > 0 {
		runtime.GOMAXPROCS(cpu_num)
	} else {
		runtime.GOMAXPROCS(mycpu)
	}

	// 启动服务器
	bootstrap.RunServer()
}
