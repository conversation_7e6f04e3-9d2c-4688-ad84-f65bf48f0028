package credit

import (
	"fincore/global"
	"fincore/query_app/service"
	"fincore/query_app/utils"
	"fincore/utils/results"
	"strconv"

	"github.com/gin-gonic/gin"
)

func Query(c *gin.Context) {
	validationResult := utils.ParamsValidateAndGet(c, GetQuerySchema)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}
	result, err := service.GetUserCredit(c, validationResult.Data)
	if err != nil {
		results.Failed(c, "查询失败, "+err.Error(), nil)
		return
	}
	results.Success(c, "查询成功", result, nil)
}

func CreateQuery(c *gin.Context) {
	validationResult := utils.ParamsValidateAndGet(c, GetCreateQuerySchema)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}
	result, err := service.CreateQuery(c, validationResult.Data)
	if err != nil {
		results.Failed(c, "创建查询失败, "+err.<PERSON>rror(), nil)
		return
	}
	results.Success(c, "创建查询成功", result, nil)
}

func Detail(c *gin.Context) {
	validationResult := utils.ParamsValidateAndGet(c, GetDetailSchema)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}
	detail, err := service.GetQueryDetail(c, validationResult.Data)
	if err != nil {
		results.Failed(c, "查询失败, "+err.Error(), nil)
		return
	}
	results.Success(c, "查询成功", detail, nil)
}

// 价格查询
func Price(c *gin.Context) {
	results.Success(c, "查询成功", gin.H{
		"leidaPrice":   global.App.Config.App.LeidaPrice,
		"tanzhenPrice": global.App.Config.App.TanZhencPrice,
	}, nil)
}

// 余额查询
func Balance(c *gin.Context) {
	balance, err := service.GetBalance(c)
	if err != nil {
		results.Failed(c, "查询失败", err.Error())
		return
	}
	results.Success(c, "查询成功", gin.H{
		"balance": balance,
	}, nil)
}

// 余额查询
func CompanyBalance(c *gin.Context) {
	balance, err := service.GetCompanyBalance(c)
	if err != nil {
		results.Failed(c, "查询失败", err.Error())
		return
	}
	results.Success(c, "查询成功", balance, nil)
}

func Record(c *gin.Context) {
	idstr := c.Query("id")
	if idstr == "" {
		results.Failed(c, "参数验证失败", "id不能为空")
		return
	}
	id, err := strconv.Atoi(idstr)
	if err != nil {
		results.Failed(c, "参数验证失败", "id格式错误")
		return
	}
	record, err := service.GetQueryRecord(c, id)
	if err != nil {
		results.Failed(c, "查询失败", err.Error())
		return
	}
	results.Success(c, "查询成功", record, nil)
}

func DelRecord(c *gin.Context) {
	idstr := c.Query("id")
	if idstr == "" {
		results.Failed(c, "参数验证失败", "id不能为空")
		return
	}
	id, err := strconv.Atoi(idstr)
	if err != nil {
		results.Failed(c, "参数验证失败", "id格式错误")
		return
	}
	err = service.DelQueryRecord(c, id)
	if err != nil {
		results.Failed(c, "删除失败", err.Error())
		return
	}
	results.Success(c, "删除成功", nil, nil)
}
