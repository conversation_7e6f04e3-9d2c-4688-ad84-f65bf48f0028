package router

import (
	//一定要导入这个Controller包，用来注册需要访问的方法
	//这里路由-由构架是添加-开发者仅在指定工程目录下controller.go文件添加宝即可

	"fincore/app/business/captcha"
	"fincore/app/business/system"
	"fincore/app/business/user"
	"fincore/query_app/api/credit"
	"fincore/query_app/api/finance"
	"fincore/route/middleware"
	"net/http"
	"reflect"

	"fincore/global"
	"fincore/utils/log"
	"strings"
	"time"

	//工具

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// 路由初始化
func InitRouter() *gin.Engine {
	//初始化路由
	R := gin.Default()
	R.SetTrustedProxies([]string{"127.0.0.1"})

	//a.2.附件访问
	R.Static("/resource", "./resource")
	//a.3.业务后台
	R.Static("/webadmin", "./resource/webadmin")
	<PERSON><PERSON>("/webbusiness", "./resource/webbusiness")
	R.LoadHTMLFiles("./resource/developer/template/install.html", "./resource/developer/template/isinstall.html")
	//访问域名根目录重定向
	R.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, global.App.Config.App.Rootview)
	})
	//控制台日志级别
	gin.SetMode(global.App.Config.App.RunlogType) //ReleaseMode 为方便调试，Gin 框架在运行的时候默认是debug模式，在控制台默认会打印出很多调试日志，上线的时候我们需要关闭debug模式，改为release模式。
	// 为 multipart forms 设置较低的内存限制 (默认是 32 MiB)
	R.MaxMultipartMemory = 8 << 20 // 8 MiB
	//0.跨域访问-注意跨域要放在gin.Default下
	var str_arr []string
	if global.App.Config.App.Allowurl != "" {
		str_arr = strings.Split(global.App.Config.App.Allowurl, `,`)
	} else {
		str_arr = []string{"http://localhost:8110"}
	}

	R.Use(cors.New(cors.Config{
		AllowOrigins: str_arr,
		// AllowOriginFunc:  func(origin string) bool { return true },
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"},
		AllowHeaders:     []string{"X-Requested-With", "Content-Type", "Authorization", "Businessid", "verify-encrypt", "ignoreCancelToken", "verify-time", "apiverify"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))
	//1.日志中间件 - 请求ID和访问日志
	R.Use(log.RequestIDMiddleware())
	R.Use(log.AccessLogMiddleware())
	//R.Use(middleware.OperateLogHandler())
	//2.错误日志
	R.Use(gin.Logger(), middleware.CustomRecovery())
	R.Use(log.ErrorLogMiddleware())
	//3.限流rate-limit 中间件
	R.Use(middleware.LimitHandler())
	//4.判断接口是否合法
	R.Use(middleware.ValidityAPi())
	//5.验证token
	R.Use(middleware.JwtVerify)
	//6.找不到路由
	R.NoRoute(func(c *gin.Context) {
		path := c.Request.URL.Path
		method := c.Request.Method

		// 记录404日志
		log.WithGinContext(c).WithFields(
			log.String("path", path),
			log.String("method", method),
		).Warn("路由不存在")

		c.JSON(404, gin.H{"code": 404, "message": "您" + method + "请求地址：" + path + "不存在！"})
	})
	// 获取验证码
	R.GET("/business/captcha/getCaptcha", reflect.ValueOf(&captcha.Index{}).MethodByName("GetCaptcha").Interface().(func(c *gin.Context)))
	admin := R.Group("/business/user")
	{
		// 	Login = '/user/login ',
		//    registerUser = '/user/registerUser ',
		//    Logout = '/user/logout',
		//    getCode = '/user/get_code',
		//    resetPassword = '/user/resetPassword',
		//    GetUserInfo = '/user/get_userinfo',
		//    GetMenu = '/user/account/get_menu',
		//    getlogininfo = '/user/get_logininfo',
		// 登录
		admin.POST("/login", reflect.ValueOf(&user.Index{}).MethodByName("Login").Interface().(func(c *gin.Context)))
		// 注册
		admin.POST("/registerUser", reflect.ValueOf(&user.Index{}).MethodByName("RegisterUser").Interface().(func(c *gin.Context)))
		// 退出登录
		admin.POST("/logout", reflect.ValueOf(&user.Index{}).MethodByName("Logout").Interface().(func(c *gin.Context)))
		// 刷新token
		admin.POST("/refreshtoken", reflect.ValueOf(&user.Index{}).MethodByName("Refreshtoken").Interface().(func(c *gin.Context)))
		// 获取登录信息
		admin.GET("/get_logininfo", reflect.ValueOf(&user.Index{}).MethodByName("Get_logininfo").Interface().(func(c *gin.Context)))
		// 发送短信验证码
		admin.POST("/postSms", reflect.ValueOf(&user.Index{}).MethodByName("PostSms").Interface().(func(c *gin.Context)))
		// 重置密码
		admin.POST("/resetPassword", reflect.ValueOf(&user.Index{}).MethodByName("ResetPassword").Interface().(func(c *gin.Context)))
		// 获取用户信息
		admin.GET("/get_userinfo", reflect.ValueOf(&user.Index{}).MethodByName("Get_userinfo").Interface().(func(c *gin.Context)))
		// 获取菜单
		admin.GET("/account/get_menu", reflect.ValueOf(&user.Account{}).MethodByName("Get_menu").Interface().(func(c *gin.Context)))
		// 		saveInfo = '/user/data/saveInfo',
		//    GetUser = '/user/data/get_user',
		//    checkPassword = '/user/data/checkPassword',
		//    changePassword = '/user/data/changePassword',
		admin.GET("/data/get_user", reflect.ValueOf(&user.Data{}).MethodByName("Get_user").Interface().(func(c *gin.Context)))
		admin.POST("/data/saveInfo", reflect.ValueOf(&user.Data{}).MethodByName("SaveInfo").Interface().(func(c *gin.Context)))
		admin.POST("/data/checkPassword", reflect.ValueOf(&user.Data{}).MethodByName("CheckPassword").Interface().(func(c *gin.Context)))
		admin.POST("/data/changePassword", reflect.ValueOf(&user.Data{}).MethodByName("ChangePassword").Interface().(func(c *gin.Context)))

	}

	sys := R.Group("/business/system")
	{
		// getList = '/system/account/get_list',
		// getRole = '/system/account/get_role',
		// save = '/system/account/save',
		// Isaccountexist = '/system/account/isaccountexist',
		// upStatus = '/system/account/upStatus',
		// del = '/system/account/del',
		sys.GET("/account/get_list", reflect.ValueOf(&system.Account{}).MethodByName("Get_list").Interface().(func(c *gin.Context)))
		sys.GET("/account/get_role", reflect.ValueOf(&system.Account{}).MethodByName("Get_role").Interface().(func(c *gin.Context)))
		sys.POST("/account/save", reflect.ValueOf(&system.Account{}).MethodByName("Save").Interface().(func(c *gin.Context)))
		sys.POST("/account/isaccountexist", reflect.ValueOf(&system.Account{}).MethodByName("Isaccountexist").Interface().(func(c *gin.Context)))
		sys.POST("/account/upStatus", reflect.ValueOf(&system.Account{}).MethodByName("UpStatus").Interface().(func(c *gin.Context)))
		sys.DELETE("/account/del", reflect.ValueOf(&system.Account{}).MethodByName("Del").Interface().(func(c *gin.Context)))

		// getList = '/system/dept/get_list',
		// getParent = '/system/dept/get_parent',
		// save = '/system/dept/save',
		// upStatus = '/system/dept/upStatus',
		// del = '/system/dept/del',
		sys.GET("/dept/get_list", reflect.ValueOf(&system.Dept{}).MethodByName("Get_list").Interface().(func(c *gin.Context)))
		sys.GET("/dept/get_parent", reflect.ValueOf(&system.Dept{}).MethodByName("Get_parent").Interface().(func(c *gin.Context)))
		sys.POST("/dept/save", reflect.ValueOf(&system.Dept{}).MethodByName("Save").Interface().(func(c *gin.Context)))
		sys.POST("/dept/upStatus", reflect.ValueOf(&system.Dept{}).MethodByName("UpStatus").Interface().(func(c *gin.Context)))
		sys.DELETE("/dept/del", reflect.ValueOf(&system.Dept{}).MethodByName("Del").Interface().(func(c *gin.Context)))
		// GetList = '/system/member/get_list',
		sys.GET("/member/get_list", reflect.ValueOf(&system.Member{}).MethodByName("Get_list").Interface().(func(c *gin.Context)))
		// 	getList = '/system/role/get_list',
		// getParent = '/system/role/get_parent',
		// getMenuList = '/system/role/get_menuList',
		// save = '/system/role/save',
		// upStatus = '/system/role/upStatus',
		// del = '/system/role/del',
		sys.GET("/role/get_list", reflect.ValueOf(&system.Role{}).MethodByName("Get_list").Interface().(func(c *gin.Context)))
		sys.GET("/role/get_parent", reflect.ValueOf(&system.Role{}).MethodByName("Get_parent").Interface().(func(c *gin.Context)))
		sys.GET("/role/get_menuList", reflect.ValueOf(&system.Role{}).MethodByName("Get_menuList").Interface().(func(c *gin.Context)))
		sys.POST("/role/save", reflect.ValueOf(&system.Role{}).MethodByName("Save").Interface().(func(c *gin.Context)))
		sys.POST("/role/upStatus", reflect.ValueOf(&system.Role{}).MethodByName("UpStatus").Interface().(func(c *gin.Context)))
		sys.DELETE("/role/del", reflect.ValueOf(&system.Role{}).MethodByName("Del").Interface().(func(c *gin.Context)))
		// 	getList = '/system/rule/get_list',
		// getParent = '/system/rule/get_parent',
		// save = '/system/rule/save',
		// upStatus = '/system/rule/upStatus',
		// del = '/system/rule/del',
		sys.GET("/rule/get_list", reflect.ValueOf(&system.Rule{}).MethodByName("Get_list").Interface().(func(c *gin.Context)))
		sys.GET("/rule/get_parent", reflect.ValueOf(&system.Rule{}).MethodByName("Get_parent").Interface().(func(c *gin.Context)))
		sys.POST("/rule/save", reflect.ValueOf(&system.Rule{}).MethodByName("Save").Interface().(func(c *gin.Context)))
		sys.POST("/rule/upStatus", reflect.ValueOf(&system.Rule{}).MethodByName("UpStatus").Interface().(func(c *gin.Context)))
		sys.DELETE("/rule/del", reflect.ValueOf(&system.Rule{}).MethodByName("Del").Interface().(func(c *gin.Context)))
		sys.GET("/company/balance", credit.CompanyBalance)
	}
	// 信用速查
	creditApi := R.Group("/business/credit")
	{
		creditApi.POST("/query", credit.Query)
		creditApi.POST("/detail", credit.Detail)
		creditApi.GET("/price", credit.Price)
		creditApi.GET("/balance", credit.Balance)
		creditApi.GET("/record", credit.Record)
		creditApi.DELETE("/record", credit.DelRecord)
		creditApi.POST("/createQuery", credit.CreateQuery)
	}
	// 财务统计
	financeApi := R.Group("/business/finance")
	{
		financeApi.GET("/balance", finance.Balance)
		financeApi.POST("/queryDetail", finance.QueryDetail)
		financeApi.POST("/rechargeDetail", finance.RechargeDetail)
		financeApi.POST("/recharge", finance.Recharge)
	}

	return R
}
