package service

import (
	"encoding/json"
	"errors"
	"fincore/global"
	"fincore/query_app/models"
	"fincore/query_app/models/dao/model"
	"fincore/query_app/repository"
	"fincore/route/middleware"
	"fincore/utils/convert"
	"fincore/utils/log"
	"fincore/utils/pagination"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const LeiDaId = "leida_v4"
const TanZhenId = "tan_zhen_c"

func GetUserCredit(c *gin.Context, req map[string]interface{}) (*model.QueryDetail, error) {
	productId := convert.GetStringFromMap(req, "product_id")
	name := convert.GetStringFromMap(req, "name")
	mobile := convert.GetStringFromMap(req, "mobile")
	idNo := convert.GetStringFromMap(req, "idNo")
	getuser, _ := c.Get("user")
	user := getuser.(*middleware.UserClaims)
	// 先看当天有没有查询过该用户，有就返回查询数据
	queryDetail := model.QueryDetail{}
	err := models.DB.Where("name = ? and mobile = ? and id_no = ? and product_id = ? and created_at >= ? ",
		name, mobile, idNo, productId, time.Now().Truncate(24*time.Hour)).First(&queryDetail).Error
	if err == nil && queryDetail.ID > 0 {

		return &queryDetail, nil
	}

	rsp, err := CallExternalAPI(queryUrl, req)
	if err != nil {
		log.App().Errorf("调用查询接口失败, err: %v", err)
		return nil, errors.New("调用查询接口失败")
	}
	if rsp.Code != 0 {
		log.App().Errorf("查询失败, err: %v", rsp)
		return nil, fmt.Errorf("查询失败: %v", rsp.Message)
	}
	if rsp.Data.Code != "success" {
		log.App().Errorf("查询失败, err: %s", rsp.Data.Message)
		return nil, errors.New(rsp.Data.Message)
	}
	data, err := json.Marshal(rsp.Data.Data)
	if err != nil {
		log.App().Errorf("序列化查询数据失败, err: %v", err)
		return nil, errors.New("序列化查询数据失败")
	}
	deptAmount, err := repository.GetDepartmentAmount(user.DeptID)
	if err != nil {
		log.App().Errorf("获取部门金额失败, err: %v", err)
		return nil, errors.New("获取部门金额失败")
	}

	price := getPrice(productId)
	if *deptAmount.Amount < price {
		return nil, errors.New("部门余额不足")
	}
	*deptAmount.Amount -= price
	// 扣减余额与保存查询详情确保一同成功
	err = models.DB.Transaction(func(tx *gorm.DB) error {
		if err = tx.Clauses(clause.Locking{Strength: "UPDATE"}).Save(deptAmount).Error; err != nil {
			return err
		}

		queryDetail = model.QueryDetail{
			UserID:       int32(user.ID),
			ProductID:    productId,
			DepartmentID: int32(user.DeptID),
			CreatedAt:    time.Now(),
			Name:         name,
			Mobile:       mobile,
			IDNo:         idNo,
			Data:         string(data),
			Balance:      *deptAmount.Amount,
			Price:        price,
		}
		if err := tx.Create(&queryDetail).Error; err != nil {
			return err
		}

		// 返回 nil 提交事务
		return nil
	})

	if err != nil {
		log.App().Errorf("保存查询详情失败, err: %v", err)
		return nil, errors.New("保存查询详情失败")
	}

	return &queryDetail, nil
}

func CreateQuery(c *gin.Context, req map[string]interface{}) (*model.Query, error) {
	getuser, _ := c.Get("user")
	user := getuser.(*middleware.UserClaims)
	query := &model.Query{
		UserID:       int32(user.ID),
		DepartmentID: int32(user.DeptID),
		Name:         convert.GetStringFromMap(req, "name"),
		IDNo:         convert.GetStringFromMap(req, "idNo"),
		Mobile:       convert.GetStringFromMap(req, "mobile"),
		CreatedAt:    time.Now(),
	}
	err := models.DB.Create(query).Error
	if err != nil {
		log.App().Errorf("创建查询记录失败, err: %v", err)
		return nil, errors.New("创建查询记录失败")
	}
	return query, nil
}

func getPrice(productId string) float64 {
	switch productId {
	case LeiDaId:
		return global.App.Config.App.LeidaPrice
	case TanZhenId:
		return global.App.Config.App.TanZhencPrice
	case "both":
		return global.App.Config.App.LeidaPrice + global.App.Config.App.TanZhencPrice
	default:
		return 0
	}
}

func GetBalance(c *gin.Context) (float64, error) {
	getuser, _ := c.Get("user")
	user := getuser.(*middleware.UserClaims)
	deptAmount, err := repository.GetDepartmentAmount(user.DeptID)
	if err != nil {
		log.App().Errorf("获取部门金额失败, err: %v", err)
		return 0, errors.New("获取部门金额失败")
	}
	return *deptAmount.Amount, nil
}

func GetCompanyBalance(c *gin.Context) (map[string]interface{}, error) {
	getuser, _ := c.Get("user")
	user := getuser.(*middleware.UserClaims)
	if user.RoleID != 1 {
		return nil, errors.New("权限不足")
	}
	rsp, err := CallExternalAPI(balanceUrl, nil)
	if err != nil {
		log.App().Errorf("调用余额查询接口失败, err: %v", err)
		return nil, errors.New("调用查询接口失败")
	}
	if rsp.Code != 0 {
		log.App().Errorf("查询失败, err: %v", rsp)
		return nil, fmt.Errorf("查询失败: %s", rsp.Message)
	}
	return rsp.Data.Data, nil
}

func GetQueryDetail(c *gin.Context, req map[string]interface{}) (*pagination.PaginationResponse, error) {
	getuser, _ := c.Get("user")
	user := getuser.(*middleware.UserClaims)

	queryDetail, err := repository.GetQueryDetail(req, user)
	if err != nil {
		log.App().Errorf("获取查询详情失败, err: %v", err)
		return nil, errors.New("获取查询详情失败")
	}
	return queryDetail, nil
}

func GetFinanceQueryDetail(c *gin.Context, req map[string]interface{}) (*pagination.PaginationResponse, error) {
	getuser, _ := c.Get("user")
	user := getuser.(*middleware.UserClaims)

	queryDetail, err := repository.GetFinanceQueryDetail(req, user)
	if err != nil {
		log.App().Errorf("获取查询明细失败, err: %v", err)
		return nil, errors.New("获取查询明细失败")
	}
	return queryDetail, nil
}

func GetQueryRecord(c *gin.Context, id int) (*model.Query, error) {
	query := &model.Query{}
	getuser, _ := c.Get("user")
	user := getuser.(*middleware.UserClaims)
	if user.RoleID == 1 {
		err := models.DB.Where("id = ? ", id).First(query).Error
		if err != nil {
			log.App().Errorf("获取查询详情失败, err: %v", err)
			return nil, errors.New("获取查询详情失败")
		}
		return query, nil
	}
	// 管理员可以查所有
	if strings.Contains(user.Remark, "管理员") {
		err := models.DB.Where("id = ? and department_id = ?", id, user.DeptID).First(query).Error
		if err != nil {
			log.App().Errorf("获取查询详情失败, err: %v", err)
			return nil, errors.New("获取查询详情失败")
		}
		return query, nil
	}
	// 普通用户只能查自己的
	err := models.DB.Where("id = ? and user_id = ? and department_id = ?", id, user.ID, user.DeptID).First(query).Error
	if err != nil {
		log.App().Errorf("获取查询详情失败, err: %v", err)
		return nil, errors.New("获取查询详情失败")
	}
	return query, nil
}

func DelQueryRecord(c *gin.Context, id int) error {
	getuser, _ := c.Get("user")
	user := getuser.(*middleware.UserClaims)
	db := models.DB

	if user.RoleID == 1 {
		db = db.Where("id = ? ", id)
	}
	// 管理员可以删除部门所有
	if strings.Contains(user.Remark, "管理员") {
		db = db.Where("id = ? and department_id = ?", id, user.DeptID)
	} else {
		// 普通用户只能删除自己的
		db = db.Where("id = ? and user_id = ? and department_id = ?", id, user.ID, user.DeptID)
	}

	err := db.Delete(&model.Query{}).Error
	if err != nil {
		log.App().Errorf("删除查询记录失败, err: %v", err)
		return errors.New("删除查询记录失败")
	}
	return nil
}
