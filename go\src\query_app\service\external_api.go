package service

import (
	"encoding/json"
	"fincore/global"
	"fmt"
	"time"

	"github.com/go-resty/resty/v2"
)

const queryUrl = "/api/risk/check"
const balanceUrl = "/api/company/balance"

func CallExternalAPI(path string, req map[string]interface{}) (*QueryRsp, error) {
	client := resty.New()
	client.SetTimeout(5 * time.Second) // 全局超时

	// 设置默认 Header
	client.SetHeaders(map[string]string{
		"Content-Type": "application/json",
	})

	// 配置重试策略（失败自动重试）
	client.SetRetryCount(3).
		SetRetryWaitTime(1 * time.Second).
		SetRetryMaxWaitTime(3 * time.Second)
	qurl := global.App.Config.App.QueryServer + path
	resp, err := client.R().
		SetBody(req).
		Post(qurl)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %v", err)
	}

	// 状态码判断
	if !resp.IsSuccess() {
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode())
	}
	var result *QueryRsp
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return result, nil
}

// {
//     "code": 0,
//     "data": {
//         "code": "string",
//         "data": {
//             "acc_exc": "string",
//             "acc_sleep": "string",
//             "count_performance": "string",
//             "currently_overdue": "string",
//             "currently_performance": "string",
//             "latest_performance_time": "string",
//             "max_performance_amt": "string",
//             "result_code": "string"
//         },
//         "message": "string",
//         "requestId": "string",
//         "requestTime": "string"
//     },
//     "message": "成功"
// }

type QueryRsp struct {
	Code int `json:"code"`
	Data struct {
		Code        string                 `json:"code"`
		Data        map[string]interface{} `json:"data"`
		Message     string                 `json:"message"`
		RequestId   string                 `json:"requestId"`
		RequestTime string                 `json:"requestTime"`
	} `json:"data"`
	Message string `json:"message"`
}
