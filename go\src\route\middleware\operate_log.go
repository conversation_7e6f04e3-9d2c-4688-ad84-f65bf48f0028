package middleware

import (
	"bytes"
	"encoding/json"
	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/gf"
	"fincore/utils/ipUtil"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type Operate struct {
	OperateName    string                                                                `json:"name"`         // 操作名称
	Table          string                                                                `json:"table"`        // 表面：用户user_operate_log 管理员：admin_operate_log
	OperandsUID    string                                                                `json:"operands_uid"` // 操作对象UID
	GetOperandsUID func(c *gin.Context, resp map[string]interface{}, field string) int64 `json:"-"`            // 获取操作对象UID
	OnSuccess      func(c *gin.Context, resp map[string]interface{}) string              `json:"-"`            // 操作成功回调
	OnError        func(c *gin.Context, resp map[string]interface{}) string              `json:"-"`            // 操作失败回调
}

type OperateLogConfig struct {
	GET  map[string]Operate `json:"GET"`
	POST map[string]Operate `json:"POST"`
}

// 默认成功详情函数
func defaultSuccessDetail(c *gin.Context, resp map[string]interface{}) string {
	return "成功"
}

// 默认失败详情函数
func defaultErrorDetail(c *gin.Context, resp map[string]interface{}) string {
	return fmt.Sprintf("失败：%s", resp["message"].(string))
}

var config OperateLogConfig = OperateLogConfig{
	GET: map[string]Operate{
		"/api/v1/example": {
			OperateName: "查询",
			Table:       "admin_operate_log",
			OnSuccess: func(c *gin.Context, resp map[string]interface{}) string {
				fmt.Printf("获取查询参数： %v\n", c.Request.URL.Query())
				reqBody, _ := c.Get("request_body")
				fmt.Printf("获取请求体参数： %v \n", reqBody)
				fmt.Printf("获取响应数据: %v\n", resp)
				return "操作成功"
			},
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				fmt.Printf("获取查询参数： %v\n", c.Request.URL.Query())
				reqBody, _ := c.Get("request_body")
				fmt.Printf("获取请求体参数： %v \n", reqBody)
				fmt.Printf("获取响应数据: %v\n", resp)
				return "操作失败"
			},
		},
		"/business/risk/riskcontroller/getForceRefreshRisk": {
			OperateName:    "强制刷新风控",
			Table:          "admin_operate_log",
			OperandsUID:    "customer_id",
			GetOperandsUID: funcGetOperandsUID,
			OnSuccess: func(c *gin.Context, resp map[string]interface{}) string {
				return "强制刷新用户风控结果"
			},
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return "强制刷新用户风控结果失败, 失败原因：" + resp["message"].(string)
			},
		},
		"/business/order/manager/getDownloadContract": {
			OperateName: "下载合同",
			Table:       "admin_operate_log",
			GetOperandsUID: func(c *gin.Context, resp map[string]interface{}, field string) int64 {
				order_no := c.Request.URL.Query().Get("order_no")
				user_id, _ := model.DB().Table("business_loan_orders").Where("order_no", order_no).Value("user_id")
				return *convert.ConvertToInt64(user_id)
			},
			OnSuccess: func(c *gin.Context, resp map[string]interface{}) string {
				order_no := c.Request.URL.Query().Get("order_no")
				return "订单号：" + convert.ConvertToString(order_no)
			},
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				order_no := c.Request.URL.Query().Get("order_no")
				return "订单号：" + convert.ConvertToString(order_no) + "。失败原因：" + resp["message"].(string) + "," + resp["data"].(string)
			},
		},
	},
	POST: map[string]Operate{
		"/business/user/login": {
			OperateName: "登录",
			Table:       "admin_operate_log",
			OnSuccess:   defaultSuccessDetail,
			OnError:     defaultErrorDetail,
		},
		"/uniapp/user/postBySms": {
			OperateName: "短信登录",
			Table:       "user_operate_log",
			OnSuccess:   defaultSuccessDetail,
			OnError:     defaultErrorDetail,
		},
		"/business/customer/customercontroller/updateCustomerQuota": {
			OperateName:    "修改额度",
			Table:          "admin_operate_log",
			OperandsUID:    "customerId",
			GetOperandsUID: funcBodyOperandsUID,
			OnSuccess:      funcUpdateCustomerQuota,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcUpdateCustomerQuota(c, resp) + "。失败原因：" + resp["message"].(string)
			},
		},
		"/business/order/manager/processDisbursement": {
			OperateName:    "放款",
			Table:          "admin_operate_log",
			OperandsUID:    "customerId",
			GetOperandsUID: funcBodyOperandsUID,
			OnSuccess:      funcDisbursement,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcDisbursement(c, resp) + "。失败原因：" + resp["message"].(string)
			},
		},
		"/business/repayment/manager/manualWithhold": {
			OperateName: "手动代扣",
			Table:       "admin_operate_log",
			GetOperandsUID: func(c *gin.Context, resp map[string]interface{}, field string) int64 {
				req, _ := c.Get("request_body")
				bill_id := req.(map[string]interface{})["bill_id"]
				data, _ := model.DB().Table("business_repayment_bills").Where("id", bill_id).Value("user_id")
				return *convert.ConvertToInt64(data)
			},
			OnSuccess: funcManualWithhold,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcManualWithhold(c, resp) + "。失败原因：" + resp["message"].(string) + "," + resp["data"].(string)
			},
		},
		"/business/payment/manager/processPartialOfflinePayment": {
			OperateName: "部分线下支付",
			Table:       "admin_operate_log",
			GetOperandsUID: func(c *gin.Context, resp map[string]interface{}, field string) int64 {
				req, _ := c.Get("request_body")
				bill_id := req.(map[string]interface{})["bill_id"]
				data, _ := model.DB().Table("business_repayment_bills").Where("id", bill_id).Value("user_id")
				return *convert.ConvertToInt64(data)
			},
			OnSuccess: funcPartialOfflinePayment,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcPartialOfflinePayment(c, resp) + "。失败原因：" + resp["message"].(string) + "," + resp["data"].(string)
			},
		},
		"/business/order/manager/updateBillDueDate": {
			OperateName: "修改账单逾期日期",
			Table:       "admin_operate_log",
			GetOperandsUID: func(c *gin.Context, resp map[string]interface{}, field string) int64 {
				req, _ := c.Get("request_body")
				bill_id := req.(map[string]interface{})["bill_id"]
				data, _ := model.DB().Table("business_repayment_bills").Where("id", bill_id).Value("user_id")
				return *convert.ConvertToInt64(data)
			},
			OnSuccess: funcUpdateBillDueDate,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcUpdateBillDueDate(c, resp) + "。失败原因：" + resp["message"].(string) + "," + resp["data"].(string)
			},
		},
		"/business/payment/manager/processRefund": {
			OperateName: "代扣退款",
			Table:       "admin_operate_log",
			GetOperandsUID: func(c *gin.Context, resp map[string]interface{}, field string) int64 {
				req, _ := c.Get("request_body")
				transaction_id := req.(map[string]interface{})["transaction_id"]
				data, _ := model.DB().Table("business_payment_transactions").Where("id", transaction_id).Value("user_id")
				return *convert.ConvertToInt64(data)
			},
			OnSuccess: funcRefund,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcRefund(c, resp) + "。失败原因：" + resp["message"].(string) + "," + resp["data"].(string)
			},
		},
		"/business/order/manager/waiveBillAmount": {
			OperateName:    "账单结清减免",
			Table:          "admin_operate_log",
			OperandsUID:    "customerId",
			GetOperandsUID: funcBodyOperandsUID,
			OnSuccess:      funcWaiveBillAmount,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcWaiveBillAmount(c, resp) + "。失败原因：" + resp["message"].(string)
			},
		},
		"/business/order/manager/earlySettlement": {
			OperateName: "提前结清",
			Table:       "admin_operate_log",
			GetOperandsUID: func(c *gin.Context, resp map[string]interface{}, field string) int64 {
				req, _ := c.Get("request_body")
				orderNo := req.(map[string]interface{})["orderNo"]
				user_id, _ := model.DB().Table("business_loan_orders").Where("order_no", orderNo).Value("user_id")
				return *convert.ConvertToInt64(user_id)
			},
			OnSuccess: funcEarlySettlement,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcEarlySettlement(c, resp) + "。失败原因：" + resp["message"].(string)
			},
		},
		"/business/order/manager/closeOrder": {
			OperateName: "关闭订单",
			Table:       "admin_operate_log",
			GetOperandsUID: func(c *gin.Context, resp map[string]interface{}, field string) int64 {
				req, _ := c.Get("request_body")
				orderNo := req.(map[string]interface{})["orderNo"]
				user_id, _ := model.DB().Table("business_loan_orders").Where("order_no", orderNo).Value("user_id")
				return *convert.ConvertToInt64(user_id)
			},
			OnSuccess: funcCloseOrder,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcCloseOrder(c, resp) + "。失败原因：" + resp["message"].(string)
			},
		},
		"/business/customer/customercontroller/updateCustomerStatus": {
			OperateName: "一键拉黑/拉白",
			Table:       "admin_operate_log",
			GetOperandsUID: func(c *gin.Context, resp map[string]interface{}, field string) int64 {
				req, _ := c.Get("request_body")
				uid := req.(map[string]interface{})["id"]
				return *convert.ConvertToInt64(uid)
			},
			OnSuccess: funcUpdateCustomerStatus,
			OnError: func(c *gin.Context, resp map[string]interface{}) string {
				return funcUpdateCustomerStatus(c, resp) + "。失败原因：" + resp["message"].(string)
			},
		},
	},
}

// 获取query中对应的操作对象UID
func funcGetOperandsUID(c *gin.Context, resp map[string]interface{}, field string) int64 {
	id := c.Request.URL.Query().Get(field)
	return *convert.ConvertToInt64(id)
}

// 获取body中对应的操作对象UID
func funcBodyOperandsUID(c *gin.Context, resp map[string]interface{}, field string) int64 {
	req, ok := c.Get("request_body")
	if !ok {
		return 0
	}
	id := req.(map[string]interface{})[field]
	if id == nil {
		return 0
	}
	return *convert.ConvertToInt64(id)
}

// 修改额度函数
func funcUpdateCustomerQuota(c *gin.Context, resp map[string]interface{}) string {
	req, ok := c.Get("request_body")
	if !ok {
		return "修改额度失败，未获取到请求体"
	}
	oldAmount := req.(map[string]interface{})["oldAmount"]
	amount := req.(map[string]interface{})["amount"]
	productId := req.(map[string]interface{})["productId"]

	// 根据 产品id：productId 查询产品名称
	productName, _ := model.DB().Table("product_rules").Where("id", productId).Value("rule_name")
	fmt.Println("根据 产品id：productId 查询产品名称")
	fmt.Println(productName)

	return "将用户额度从" + convert.ConvertToString(oldAmount) + "修改为" + convert.ConvertToString(amount) + "，匹配产品为:" + convert.ConvertToString(productName) + "(产品id:" + convert.ConvertToString(productId) + ")"
}

// 放款函数
func funcDisbursement(c *gin.Context, resp map[string]interface{}) string {
	req, ok := c.Get("request_body")
	if !ok {
		return "放款失败，未获取到请求体"
	}
	orderNo := req.(map[string]interface{})["orderNo"]

	data, _ := model.DB().Table("business_loan_orders a").LeftJoin("business_app_account b", "a.user_id = b.id").Where("a.order_no", orderNo).Fields("a.user_id as userId, b.name as userName").First()
	fmt.Println(data)
	userId := data["userId"]
	userName := data["userName"]
	return "给" + convert.ConvertToString(userName) + "(uid:" + convert.ConvertToString(userId) + ")放款，订单号：" + convert.ConvertToString(orderNo)
}

// 代扣函数
func funcManualWithhold(c *gin.Context, resp map[string]interface{}) string {
	req, ok := c.Get("request_body")
	if !ok {
		return "记录手动代扣失败，未获取到请求体"
	}
	amount := req.(map[string]interface{})["amount"]               // 代扣金额
	bank_card_id := req.(map[string]interface{})["bank_card_id"]   // 代扣银行卡号
	bill_id := req.(map[string]interface{})["bill_id"]             // 代扣账单id
	withhold_type := req.(map[string]interface{})["withhold_type"] // 代扣类型

	// 根据账单id查询订单号
	data, _ := model.DB().Table("business_repayment_bills s").LeftJoin("business_loan_orders o", "s.order_id = o.id").
		Where("s.id", bill_id).Fields("o.order_no as orderNo, s.period_number as periodNumber").First()
	orderNo := data["orderNo"]
	periodNumber := data["periodNumber"]

	withholdTxt := ""
	if withhold_type == "asset" {
		withholdTxt = "资管代扣"
	} else if withhold_type == "guarantee" {
		withholdTxt = "担保代扣"
	} else {
		withholdTxt = "未知代扣"
	}

	// 查询银行卡号
	bank_card_no, _ := model.DB().Table("business_bank_cards").Where("id", bank_card_id).Value("bank_card_no")
	return "业务类型：" + withholdTxt + "， 订单号：" + convert.ConvertToString(orderNo) + " ， 账单期数:" + convert.ConvertToString(periodNumber) + "，代扣额度：" + convert.ConvertToString(amount) + "，代扣银行卡号：" + convert.ConvertToString(bank_card_no)
}

// 部分线下支付函数
func funcPartialOfflinePayment(c *gin.Context, resp map[string]interface{}) string {
	req, _ := c.Get("request_body")
	amount := req.(map[string]interface{})["amount"]
	bill_id := req.(map[string]interface{})["bill_id"]
	payment_channel := req.(map[string]interface{})["payment_channel"]

	// 根据账单id查询订单号
	data, _ := model.DB().Table("business_repayment_bills s").LeftJoin("business_loan_orders o", "s.order_id = o.id").
		Where("s.id", bill_id).Fields("o.order_no as orderNo, s.period_number as periodNumber").First()
	orderNo := data["orderNo"]
	periodNumber := data["periodNumber"]

	// 查询用户id
	return "订单号为：" + convert.ConvertToString(orderNo) + " 的 " + convert.ConvertToString(periodNumber) + "期账单，额度：" + convert.ConvertToString(amount) + "，支付渠道：" + convert.ConvertToString(payment_channel)
}

// 修改逾期日期
func funcUpdateBillDueDate(c *gin.Context, resp map[string]interface{}) string {
	req, _ := c.Get("request_body")
	bill_id := req.(map[string]interface{})["bill_id"]
	new_due_date := req.(map[string]interface{})["due_date"] // 2025-09-04
	old_due_date := req.(map[string]interface{})["old_due_date"]

	// 根据账单id查询订单号
	data, _ := model.DB().Table("business_repayment_bills s").LeftJoin("business_loan_orders o", "s.order_id = o.id").
		Where("s.id", bill_id).Fields("o.order_no as orderNo, s.period_number as periodNumber").First()
	orderNo := data["orderNo"]
	periodNumber := data["periodNumber"]

	return "订单号为：" + convert.ConvertToString(orderNo) + " 账单期数:" + convert.ConvertToString(periodNumber) + "，修改前日期：" + convert.ConvertToString(old_due_date) + ", 修改后日期：" + convert.ConvertToString(new_due_date)
}

// 代扣退款
func funcRefund(c *gin.Context, resp map[string]interface{}) string {
	req, _ := c.Get("request_body")
	transaction_id := req.(map[string]interface{})["transaction_id"]
	refund_amount := req.(map[string]interface{})["refund_amount"]
	refund_reason := req.(map[string]interface{})["refund_reason"]

	// 根据流水id查询
	data, _ := model.DB().Table("business_payment_transactions t").LeftJoin("business_repayment_bills s", "t.bill_id = s.id").Where("t.id", transaction_id).Fields("t.order_no as orderNo, s.period_number as periodNumber").First()
	orderNo := data["orderNo"]
	periodNumber := data["periodNumber"]

	return "订单号为：" + convert.ConvertToString(orderNo) + " 账单期数:" + convert.ConvertToString(periodNumber) + "，退款额度：" + convert.ConvertToString(refund_amount) + "，退款原因：" + convert.ConvertToString(refund_reason)
}

// 账单结清减免
func funcWaiveBillAmount(c *gin.Context, resp map[string]interface{}) string {
	req, _ := c.Get("request_body")
	bill_id := req.(map[string]interface{})["bill_id"]
	waive_amount := req.(map[string]interface{})["waive_amount"]

	// 根据账单id查询订单号
	data, _ := model.DB().Table("business_repayment_bills s").LeftJoin("business_loan_orders o", "s.order_id = o.id").
		Where("s.id", bill_id).Fields("o.order_no as orderNo, s.period_number as periodNumber").First()
	orderNo := data["orderNo"]
	periodNumber := data["periodNumber"]

	return "订单号为：" + convert.ConvertToString(orderNo) + " 账单期数:" + convert.ConvertToString(periodNumber) + "，减免金额：" + convert.ConvertToString(waive_amount)
}

// 提前结清
func funcEarlySettlement(c *gin.Context, resp map[string]interface{}) string {
	req, _ := c.Get("request_body")
	orderNo := req.(map[string]interface{})["orderNo"]

	return "订单号为：" + convert.ConvertToString(orderNo)
}

// 关闭订单
func funcCloseOrder(c *gin.Context, resp map[string]interface{}) string {
	req, _ := c.Get("request_body")
	orderNo := req.(map[string]interface{})["orderNo"]
	reason_for_closure := convert.GetIntFromMap(req.(map[string]interface{}), "reason_for_closure", -1)
	closure_remarks := req.(map[string]interface{})["closure_remarks"]
	fmt.Println("获取到的reason_for_closure")
	fmt.Println(reason_for_closure)
	reason_for_closure_txt := ""
	switch reason_for_closure {
	case 0:
		reason_for_closure_txt = "终审拒绝"
	case 1:
		reason_for_closure_txt = "法院涉案"
	case 2:
		reason_for_closure_txt = "纯白户"
	case 3:
		reason_for_closure_txt = "客户失联"
	case 4:
		reason_for_closure_txt = "不提供资料"
	case 5:
		reason_for_closure_txt = "多余订单"
	case 6:
		reason_for_closure_txt = "重新下单"
	case 7:
		reason_for_closure_txt = "客户不同意方案"
	default:
		reason_for_closure_txt = "其他原因"
	}

	return "订单号为：" + convert.ConvertToString(orderNo) + " 关闭原因：" + convert.ConvertToString(reason_for_closure_txt) + " 关闭备注：" + convert.ConvertToString(closure_remarks)
}

// 一键拉黑拉白
func funcUpdateCustomerStatus(c *gin.Context, resp map[string]interface{}) string {
	req, _ := c.Get("request_body")
	status := convert.GetIntFromMap(req.(map[string]interface{}), "status", -1)
	statusTxt := ""

	switch status {
	case 2:
		statusTxt = "拉黑"
	case 1:
		statusTxt = "拉白"
	default:
		statusTxt = "未知"
	}
	return statusTxt
}

func getOperate(method, path string) Operate {
	switch method {
	case "GET":
		return config.GET[path]
	case "POST":
		return config.POST[path]
	}
	return Operate{}
}

// 自定义ResponseWriter捕获响应
type ResponseRecorder struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *ResponseRecorder) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func OperateLogHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		operate := getOperate(c.Request.Method, c.Request.URL.Path)
		if operate.OperateName == "" {
			c.Next()
			return
		}
		// 创建响应记录器
		recorder := &ResponseRecorder{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = recorder

		// 读取并缓存请求体
		var reqBody []byte
		if c.Request.Body != nil {
			// 读取原始请求体
			reqBody, _ = io.ReadAll(c.Request.Body)
			// 重置请求体，使后续处理器可再次读取
			c.Request.Body = io.NopCloser(bytes.NewBuffer(reqBody))
		}

		// 解析请求体JSON格式
		var reqData map[string]interface{}
		if len(reqBody) > 0 && c.ContentType() == "application/json" {
			if err := json.Unmarshal(reqBody, &reqData); err == nil {
				// 存储到Context
				c.Set("request_body", reqData)
			}
		}

		c.Next() // 执行后续处理

		// 解析响应内容
		var resp map[string]interface{}
		// 不处理非正常返回值
		if err := json.Unmarshal(recorder.body.Bytes(), &resp); err != nil {
			return
		}
		ip := gf.GetIp(c)
		realIp := strings.Split(ip, ", ")[0]
		region, err := ipUtil.GetIpLocation(realIp)
		if err != nil {
			region = "未知"
		}
		// 非真实用户不记录
		user, ok := c.Get("user")
		if !ok {
			return
		}
		userInfo, ok := user.(*UserClaims)
		if !ok {
			return
		}
		opLog := map[string]interface{}{
			"ip":           ip,
			"region":       region,
			"uid":          userInfo.ID,
			"operate_name": operate.OperateName,
			"created_at":   time.Now(),
		}

		if resp["code"] == float64(0) { // JSON解析数字默认为float64
			opLog["result"] = "成功"
			opLog["detail"] = operate.OnSuccess(c, resp)
		} else {
			opLog["detail"] = operate.OnError(c, resp)
			opLog["result"] = "失败"
		}

		if operate.GetOperandsUID != nil {
			opLog["operands_uid"] = operate.GetOperandsUID(c, resp, operate.OperandsUID)
		}

		_, err = model.DB().Table(operate.Table).Data(opLog).Insert()
		if err != nil {
			fmt.Printf("插入操作日志失败:%v", err)
		}
	}
}
