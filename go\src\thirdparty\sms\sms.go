package sms

import (
	"encoding/json"
	"fincore/global"
	"fincore/utils/gf"
	"fincore/utils/log"
	"fmt"
	"time"

	"fincore/model"

	"github.com/go-resty/resty/v2"
	"github.com/google/uuid"
)

// 短信模板类型
type templateType uint

const (
	// 验证码类型
	templateTypeCode templateType = 1
	// 到期通知类型
	templateTypeNoticeAhead templateType = 2
	// 逾期通知类型
	templateTypeNoticeOverdue templateType = 3
	// 可用余额通知类型
	templateTypeNoticeAvailableQuota templateType = 4
)

type template struct {
	msg     string
	checker func(...string) error
}

// 短信平台的参数校验
func codeCheck(params ...string) error {
	if len(params) != 1 {
		return fmt.Errorf("参数数量错误")
	}
	if len(params[0]) != 6 {
		return fmt.Errorf("验证码长度不正确")
	}
	// 校验验证码格式
	codePattern := `^\d{6}$`
	if !gf.MatchRegex(params[0], codePattern) {
		return fmt.Errorf("验证码格式错误")
	}
	return nil
}

var templateMap = map[templateType]*template{
	templateTypeCode: {
		msg:     "验证码:%s（1分钟内有效），如非本人操作，请忽略本条短信",
		checker: codeCheck,
	},
	templateTypeNoticeAhead: {
		msg:     "尊敬的%s，账单%s元待还，最后还款日期%s，还款详情：https://www.hifincore.com%s",
		checker: nil,
	},
	templateTypeNoticeOverdue: {
		msg:     "尊敬的%s，您于%s月%s日完成的借款订单已经逾期，为避免您个人信誉影响，请到平台完成本期还款，还款详情：https://www.hifincore.com%s",
		checker: nil,
	},
	templateTypeNoticeAvailableQuota: {
		msg:     "尊敬的%s，您在%s平台有%s元额度待使用，立即申请>>https://www.hifincore.com%s",
		checker: nil,
	},
}

// 定义短信请求的数据结构
type smsRequest struct {
	Account  string `json:"account"`
	Password string `json:"password"`
	Data     []struct {
		Msgid    string `json:"msgid"`
		Phones   string `json:"phones"`
		Content  string `json:"content"`
		Sign     string `json:"sign"`
		Subcode  string `json:"subcode"`
		Sendtime string `json:"sendtime"`
	} `json:"data"`
}

// 短信平台返回值{"msgid":"2c92825934837c4d0154837dcba00150","result":"0","desc":"提交成功","failPhones":""}
type smsResponse struct {
	Msgid      string `json:"msgid"`
	Result     string `json:"result"`
	Desc       string `json:"desc"`
	FailPhones string `json:"failPhones"`
}

// 发送短信验证码
func SendSmsCode(mobile string, smsCode string) error {
	// 入参校验
	if len(mobile) != 11 {
		return fmt.Errorf("手机号格式不正确")
	}

	// 验证手机号格式
	mobilePattern := `^1[3-9]\d{9}$`
	if !gf.MatchRegex(mobile, mobilePattern) {
		return fmt.Errorf("手机号格式不正确")
	}

	// 获取模板
	template := templateMap[templateTypeCode]
	if err := template.checker(smsCode); err != nil {
		return err
	}

	msgid := uuid.New().String()

	// 账号限流不同，使用验证码账号
	reqBody := &smsRequest{
		Account:  global.App.Config.Sms.CodeAccount,
		Password: global.App.Config.Sms.CodePassword,
		Data: []struct {
			Msgid    string `json:"msgid"`
			Phones   string `json:"phones"`
			Content  string `json:"content"`
			Sign     string `json:"sign"`
			Subcode  string `json:"subcode"`
			Sendtime string `json:"sendtime"`
		}{
			{
				Msgid:   msgid,
				Phones:  mobile,
				Content: fmt.Sprintf(template.msg, smsCode),
				Sign:    global.App.Config.Sms.Sign,
			},
		},
	}

	// 发送短信记录
	record := model.SmsRecord{
		Title:     "验证码短信",
		Content:   fmt.Sprintf(template.msg, smsCode),
		Mobile:    mobile,
		CreatedAt: time.Now(),
	}
	SmsSendRecord(&record)

	return callSmsApi(reqBody)
}

func callSmsApi(reqBody *smsRequest) error {
	client := resty.New()
	client.SetTimeout(5 * time.Second) // 全局超时

	// 设置默认 Header
	client.SetHeaders(map[string]string{
		"Content-Type": "application/json",
	})

	// 配置重试策略（失败自动重试）
	client.SetRetryCount(3).
		SetRetryWaitTime(1 * time.Second).
		SetRetryMaxWaitTime(3 * time.Second)

	resp, err := client.R().
		SetBody(reqBody).
		Post(global.App.Config.Sms.Apiurl)

	if err != nil {
		return fmt.Errorf("请求失败: %v", err)
	}

	// 状态码判断
	if !resp.IsSuccess() {
		return fmt.Errorf("请求失败，状态码: %d", resp.StatusCode())
	}
	var result smsResponse
	err = json.Unmarshal(resp.Body(), &result)
	if err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	if result.Result != "0" {
		return fmt.Errorf("短信发送失败: %s", result.Desc)
	}
	return nil
}

type NoticeAheadParams struct {
	Username string
	Amount   string
	DueDate  string
	Mobile   string
	URL      string
}

// 批量发送账单即将到期短信
func SendBillNoticeAheadSms(notices []NoticeAheadParams) error {
	if notices == nil {
		return nil
	}
	l := len(notices)
	if l == 0 {
		return nil
	}
	reqBody := &smsRequest{
		Account:  global.App.Config.Sms.BusinessAccount,
		Password: global.App.Config.Sms.BusinessPassword,
		Data: make([]struct {
			Msgid    string `json:"msgid"`
			Phones   string `json:"phones"`
			Content  string `json:"content"`
			Sign     string `json:"sign"`
			Subcode  string `json:"subcode"`
			Sendtime string `json:"sendtime"`
		}, len(notices)),
	}
	for i := range l {
		notice := notices[i]
		reqBody.Data[i].Msgid = uuid.New().String()
		reqBody.Data[i].Phones = notice.Mobile
		reqBody.Data[i].Content = fmt.Sprintf(templateMap[templateTypeNoticeAhead].msg, notice.Username, notice.Amount, notice.DueDate, notice.URL)

		// 记录短信发送记录
		record := model.SmsRecord{
			Title:     "账单即将到期短信",
			Content:   fmt.Sprintf(templateMap[templateTypeNoticeAhead].msg, notice.Username, notice.Amount, notice.DueDate, notice.URL),
			Mobile:    notice.Mobile,
			CreatedAt: time.Now(),
			Recipient: notice.Username,
		}
		SmsSendRecord(&record)
	}
	return callSmsApi(reqBody)
}

type NoticeOverdueParams struct {
	Username string
	DueMonth string
	DueDay   string
	Mobile   string
	URL      string
}

// 批量发送账单已逾期短信
func SendBillNoticeOverdueSms(notices []NoticeOverdueParams) error {
	if notices == nil {
		return nil
	}
	l := len(notices)
	if l == 0 {
		return nil
	}
	reqBody := &smsRequest{
		Account:  global.App.Config.Sms.BusinessAccount,
		Password: global.App.Config.Sms.BusinessPassword,
		Data: make([]struct {
			Msgid    string `json:"msgid"`
			Phones   string `json:"phones"`
			Content  string `json:"content"`
			Sign     string `json:"sign"`
			Subcode  string `json:"subcode"`
			Sendtime string `json:"sendtime"`
		}, len(notices)),
	}
	for i := range l {
		notice := notices[i]
		reqBody.Data[i].Msgid = uuid.New().String()
		reqBody.Data[i].Phones = notice.Mobile
		reqBody.Data[i].Content = fmt.Sprintf(templateMap[templateTypeNoticeOverdue].msg, notice.Username, notice.DueMonth, notice.DueDay, notice.URL)

		// 记录短信发送记录
		record := model.SmsRecord{
			Title:     "账单已逾期短信",
			Content:   fmt.Sprintf(templateMap[templateTypeNoticeOverdue].msg, notice.Username, notice.DueMonth, notice.DueDay, notice.URL),
			Mobile:    notice.Mobile,
			CreatedAt: time.Now(),
			Recipient: notice.Username,
		}
		SmsSendRecord(&record)
	}

	return callSmsApi(reqBody)
}

type NoticeAvailableQuotaParams struct {
	Username string
	Platform string
	Amount   string
	Mobile   string
	URL      string
}

// 批量发送可用额度短信
func SendAvailableQuotaNoticeSms(notices []NoticeAvailableQuotaParams) error {
	if notices == nil {
		return nil
	}
	l := len(notices)
	if l == 0 {
		return nil
	}
	reqBody := &smsRequest{
		Account:  global.App.Config.Sms.BusinessAccount,
		Password: global.App.Config.Sms.BusinessPassword,
		Data: make([]struct {
			Msgid    string `json:"msgid"`
			Phones   string `json:"phones"`
			Content  string `json:"content"`
			Sign     string `json:"sign"`
			Subcode  string `json:"subcode"`
			Sendtime string `json:"sendtime"`
		}, len(notices)),
	}
	for i := range l {
		notice := notices[i]
		reqBody.Data[i].Msgid = uuid.New().String()
		reqBody.Data[i].Phones = notice.Mobile
		reqBody.Data[i].Content = fmt.Sprintf(templateMap[templateTypeNoticeAvailableQuota].msg, notice.Username, notice.Platform, notice.Amount, notice.URL)

		// 记录短信发送记录
		record := model.SmsRecord{
			Title:     "可用额度短信",
			Content:   fmt.Sprintf(templateMap[templateTypeNoticeAvailableQuota].msg, notice.Username, notice.Platform, notice.Amount, notice.URL),
			Mobile:    notice.Mobile,
			CreatedAt: time.Now(),
			Recipient: notice.Username,
		}
		SmsSendRecord(&record)
	}
	return callSmsApi(reqBody)
}

// 记录短信发送日志记录到sms_record表
func SmsSendRecord(sms *model.SmsRecord) {
	// 记录短信发送日志到sms_record表
	record := map[string]interface{}{
		"title":      sms.Title,
		"content":    sms.Content,
		"mobile":     sms.Mobile,
		"sender":     sms.Sender,
		"recipient":  sms.Recipient,
		"created_at": sms.CreatedAt,
	}
	_, err := model.DB().Table("sms_record").Insert(record)
	if err != nil {
		log.Error("记录短信发送日志失败:", err)
		return
	}
	return
}
