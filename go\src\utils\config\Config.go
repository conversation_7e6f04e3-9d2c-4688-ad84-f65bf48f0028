package config

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"strings"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

type Config struct {
	DBconf         DBconf         `yaml:"dbconf"`
	App            App            `yaml:"app"`
	Jwt            Jwt            `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	Log            Log            `mapstructure:"log" json:"log" yaml:"log"`
	Sms            Sms            `mapstructure:"sms" json:"sms" yaml:"sms"`
	Vbank          Vbank          `mapstructure:"vbank" json:"vbank" yaml:"vbank"`
	Aiqian_envs    Aiqian_envs    `mapstructure:"aiqian_envs" json:"aiqian_envs" yaml:"aiqian_envs"`
	AiqianSign     AiqianSign     `mapstructure:"aiqian_sign" json:"aiqian_sign" yaml:"aiqian_sign"`             // 爱签签署信息配置
	SumPay         SumPay         `mapstructure:"sumpay" json:"sumpay" yaml:"sumpay"`                            // 商盟 统统付
	RiskThirdParty RiskThirdParty `mapstructure:"risk_thirdparty" json:"risk_thirdparty" yaml:"risk_thirdparty"` // 第三方风控配置
	RiskModel      RiskModel      `mapstructure:"risk_model" json:"risk_model" yaml:"risk_model"`                // 风控模型服务配置
	ThirdRisk      ThirdRisk      `mapstructure:"third_risk" json:"third_risk" yaml:"third_risk"`                // 第三方风控服务配置
	RiskEvaluation RiskEvaluation `mapstructure:"risk_evaluation" json:"risk_evaluation" yaml:"risk_evaluation"` // 风控评估配置
	SystemDeduct   SystemDeduct   `mapstructure:"system_deduct" json:"system_deduct" yaml:"system_deduct"`       // 系统代扣配置
	Redis          Redis          `mapstructure:"redis" json:"redis" yaml:"redis"`                               // redis 配置
	Lock           Lock           `mapstructure:"lock" json:"lock" yaml:"lock"`                                  // 锁配置
	OSS            OSS            `mapstructure:"oss" json:"oss" yaml:"oss"`                                     // 阿里云OSS配置
}

// 读取Yaml配置文件，并转换成Config对象  struct结构
func InitializeConfig(appName string) *Config {
	// 检查是否使用OSS配置
	if useOSSConfig := os.Getenv("USE_OSS_CONFIG"); useOSSConfig == "true" {

		// 使用安全的OSS加载器（支持证书文件下载和fincore.yml加密）
		ossLoader, err := NewSecureOSSLoader()
		if err != nil {
			fmt.Println("创建OSS配置加载器失败，回退到本地配置: " + err.Error())
		} else {
			// 验证OSS连接
			if err := ossLoader.ValidateConnection(); err != nil {
				fmt.Println("OSS连接验证失败，回退到本地配置: " + err.Error())
			} else {
				configPath := os.Getenv("CONFIG_PATH")
				if configPath == "" {
					configPath = "config/prod/config.yml"
				}

				// 加载配置并解密所有敏感字段
				ossConfig, err := ossLoader.LoadConfig(configPath)
				if err != nil {
					fmt.Println("从OSS加载配置失败，回退到本地配置: " + err.Error())
				} else {
					fmt.Println("成功加载oss配置")

					// 注册清理函数，在程序退出时清理临时文件
					go func() {
						defer ossLoader.CleanupTempFiles()
					}()

					return ossConfig
				}
			}
		}
	}
	return loadLocalConfig(appName)
}

// loadLocalConfig 加载本地配置文件
func loadLocalConfig(appName string) *Config {
	config := &Config{}
	v := viper.New()

	if appName == "query_app" {
		v.SetConfigName("query_app_config")
	} else {
		// 根据环境变量选择配置文件
		env := os.Getenv("ENV")
		if env == "dev" {
			v.SetConfigName("config_dev")
		} else {
			v.SetConfigName("config")
		}
	}

	v.SetConfigType("yml")

	//获取项目的执行路径
	path, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	resourcePath := filepath.Join(path, "resource")
	v.AddConfigPath(resourcePath)
	v.AddConfigPath("./resource/")
	v.AddConfigPath("../resource/")
	v.AddConfigPath("../../resource/")
	v.AddConfigPath("../../../../../resource/")

	if err := v.ReadInConfig(); err != nil {
		fmt.Println("读取本地配置文件失败: " + err.Error())
	}

	if err := v.Unmarshal(config); err != nil {
		fmt.Println("解析本地配置文件失败: " + err.Error())
	}

	// 移除本地配置中ENC:前缀（本地配置为明文存储）
	RemoveEncPrefixFromConfig(config)
	fmt.Println("已移除本地配置中的ENC:前缀")

	// 监听配置文件变化
	v.WatchConfig()
	v.OnConfigChange(func(e fsnotify.Event) {
		fmt.Println("配置文件发生变化: " + e.Name)
		if err := v.Unmarshal(config); err != nil {
			fmt.Println("重新加载配置失败: " + err.Error())
		} else {
			// 重新移除ENC:前缀
			RemoveEncPrefixFromConfig(config)
		}
	})

	fmt.Println("成功加载本地配置文件: " + v.ConfigFileUsed())
	return config
}
func listenSignal() {
	go func() {
		// 执行重启命令
		cmd := exec.Command("fincore", "run", "daemon", "restart")
		stdout, err := cmd.StdoutPipe()
		if err != nil {
			fmt.Println(err)
		}
		defer stdout.Close()

		if err := cmd.Start(); err != nil {
			panic(err)
		}
		reader := bufio.NewReader(stdout)
		//实时循环读取输出流中的一行内容
		for {
			line, err2 := reader.ReadString('\n')
			if err2 != nil || io.EOF == err2 {
				break
			}
			fmt.Print(line)
		}

		if err := cmd.Wait(); err != nil {
			fmt.Println(err)
		}
		opBytes, _ := io.ReadAll(stdout)
		fmt.Print(string(opBytes))

	}()
}

// RemoveEncPrefixFromConfig 递归移除配置中所有字符串字段的ENC:前缀
// 用于本地配置加载时，自动移除ENC:前缀，剩下的就是明文配置
func RemoveEncPrefixFromConfig(config *Config) {
	removeEncPrefixFromStruct(reflect.ValueOf(config).Elem())
}

// removeEncPrefixFromStruct 递归处理结构体字段
func removeEncPrefixFromStruct(v reflect.Value) {
	if !v.IsValid() || !v.CanSet() {
		return
	}

	switch v.Kind() {
	case reflect.String:
		// 处理字符串字段，移除ENC:前缀
		str := v.String()
		if strings.HasPrefix(str, "ENC:") {
			v.SetString(strings.TrimPrefix(str, "ENC:"))
		}

	case reflect.Struct:
		// 递归处理结构体字段
		for i := 0; i < v.NumField(); i++ {
			field := v.Field(i)
			if field.CanSet() {
				removeEncPrefixFromStruct(field)
			}
		}

	case reflect.Ptr:
		// 处理指针字段
		if !v.IsNil() {
			removeEncPrefixFromStruct(v.Elem())
		}

	case reflect.Slice, reflect.Array:
		// 处理切片和数组
		for i := 0; i < v.Len(); i++ {
			removeEncPrefixFromStruct(v.Index(i))
		}

	case reflect.Map:
		// 处理映射
		for _, key := range v.MapKeys() {
			mapValue := v.MapIndex(key)
			if mapValue.Kind() == reflect.Interface {
				mapValue = mapValue.Elem()
			}
			if mapValue.IsValid() && mapValue.CanSet() {
				removeEncPrefixFromStruct(mapValue)
			}
		}
	}
}
