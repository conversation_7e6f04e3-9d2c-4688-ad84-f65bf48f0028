package lock

import (
	"sync"
	"time"

	"fincore/global"
	"fincore/utils/log"
)

// LockType 锁类型
type LockType string

const (
	LockTypeMemory LockType = "memory" // 内存锁
	LockTypeRedis  LockType = "redis"  // Redis分布式锁
)

// LockConfig 锁配置
type LockConfig struct {
	Type  LockType    `mapstructure:"type" json:"type" yaml:"type"`    // 锁类型
	Redis RedisConfig `mapstructure:"redis" json:"redis" yaml:"redis"` // Redis锁配置
}

// RedisConfig Redis锁配置
type RedisConfig struct {
	DefaultExpiration int  `mapstructure:"default_expiration" json:"default_expiration" yaml:"default_expiration"` // 默认过期时间（秒）
	RenewalInterval   int  `mapstructure:"renewal_interval" json:"renewal_interval" yaml:"renewal_interval"`       // 续期间隔（秒）
	MaxRetryTimes     int  `mapstructure:"max_retry_times" json:"max_retry_times" yaml:"max_retry_times"`          // 最大重试次数
	RetryIntervalMs   int  `mapstructure:"retry_interval_ms" json:"retry_interval_ms" yaml:"retry_interval_ms"`    // 重试间隔（毫秒）
	EnableWatchdog    bool `mapstructure:"enable_watchdog" json:"enable_watchdog" yaml:"enable_watchdog"`          // 启用看门狗
}

// LockFactory 锁工厂
type LockFactory struct {
	config        *LockConfig
	memoryManager LockManager
	redisManager  LockManager
	mu            sync.RWMutex
}

var (
	defaultFactory *LockFactory
	factoryOnce    sync.Once
)

// GetDefaultFactory 获取默认锁工厂实例
func GetDefaultFactory() *LockFactory {
	factoryOnce.Do(func() {
		defaultFactory = NewLockFactory()
	})
	return defaultFactory
}

// NewLockFactory 创建新的锁工厂
func NewLockFactory() *LockFactory {
	factory := &LockFactory{
		config:        getDefaultLockConfig(),
		memoryManager: NewSimpleLockManager(),
	}

	// 根据配置初始化Redis管理器
	if factory.config.Type == LockTypeRedis {
		factory.initRedisManager()
	}

	return factory
}

// getDefaultLockConfig 获取默认锁配置
func getDefaultLockConfig() *LockConfig {
	// 尝试从全局配置中读取
	if global.App != nil && global.App.Config != nil {
		lockConfig := global.App.Config.Lock

		// 转换配置类型
		lockType := LockTypeMemory
		if lockConfig.Type == "redis" {
			lockType = LockTypeRedis
		}

		// 使用配置文件中的值，如果为0则使用默认值
		defaultExpiration := lockConfig.Redis.DefaultExpiration
		if defaultExpiration == 0 {
			defaultExpiration = 30
		}

		renewalInterval := lockConfig.Redis.RenewalInterval
		if renewalInterval == 0 {
			renewalInterval = 10
		}

		maxRetryTimes := lockConfig.Redis.MaxRetryTimes
		if maxRetryTimes == 0 {
			maxRetryTimes = 3
		}

		retryIntervalMs := lockConfig.Redis.RetryIntervalMs
		if retryIntervalMs == 0 {
			retryIntervalMs = 100
		}

		return &LockConfig{
			Type: lockType,
			Redis: RedisConfig{
				DefaultExpiration: defaultExpiration,
				RenewalInterval:   renewalInterval,
				MaxRetryTimes:     maxRetryTimes,
				RetryIntervalMs:   retryIntervalMs,
				EnableWatchdog:    lockConfig.Redis.EnableWatchdog,
			},
		}
	}

	// 返回默认配置
	return &LockConfig{
		Type: LockTypeMemory, // 默认使用内存锁
		Redis: RedisConfig{
			DefaultExpiration: 30,   // 30秒
			RenewalInterval:   10,   // 10秒
			MaxRetryTimes:     3,    // 3次重试
			RetryIntervalMs:   100,  // 100毫秒
			EnableWatchdog:    true, // 启用看门狗
		},
	}
}

// initRedisManager 初始化Redis管理器
func (lf *LockFactory) initRedisManager() {
	// 检查Redis连接是否可用
	if lf.isRedisHealthy() {
		lf.redisManager = NewRedisLockManager()

		// 预加载Lua脚本
		if count, err := LoadScripts(); err != nil {
			// 脚本加载失败，记录日志但不影响使用
			if logger := log.GetDefaultLogger(); logger != nil {
				logger.WithFields(
					log.String("error", err.Error()),
				).Warn("Redis锁脚本预加载失败")
			}
		} else {
			if logger := log.GetDefaultLogger(); logger != nil {
				logger.WithFields(
					log.Int("count", count),
				).Info("Redis锁脚本预加载成功")
			}
		}
	} else {
		// Redis不可用，记录警告
		if logger := log.GetDefaultLogger(); logger != nil {
			logger.Warn("Redis连接不可用，将使用内存锁作为降级方案")
		}
	}
}

// isRedisHealthy 检查Redis连接是否健康
func (lf *LockFactory) isRedisHealthy() bool {
	defer func() {
		if r := recover(); r != nil {
			// Redis连接出现panic，认为不健康
		}
	}()

	// 尝试创建一个临时的Redis管理器来测试连接
	tempManager := NewRedisLockManager()
	if redisManager, ok := tempManager.(*RedisLockManager); ok {
		return redisManager.IsHealthy()
	}

	return false
}

// GetManager 获取锁管理器
func (lf *LockFactory) GetManager() LockManager {
	lf.mu.RLock()
	defer lf.mu.RUnlock()

	switch lf.config.Type {
	case LockTypeRedis:
		if lf.redisManager != nil {
			// 检查Redis是否仍然健康
			if redisManager, ok := lf.redisManager.(*RedisLockManager); ok {
				if redisManager.IsHealthy() {
					return lf.redisManager
				}
				// Redis不健康，降级到内存锁
				if logger := log.GetDefaultLogger(); logger != nil {
					logger.Warn("Redis连接异常，降级使用内存锁")
				}
			}
		}
		// Redis不可用，降级到内存锁
		return lf.memoryManager
	default:
		return lf.memoryManager
	}
}

// GetLock 获取锁
func (lf *LockFactory) GetLock(key string, expiration ...time.Duration) Lock {
	manager := lf.GetManager()
	lock := manager.GetLock(key, expiration...)

	// 如果配置为Redis锁但实际使用了内存锁，添加警告
	if lf.config.Type == LockTypeRedis && lf.GetCurrentLockType() == LockTypeMemory {
		if simpleLock, ok := lock.(*simpleLock); ok {
			simpleLock.warnings = append(simpleLock.warnings, "Redis不可用，已降级到内存锁")
		}
	}

	return lock
}

// GetLockWithLogger 获取带日志的锁
func (lf *LockFactory) GetLockWithLogger(key string, logger *log.Logger, expiration ...time.Duration) Lock {
	manager := lf.GetManager()
	lock := manager.GetLockWithLogger(key, logger, expiration...)

	// 如果配置为Redis锁但实际使用了内存锁，添加警告
	if lf.config.Type == LockTypeRedis && lf.GetCurrentLockType() == LockTypeMemory {
		if simpleLock, ok := lock.(*simpleLock); ok {
			simpleLock.warnings = append(simpleLock.warnings, "Redis不可用，已降级到内存锁")
		}
	}

	return lock
}

// UnlockByKey 通过key解锁
func (lf *LockFactory) UnlockByKey(key string) error {
	manager := lf.GetManager()
	return manager.UnlockByKey(key)
}

// CleanExpiredLocks 清理过期锁
func (lf *LockFactory) CleanExpiredLocks() int {
	manager := lf.GetManager()
	return manager.CleanExpiredLocks()
}

// GetStats 获取锁统计信息
func (lf *LockFactory) GetStats() map[string]LockStats {
	manager := lf.GetManager()
	return manager.GetStats()
}

// GetConfig 获取当前配置
func (lf *LockFactory) GetConfig() *LockConfig {
	lf.mu.RLock()
	defer lf.mu.RUnlock()

	// 返回配置的副本
	configCopy := *lf.config
	return &configCopy
}

// UpdateConfig 更新配置
func (lf *LockFactory) UpdateConfig(config *LockConfig) error {
	lf.mu.Lock()
	defer lf.mu.Unlock()

	oldType := lf.config.Type
	lf.config = config

	// 如果锁类型发生变化，重新初始化管理器
	if oldType != config.Type {
		if config.Type == LockTypeRedis {
			lf.initRedisManager()
		}

		if logger := log.GetDefaultLogger(); logger != nil {
			logger.WithFields(
				log.String("old_type", string(oldType)),
				log.String("new_type", string(config.Type)),
			).Info("锁类型配置已更新")
		}
	}

	return nil
}

// GetCurrentLockType 获取当前实际使用的锁类型
func (lf *LockFactory) GetCurrentLockType() LockType {
	manager := lf.GetManager()

	switch manager.(type) {
	case *RedisLockManager:
		return LockTypeRedis
	case *SimpleLockManager:
		return LockTypeMemory
	default:
		return LockTypeMemory
	}
}

// IsUsingRedis 是否正在使用Redis锁
func (lf *LockFactory) IsUsingRedis() bool {
	return lf.GetCurrentLockType() == LockTypeRedis
}

// GetHealthStatus 获取健康状态
func (lf *LockFactory) GetHealthStatus() map[string]interface{} {
	status := make(map[string]interface{})

	status["configured_type"] = string(lf.config.Type)
	status["actual_type"] = string(lf.GetCurrentLockType())
	status["is_healthy"] = true

	// 检查Redis状态
	if lf.config.Type == LockTypeRedis {
		if lf.redisManager != nil {
			if redisManager, ok := lf.redisManager.(*RedisLockManager); ok {
				redisHealthy := redisManager.IsHealthy()
				status["redis_healthy"] = redisHealthy
				if !redisHealthy {
					status["is_healthy"] = false
					status["fallback_reason"] = "Redis连接异常"
				}
			}
		} else {
			status["redis_healthy"] = false
			status["is_healthy"] = false
			status["fallback_reason"] = "Redis管理器未初始化"
		}
	}

	return status
}
