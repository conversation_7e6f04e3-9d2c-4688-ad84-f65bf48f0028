package lock

import (
	"testing"
	"time"
)

// TestMemoryLockStatus 测试内存锁状态功能
func TestMemoryLockStatus(t *testing.T) {
	manager := NewSimpleLockManager()

	// 测试成功获取锁的状态
	lock := manager.GetLock("test:memory:status", 30*time.Second)

	// 检查初始状态
	if lock.IsActuallyLocked() {
		t.<PERSON><PERSON>("锁初始状态应该是未锁定")
	}

	if lock.GetLastError() != nil {
		t.Error("初始状态不应该有错误")
	}

	// 加锁
	lock.Lock()

	// 检查加锁后状态
	if !lock.IsActuallyLocked() {
		t.<PERSON>rror("加锁后应该是锁定状态")
	}

	status := lock.GetLockStatus()
	if !status.IsLocked {
		t.Error("状态中应该显示已锁定")
	}

	if status.LockType != LockTypeMemory {
		t.Errorf("锁类型应该是内存锁，实际: %s", status.LockType)
	}

	if status.Error != nil {
		t.Errorf("成功加锁不应该有错误: %v", status.Error)
	}

	if status.AcquiredAt.IsZero() {
		t.<PERSON>rror("应该记录获取时间")
	}

	// 解锁
	lock.Unlock()

	// 检查解锁后状态
	if lock.IsActuallyLocked() {
		t.Error("解锁后应该是未锁定状态")
	}
}

// TestMemoryLockTryLockStatus 测试内存锁TryLock状态
func TestMemoryLockTryLockStatus(t *testing.T) {
	manager := NewSimpleLockManager()

	// 第一个锁成功
	lock1 := manager.GetLock("test:memory:trylock:status", 30*time.Second)
	success1, _ := lock1.TryLock()

	if !success1 {
		t.Error("第一次TryLock应该成功")
	}

	if !lock1.IsActuallyLocked() {
		t.Error("TryLock成功后应该是锁定状态")
	}

	status1 := lock1.GetLockStatus()
	if status1.Error != nil {
		t.Errorf("成功TryLock不应该有错误: %v", status1.Error)
	}

	// 第二个锁失败
	lock2 := manager.GetLock("test:memory:trylock:status", 30*time.Second)
	success2, _ := lock2.TryLock()

	if success2 {
		t.Error("第二次TryLock应该失败")
	}

	if lock2.IsActuallyLocked() {
		t.Error("TryLock失败后应该是未锁定状态")
	}

	// 清理
	lock1.Unlock()
}

// TestRedisLockStatus 测试Redis锁状态功能
func TestRedisLockStatus(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁状态测试")
	}

	manager := NewRedisLockManager()

	// 测试成功获取锁的状态
	lock := manager.GetLock("test:redis:status", 30*time.Second)

	// 检查初始状态
	if lock.IsActuallyLocked() {
		t.Error("锁初始状态应该是未锁定")
	}

	// 加锁
	lock.Lock()

	// 检查加锁后状态
	if !lock.IsActuallyLocked() {
		t.Error("加锁后应该是锁定状态")
	}

	status := lock.GetLockStatus()
	if !status.IsLocked {
		t.Error("状态中应该显示已锁定")
	}

	if status.LockType != LockTypeRedis {
		t.Errorf("锁类型应该是Redis锁，实际: %s", status.LockType)
	}

	if status.Error != nil {
		t.Errorf("成功加锁不应该有错误: %v", status.Error)
	}

	if status.AcquiredAt.IsZero() {
		t.Error("应该记录获取时间")
	}

	// 解锁
	lock.Unlock()

	// 检查解锁后状态
	if lock.IsActuallyLocked() {
		t.Error("解锁后应该是未锁定状态")
	}
}

// TestRedisLockTryLockStatus 测试Redis锁TryLock状态
func TestRedisLockTryLockStatus(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis锁状态测试")
	}

	manager := NewRedisLockManager()

	// 清理可能存在的锁
	testKey := "test:redis:trylock:status"
	manager.UnlockByKey(testKey)
	time.Sleep(10 * time.Millisecond)

	// 第一个锁成功
	lock1 := manager.GetLock(testKey, 30*time.Second)
	success1, _ := lock1.TryLock()

	if !success1 {
		t.Error("第一次TryLock应该成功")
	}

	if !lock1.IsActuallyLocked() {
		t.Error("TryLock成功后应该是锁定状态")
	}

	status1 := lock1.GetLockStatus()
	if status1.Error != nil {
		t.Errorf("成功TryLock不应该有错误: %v", status1.Error)
	}

	// 第二个锁失败
	lock2 := manager.GetLock(testKey, 30*time.Second)
	success2, _ := lock2.TryLock()

	if success2 {
		t.Error("第二次TryLock应该失败")
	}

	if lock2.IsActuallyLocked() {
		t.Error("TryLock失败后应该是未锁定状态")
	}

	status2 := lock2.GetLockStatus()
	if status2.Error == nil {
		t.Error("TryLock失败应该有错误信息")
	}

	// 清理
	lock1.Unlock()
}

// TestLockFactoryFallbackWarning 测试工厂降级警告
func TestLockFactoryFallbackWarning(t *testing.T) {
	// 创建配置为Redis但Redis不可用的工厂
	config := &LockConfig{
		Type: LockTypeRedis,
		Redis: RedisConfig{
			DefaultExpiration: 30,
			RenewalInterval:   10,
			MaxRetryTimes:     3,
			RetryIntervalMs:   100,
			EnableWatchdog:    true,
		},
	}

	factory := NewLockFactory()
	factory.UpdateConfig(config)

	// 如果Redis不可用，应该降级到内存锁并有警告
	if !factory.IsUsingRedis() {
		lock := factory.GetLock("test:fallback:warning", 30*time.Second)
		lock.Lock()

		status := lock.GetLockStatus()
		if len(status.Warnings) == 0 {
			t.Error("降级到内存锁应该有警告信息")
		}

		if status.LockType != LockTypeMemory {
			t.Errorf("应该降级到内存锁，实际: %s", status.LockType)
		}

		t.Logf("降级警告: %v", status.Warnings)
		lock.Unlock()
	} else {
		t.Log("Redis可用，跳过降级测试")
	}
}

// TestGlobalLockStatus 测试全局锁函数的状态功能
func TestGlobalLockStatus(t *testing.T) {
	// 测试全局函数
	lock := GetLock("test:global:status", 30*time.Second)
	lock.Lock()
	defer lock.Unlock()

	// 检查状态
	if !lock.IsActuallyLocked() {
		t.Error("全局锁应该是锁定状态")
	}

	status := lock.GetLockStatus()
	if !status.IsLocked {
		t.Error("全局锁状态应该显示已锁定")
	}

	if status.LockType != LockTypeMemory && status.LockType != LockTypeRedis {
		t.Errorf("未知的锁类型: %s", status.LockType)
	}

	t.Logf("全局锁状态: 类型=%s, 锁定=%v, 警告=%v",
		status.LockType, status.IsLocked, status.Warnings)
}
