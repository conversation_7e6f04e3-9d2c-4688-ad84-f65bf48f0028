package lock

import (
	"context"
	"fmt"

	"fincore/utils/utilstool/goredis"
)

// Redis Lua脚本定义
// 使用Lua脚本确保Redis操作的原子性

var ctx = context.Background()

// LuaUnlockScript 安全解锁脚本
// 只有当锁的值匹配时才删除锁，防止误删其他进程的锁
const LuaUnlockScript = `
-- KEYS[1]: 锁的key
-- ARGV[1]: 锁的value（用于验证所有权）
if redis.call("GET", KEYS[1]) == ARGV[1] then
    return redis.call("DEL", KEYS[1])
else
    return 0
end
`

// LuaRenewScript 锁续期脚本
// 只有当锁的值匹配时才续期，确保只有锁的持有者才能续期
const LuaRenewScript = `
-- KEYS[1]: 锁的key
-- ARGV[1]: 锁的value（用于验证所有权）
-- ARGV[2]: 新的过期时间（秒）
if redis.call("GET", KEYS[1]) == ARGV[1] then
    return redis.call("EXPIRE", KEYS[1], ARGV[2])
else
    return 0
end
`

// LuaAcquireScript 获取锁脚本（带重试逻辑）
// 尝试获取锁，如果失败则返回锁的剩余TTL
const LuaAcquireScript = `
-- KEYS[1]: 锁的key
-- ARGV[1]: 锁的value
-- ARGV[2]: 过期时间（秒）
local result = redis.call("SET", KEYS[1], ARGV[1], "NX", "EX", ARGV[2])
if result then
    return {1, 0}  -- 成功获取锁，TTL为0
else
    local ttl = redis.call("TTL", KEYS[1])
    if ttl == -1 then
        -- 锁存在但没有过期时间，设置一个默认过期时间
        redis.call("EXPIRE", KEYS[1], ARGV[2])
        ttl = tonumber(ARGV[2])
    end
    return {0, ttl}  -- 获取锁失败，返回剩余TTL
end
`

// LuaExtendScript 延长锁的过期时间脚本
// 只有当锁的值匹配时才延长过期时间，并返回新的TTL
const LuaExtendScript = `
-- KEYS[1]: 锁的key
-- ARGV[1]: 锁的value（用于验证所有权）
-- ARGV[2]: 延长的时间（秒）
if redis.call("GET", KEYS[1]) == ARGV[1] then
    local ttl = redis.call("TTL", KEYS[1])
    if ttl > 0 then
        local new_ttl = ttl + tonumber(ARGV[2])
        redis.call("EXPIRE", KEYS[1], new_ttl)
        return new_ttl
    else
        return 0  -- 锁已过期或不存在
    end
else
    return -1  -- 锁不属于当前进程
end
`

// LuaCheckScript 检查锁状态脚本
// 检查锁是否存在且属于当前进程
const LuaCheckScript = `
-- KEYS[1]: 锁的key
-- ARGV[1]: 锁的value（用于验证所有权）
local current_value = redis.call("GET", KEYS[1])
if current_value == ARGV[1] then
    local ttl = redis.call("TTL", KEYS[1])
    return {1, ttl}  -- 锁属于当前进程，返回TTL
elseif current_value then
    local ttl = redis.call("TTL", KEYS[1])
    return {0, ttl}  -- 锁被其他进程持有，返回TTL
else
    return {-1, 0}  -- 锁不存在
end
`

// LuaForceUnlockScript 强制解锁脚本（管理员功能）
// 不验证所有权，直接删除锁（谨慎使用）
const LuaForceUnlockScript = `
-- KEYS[1]: 锁的key
local result = redis.call("DEL", KEYS[1])
return result
`

// LuaBatchUnlockScript 批量解锁脚本
// 批量删除指定前缀的锁
const LuaBatchUnlockScript = `
-- KEYS[1]: 锁的前缀模式（如 "lock:*"）
local keys = redis.call("KEYS", KEYS[1])
local count = 0
for i = 1, #keys do
    local result = redis.call("DEL", keys[i])
    count = count + result
end
return count
`

// LuaGetLockInfoScript 获取锁信息脚本
// 获取锁的详细信息：值、TTL、是否存在
const LuaGetLockInfoScript = `
-- KEYS[1]: 锁的key
local value = redis.call("GET", KEYS[1])
if value then
    local ttl = redis.call("TTL", KEYS[1])
    return {value, ttl}
else
    return {nil, -2}  -- 锁不存在
end
`

// LuaCompareAndSwapScript 比较并交换锁值脚本
// 原子性地比较锁的当前值，如果匹配则更新为新值
const LuaCompareAndSwapScript = `
-- KEYS[1]: 锁的key
-- ARGV[1]: 期望的当前值
-- ARGV[2]: 新的值
-- ARGV[3]: 过期时间（秒）
local current_value = redis.call("GET", KEYS[1])
if current_value == ARGV[1] then
    redis.call("SET", KEYS[1], ARGV[2], "EX", ARGV[3])
    return 1  -- 成功更新
else
    return 0  -- 值不匹配，更新失败
end
`

// LuaIncrementCounterScript 递增计数器脚本
// 用于统计锁的使用次数
const LuaIncrementCounterScript = `
-- KEYS[1]: 计数器key
-- ARGV[1]: 过期时间（秒）
local count = redis.call("INCR", KEYS[1])
if count == 1 then
    redis.call("EXPIRE", KEYS[1], ARGV[1])
end
return count
`

// ScriptSHA 存储已加载脚本的SHA值，避免重复加载
var ScriptSHA = make(map[string]string)

// 脚本名称常量
const (
	ScriptUnlock           = "unlock"
	ScriptRenew            = "renew"
	ScriptAcquire          = "acquire"
	ScriptExtend           = "extend"
	ScriptCheck            = "check"
	ScriptForceUnlock      = "force_unlock"
	ScriptBatchUnlock      = "batch_unlock"
	ScriptGetLockInfo      = "get_lock_info"
	ScriptCompareAndSwap   = "compare_and_swap"
	ScriptIncrementCounter = "increment_counter"
)

// GetScript 根据脚本名称获取脚本内容
func GetScript(name string) string {
	switch name {
	case ScriptUnlock:
		return LuaUnlockScript
	case ScriptRenew:
		return LuaRenewScript
	case ScriptAcquire:
		return LuaAcquireScript
	case ScriptExtend:
		return LuaExtendScript
	case ScriptCheck:
		return LuaCheckScript
	case ScriptForceUnlock:
		return LuaForceUnlockScript
	case ScriptBatchUnlock:
		return LuaBatchUnlockScript
	case ScriptGetLockInfo:
		return LuaGetLockInfoScript
	case ScriptCompareAndSwap:
		return LuaCompareAndSwapScript
	case ScriptIncrementCounter:
		return LuaIncrementCounterScript
	default:
		return ""
	}
}

// LoadScripts 预加载所有脚本到Redis服务器
// 返回加载成功的脚本数量
func LoadScripts() (int, error) {
	client := goredis.GetRedisClient()
	scripts := []string{
		ScriptUnlock,
		ScriptRenew,
		ScriptAcquire,
		ScriptExtend,
		ScriptCheck,
		ScriptForceUnlock,
		ScriptBatchUnlock,
		ScriptGetLockInfo,
		ScriptCompareAndSwap,
		ScriptIncrementCounter,
	}

	loadedCount := 0
	for _, scriptName := range scripts {
		script := GetScript(scriptName)
		if script == "" {
			continue
		}

		sha, err := client.ScriptLoad(ctx, script).Result()
		if err != nil {
			return loadedCount, err
		}

		ScriptSHA[scriptName] = sha
		loadedCount++
	}

	return loadedCount, nil
}

// ExecuteScript 执行预加载的脚本
func ExecuteScript(scriptName string, keys []string, args ...interface{}) (interface{}, error) {
	client := goredis.GetRedisClient()

	sha, exists := ScriptSHA[scriptName]
	if !exists {
		// 脚本未预加载，直接执行
		script := GetScript(scriptName)
		if script == "" {
			return nil, fmt.Errorf("未知的脚本: %s", scriptName)
		}
		return client.Eval(ctx, script, keys, args...).Result()
	}

	// 使用预加载的脚本
	result, err := client.EvalSha(ctx, sha, keys, args...).Result()
	if err != nil && err.Error() == "NOSCRIPT No matching script. Please use EVAL." {
		// 脚本可能被Redis清除了，重新加载
		script := GetScript(scriptName)
		if script != "" {
			newSHA, loadErr := client.ScriptLoad(ctx, script).Result()
			if loadErr == nil {
				ScriptSHA[scriptName] = newSHA
				return client.EvalSha(ctx, newSHA, keys, args...).Result()
			}
		}
	}

	return result, err
}
