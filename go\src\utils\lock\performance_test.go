package lock

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"testing"
	"time"

	"fincore/utils/log"
)

// BenchmarkLockUnlockPerformance 基准测试：基本加锁解锁性能
func BenchmarkLockUnlockPerformance(b *testing.B) {
	manager := NewSimpleLockManager()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("bench:lock:%d", i%1000) // 使用1000个不同的key
		lock := manager.GetLock(key, 1*time.Minute)
		lock.Lock()
		lock.Unlock()
	}
}

// BenchmarkTryLockPerformance 基准测试：尝试加锁性能
func BenchmarkTryLockPerformance(b *testing.B) {
	manager := NewSimpleLockManager()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("bench:trylock:%d", i%1000)
		lock := manager.GetLock(key, 1*time.Minute)
		success, _ := lock.TryLock()
		if success {
			lock.Unlock()
		}
	}
}

// BenchmarkChainedOperationsPerformance 基准测试：链式操作性能
func BenchmarkChainedOperationsPerformance(b *testing.B) {
	manager := NewSimpleLockManager()
	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("bench:chain:%d", i%1000)
		lock, err := manager.GetLock(key, 1*time.Minute).
			WithContext(ctx).
			WithTimeout(1 * time.Second).
			Lock()
		if err != nil {
			b.Fatal(err)
		}
		lock.Unlock()
	}
}

// BenchmarkConcurrentAccess 基准测试：并发访问性能
func BenchmarkConcurrentAccess(b *testing.B) {
	manager := NewSimpleLockManager()

	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			key := fmt.Sprintf("bench:concurrent:%d", i%100)
			lock := manager.GetLock(key, 1*time.Minute)
			success, _ := lock.TryLock()
			if success {
				lock.Unlock()
			}
			i++
		}
	})
}

// TestMemoryLeak 内存泄漏测试
func TestMemoryLeak(t *testing.T) {
	manager := NewSimpleLockManager()

	// 记录初始内存
	runtime.GC()
	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1)

	// 创建大量锁并使用
	const numLocks = 10000
	for i := 0; i < numLocks; i++ {
		key := fmt.Sprintf("memory:test:%d", i)
		lock := manager.GetLock(key, 100*time.Millisecond) // 短过期时间
		lock.Lock()
		lock.Unlock()
	}

	// 等待锁过期
	time.Sleep(200 * time.Millisecond)

	// 清理过期锁
	cleaned := manager.CleanExpiredLocks()
	t.Logf("清理了 %d 个过期锁", cleaned)

	// 强制垃圾回收
	runtime.GC()
	runtime.GC() // 调用两次确保完全清理

	// 记录清理后内存
	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)

	// 检查内存增长
	memoryGrowth := int64(m2.Alloc) - int64(m1.Alloc)
	t.Logf("内存增长: %d bytes", memoryGrowth)

	// 如果内存增长超过1MB，可能存在内存泄漏
	if memoryGrowth > 1024*1024 {
		t.Errorf("可能存在内存泄漏，内存增长: %d bytes", memoryGrowth)
	}
}

// TestHighConcurrency 高并发测试
func TestHighConcurrency(t *testing.T) {
	manager := NewSimpleLockManager()
	const numGoroutines = 1000
	const numOperations = 100

	var wg sync.WaitGroup
	var successCount int64
	var mu sync.Mutex

	startTime := time.Now()

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			localSuccess := 0
			for j := 0; j < numOperations; j++ {
				key := fmt.Sprintf("concurrent:test:%d", j%10) // 10个共享资源
				lock := manager.GetLock(key, 1*time.Minute)

				if success, _ := lock.TryLock(); success {
					localSuccess++
					// 模拟短暂的工作
					time.Sleep(time.Microsecond)
					lock.Unlock()
				}
			}

			mu.Lock()
			successCount += int64(localSuccess)
			mu.Unlock()
		}(i)
	}

	wg.Wait()
	duration := time.Since(startTime)

	totalOperations := int64(numGoroutines * numOperations)
	t.Logf("高并发测试完成:")
	t.Logf("- 总操作数: %d", totalOperations)
	t.Logf("- 成功操作数: %d", successCount)
	t.Logf("- 成功率: %.2f%%", float64(successCount)/float64(totalOperations)*100)
	t.Logf("- 总耗时: %v", duration)
	t.Logf("- 平均每操作耗时: %v", duration/time.Duration(totalOperations))
}

// TestLockContention 锁竞争测试
func TestLockContention(t *testing.T) {
	manager := NewSimpleLockManager()
	const numGoroutines = 100
	const sharedKey = "contention:test"

	var wg sync.WaitGroup
	var successCount int64
	var mu sync.Mutex

	startTime := time.Now()

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			lock := manager.GetLock(sharedKey, 1*time.Minute)

			if success, _ := lock.TryLock(); success {
				mu.Lock()
				successCount++
				mu.Unlock()

				// 持有锁一段时间
				time.Sleep(10 * time.Millisecond)
				lock.Unlock()
			}
		}(i)
	}

	wg.Wait()
	duration := time.Since(startTime)

	t.Logf("锁竞争测试完成:")
	t.Logf("- 参与竞争的goroutine数: %d", numGoroutines)
	t.Logf("- 成功获取锁的数量: %d", successCount)
	t.Logf("- 总耗时: %v", duration)

	// 在高竞争情况下，只有一个goroutine应该能获取锁
	if successCount != 1 {
		t.Errorf("期望只有1个goroutine获取锁，实际: %d", successCount)
	}
}

// TestLockExpirationPerformance 锁过期性能测试
func TestLockExpirationPerformance(t *testing.T) {
	manager := NewSimpleLockManager()

	// 创建大量短期锁
	const numLocks = 1000
	for i := 0; i < numLocks; i++ {
		key := fmt.Sprintf("expiration:test:%d", i)
		lock := manager.GetLock(key, 50*time.Millisecond)
		lock.Lock()
		// 故意不解锁，让它们过期
	}

	// 检查锁数量
	stats := manager.GetStats()
	if len(stats) != numLocks {
		t.Errorf("期望 %d 个锁，实际: %d", numLocks, len(stats))
	}

	// 等待过期
	time.Sleep(100 * time.Millisecond)

	// 清理过期锁
	cleaned := manager.CleanExpiredLocks()
	t.Logf("清理了 %d 个过期锁", cleaned)

	// 检查清理后的锁数量
	stats = manager.GetStats()
	if len(stats) != 0 {
		t.Errorf("清理后应该没有锁，实际: %d", len(stats))
	}
}

// TestContextPerformance 上下文性能测试
func TestContextPerformance(t *testing.T) {
	manager := NewSimpleLockManager()
	ctx := log.SetRequestIDToContext(context.Background(), "perf-test-123")

	const numOperations = 1000
	startTime := time.Now()

	for i := 0; i < numOperations; i++ {
		key := fmt.Sprintf("context:perf:%d", i)
		lock := manager.GetLock(key, 1*time.Minute).WithContext(ctx)
		lock.Lock()
		lock.Unlock()
	}

	duration := time.Since(startTime)
	avgDuration := duration / numOperations

	t.Logf("上下文性能测试:")
	t.Logf("- 操作数: %d", numOperations)
	t.Logf("- 总耗时: %v", duration)
	t.Logf("- 平均每操作耗时: %v", avgDuration)

	// 检查性能是否合理（每操作应该在1ms以内）
	if avgDuration > time.Millisecond {
		t.Errorf("性能过慢，平均每操作耗时: %v", avgDuration)
	}
}
