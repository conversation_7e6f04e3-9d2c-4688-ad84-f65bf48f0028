package lock

import (
	"context"
	"crypto/rand"
	"fmt"
	"os"
	"sync"
	"time"

	"fincore/utils/log"
	"fincore/utils/utilstool/goredis"
)

// redisLock Redis分布式锁实现
type redisLock struct {
	key        string
	value      string        // 锁的唯一标识符
	expiration time.Duration // 锁的过期时间
	isLocked   bool          // 是否已加锁
	ctx        context.Context
	cancel     context.CancelFunc
	logger     *log.Logger
	timeout    time.Duration // 加锁超时时间
	mu         sync.Mutex    // 保护内部状态
	acquiredAt time.Time     // 获取锁的时间
	lastError  error         // 最后一次错误
	warnings   []string      // 警告信息
}

// generateLockValue 生成锁的唯一标识符
func generateLockValue() string {
	// 使用随机数 + 进程ID + 时间戳确保唯一性
	randomBytes := make([]byte, 8)
	rand.Read(randomBytes)
	pid := os.Getpid()
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("%x:%d:%d", randomBytes, pid, timestamp)
}

// newRedisLock 创建新的Redis锁
func newRedisLock(key string, expiration time.Duration, logger *log.Logger) *redisLock {
	ctx, cancel := context.WithCancel(context.Background())
	return &redisLock{
		key:        fmt.Sprintf("lock:%s", key), // 添加前缀避免键冲突
		value:      generateLockValue(),
		expiration: expiration,
		isLocked:   false,
		ctx:        ctx,
		cancel:     cancel,
		logger:     logger,
		timeout:    30 * time.Second, // 默认30秒加锁超时
	}
}

// Lock 加锁（阻塞式，兼容内存锁行为）
func (rl *redisLock) Lock() (rLock Lock, err error) {
	rLock = rl
	rl.mu.Lock()
	defer rl.mu.Unlock()

	if rl.isLocked {
		// 已经加锁，直接返回
		return
	}

	startTime := time.Now()

	// 记录加锁开始日志
	if rl.logger != nil {
		rl.logger.WithFields(
			log.String("key", rl.key),
			log.String("value", rl.value),
			log.String("action", "redis_lock_start"),
			log.Duration("expiration", rl.expiration),
			log.Duration("timeout", rl.timeout),
		).Debug("开始Redis加锁")
	}

	// 清除之前的错误和警告
	rl.lastError = nil
	rl.warnings = nil

	// 阻塞式获取锁，带重试机制
	success := rl.acquireLockWithRetry()
	if success {
		rl.isLocked = true
		rl.acquiredAt = time.Now() // 记录获取时间
		// 启动看门狗自动续期
		rl.startWatchdog()

		// 记录加锁成功日志
		if rl.logger != nil {
			duration := time.Since(startTime)
			rl.logger.WithFields(
				log.String("key", rl.key),
				log.String("value", rl.value),
				log.String("action", "redis_lock_success"),
				log.Duration("duration", duration),
			).Info("Redis加锁成功")
		}
	} else {
		// 记录加锁失败
		rl.lastError = fmt.Errorf("redis锁获取失败: 超时或Redis不可用")

		// 记录加锁失败日志
		if rl.logger != nil {
			duration := time.Since(startTime)
			rl.logger.WithFields(
				log.String("key", rl.key),
				log.String("value", rl.value),
				log.String("action", "redis_lock_failed"),
				log.Duration("duration", duration),
			).Warn("Redis加锁失败")
		}

		return rl, rl.lastError
	}

	return
}

// acquireLock 尝试获取Redis锁
func (rl *redisLock) acquireLock() bool {
	client := goredis.GetRedisClient()

	// 使用SET key value NX EX seconds命令原子性地设置锁
	result, err := client.SetNX(rl.ctx, rl.key, rl.value, rl.expiration).Result()
	if err != nil {
		if rl.logger != nil {
			rl.logger.WithFields(
				log.String("key", rl.key),
				log.String("error", err.Error()),
			).Error("Redis加锁操作失败")
		}
		return false
	}

	return result
}

// acquireLockWithRetry 阻塞式获取锁，带重试机制
func (rl *redisLock) acquireLockWithRetry() bool {
	// 首先尝试立即获取锁
	if rl.acquireLock() {
		return true
	}

	// 如果没有设置超时时间，使用默认超时
	timeout := rl.timeout
	if timeout <= 0 {
		timeout = 30 * time.Second
	}

	// 创建超时上下文
	ctx, cancel := context.WithTimeout(rl.ctx, timeout)
	defer cancel()

	// 重试间隔，从配置中获取或使用默认值
	retryInterval := 100 * time.Millisecond

	ticker := time.NewTicker(retryInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			// 超时或上下文取消
			if rl.logger != nil {
				rl.logger.WithFields(
					log.String("key", rl.key),
					log.String("reason", ctx.Err().Error()),
				).Warn("Redis加锁超时或被取消")
			}
			return false

		case <-ticker.C:
			// 重试获取锁
			if rl.acquireLock() {
				return true
			}
		}
	}
}

// Unlock 解锁
func (rl *redisLock) Unlock() Lock {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	if !rl.isLocked {
		// 记录重复解锁警告
		if rl.logger != nil {
			rl.logger.WithFields(
				log.String("key", rl.key),
				log.String("action", "redis_unlock_duplicate"),
			).Warn("尝试解锁未锁定的Redis锁")
		}
		return rl
	}

	// 记录解锁开始日志
	if rl.logger != nil {
		rl.logger.WithFields(
			log.String("key", rl.key),
			log.String("value", rl.value),
			log.String("action", "redis_unlock_start"),
		).Info("开始Redis解锁")
	}

	// 停止看门狗
	rl.cancel()

	// 使用Lua脚本安全地删除锁
	success := rl.releaseLock()
	rl.isLocked = false

	// 记录解锁结果日志
	if rl.logger != nil {
		rl.logger.WithFields(
			log.String("key", rl.key),
			log.String("value", rl.value),
			log.String("action", "redis_unlock_result"),
			log.Bool("success", success),
		).Info("Redis解锁完成")
	}

	return rl
}

// releaseLock 使用Lua脚本安全地释放锁
func (rl *redisLock) releaseLock() bool {
	client := goredis.GetRedisClient()

	// 使用新的上下文，避免因为cancel()导致的上下文取消问题
	ctx := context.Background()

	// Lua脚本：只有当锁的值匹配时才删除
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end
	`

	result, err := client.Eval(ctx, luaScript, []string{rl.key}, rl.value).Result()
	if err != nil {
		if rl.logger != nil {
			rl.logger.WithFields(
				log.String("key", rl.key),
				log.String("error", err.Error()),
			).Error("Redis解锁脚本执行失败")
		}
		return false
	}

	// 检查脚本执行结果
	if deleted, ok := result.(int64); ok && deleted == 1 {
		return true
	}

	return false
}

// TryLock 尝试加锁
func (rl *redisLock) TryLock() (bool, Lock) {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	if rl.isLocked {
		// 已经加锁，返回成功
		return true, rl
	}

	// 记录尝试加锁日志
	if rl.logger != nil {
		rl.logger.WithFields(
			log.String("key", rl.key),
			log.String("value", rl.value),
			log.String("action", "redis_try_lock"),
		).Info("尝试Redis加锁")
	}

	// 清除之前的错误
	rl.lastError = nil

	// 尝试获取锁
	success := rl.acquireLock()
	if success {
		rl.isLocked = true
		rl.acquiredAt = time.Now() // 记录获取时间
		// 启动看门狗自动续期
		rl.startWatchdog()

		// 记录尝试加锁成功日志
		if rl.logger != nil {
			rl.logger.WithFields(
				log.String("key", rl.key),
				log.String("value", rl.value),
				log.String("action", "redis_try_lock_success"),
			).Info("Redis尝试加锁成功")
		}

		return true, rl
	}

	// 记录失败原因
	rl.lastError = fmt.Errorf("Redis锁被占用")

	// 记录尝试加锁失败日志
	if rl.logger != nil {
		rl.logger.WithFields(
			log.String("key", rl.key),
			log.String("value", rl.value),
			log.String("action", "redis_try_lock_failed"),
		).Info("Redis尝试加锁失败，锁被占用")
	}

	return false, rl
}

// WithTimeout 设置超时时间
func (rl *redisLock) WithTimeout(timeout time.Duration) Lock {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	rl.timeout = timeout
	return rl
}

// WithContext 设置上下文
func (rl *redisLock) WithContext(ctx context.Context) Lock {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	// 取消原有上下文
	rl.cancel()

	// 创建新的上下文
	newCtx, cancel := context.WithCancel(ctx)
	rl.ctx = newCtx
	rl.cancel = cancel

	// 如果有日志对象，使用新上下文创建新的日志对象
	if rl.logger != nil {
		rl.logger = rl.logger.WithContext(ctx)
	}

	return rl
}

// WithLogger 设置日志对象
func (rl *redisLock) WithLogger(logger *log.Logger) Lock {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	if rl.ctx != nil && rl.ctx != context.Background() {
		rl.logger = logger.WithContext(rl.ctx)
	} else {
		rl.logger = logger
	}

	return rl
}

// GetKey 获取锁的key
func (rl *redisLock) GetKey() string {
	return rl.key
}

// IsActuallyLocked 检查是否真正持有锁
func (rl *redisLock) IsActuallyLocked() bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	return rl.isLocked
}

// GetLockStatus 获取详细锁状态
func (rl *redisLock) GetLockStatus() LockStatus {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	return LockStatus{
		IsLocked:   rl.isLocked,
		LockType:   LockTypeRedis,
		AcquiredAt: rl.acquiredAt,
		Error:      rl.lastError,
		Warnings:   append([]string{}, rl.warnings...), // 复制切片避免并发问题
	}
}

// GetLastError 获取最后一次错误
func (rl *redisLock) GetLastError() error {
	rl.mu.Lock()
	defer rl.mu.Unlock()
	return rl.lastError
}

// startWatchdog 启动看门狗自动续期
func (rl *redisLock) startWatchdog() {
	// 续期间隔为过期时间的1/3
	renewalInterval := rl.expiration / 3
	if renewalInterval < time.Second {
		renewalInterval = time.Second // 最小1秒间隔
	}

	go func() {
		ticker := time.NewTicker(renewalInterval)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// 检查锁是否还被持有
				rl.mu.Lock()
				if !rl.isLocked {
					rl.mu.Unlock()
					return // 锁已释放，停止续期
				}
				rl.mu.Unlock()

				// 执行续期
				success := rl.renewLock()
				if !success {
					// 续期失败，可能锁已被其他进程获取
					rl.mu.Lock()
					rl.isLocked = false
					rl.mu.Unlock()
					return
				}

			case <-rl.ctx.Done():
				return // 上下文取消，停止续期
			}
		}
	}()
}

// renewLock 续期锁
func (rl *redisLock) renewLock() bool {
	client := goredis.GetRedisClient()

	// Lua脚本：只有当锁的值匹配时才续期
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("EXPIRE", KEYS[1], ARGV[2])
		else
			return 0
		end
	`

	expirationSeconds := int(rl.expiration.Seconds())
	result, err := client.Eval(rl.ctx, luaScript, []string{rl.key}, rl.value, expirationSeconds).Result()
	if err != nil {
		if rl.logger != nil {
			rl.logger.WithFields(
				log.String("key", rl.key),
				log.String("error", err.Error()),
			).Error("Redis锁续期脚本执行失败")
		}
		return false
	}

	// 检查脚本执行结果
	if renewed, ok := result.(int64); ok && renewed == 1 {
		if rl.logger != nil {
			rl.logger.WithFields(
				log.String("key", rl.key),
				log.String("value", rl.value),
				log.String("action", "redis_lock_renewed"),
				log.Duration("expiration", rl.expiration),
			).Debug("Redis锁续期成功")
		}
		return true
	}

	if rl.logger != nil {
		rl.logger.WithFields(
			log.String("key", rl.key),
			log.String("value", rl.value),
			log.String("action", "redis_lock_renewal_failed"),
		).Warn("Redis锁续期失败，锁可能已被其他进程获取")
	}

	return false
}
