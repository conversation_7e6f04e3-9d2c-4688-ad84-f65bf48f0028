package lock

import (
	"context"
	"fmt"
	"sync"
	"time"

	"fincore/utils/log"
	"fincore/utils/utilstool/goredis"
)

// RedisLockManager Redis分布式锁管理器
type RedisLockManager struct {
	locks sync.Map // key: string, value: *redisLock，用于跟踪本地创建的锁
}

// NewRedisLockManager 创建新的Redis锁管理器
func NewRedisLockManager() LockManager {
	return &RedisLockManager{}
}

// GetLock 获取指定key的锁
func (rlm *RedisLockManager) GetLock(key string, expiration ...time.Duration) Lock {
	var exp time.Duration
	if len(expiration) > 0 {
		exp = expiration[0]
	} else {
		exp = 30 * time.Second // Redis锁默认30秒过期
	}

	// 创建新的Redis锁
	redisLock := newRedisLock(key, exp, nil)

	// 存储锁引用以便统计和管理
	rlm.locks.Store(redisLock.key, redisLock)

	return redisLock
}

// GetLockWithLogger 获取指定key的锁，使用指定日志对象
func (rlm *RedisLockManager) GetLockWithLogger(key string, logger *log.Logger, expiration ...time.Duration) Lock {
	var exp time.Duration
	if len(expiration) > 0 {
		exp = expiration[0]
	} else {
		exp = 30 * time.Second // Redis锁默认30秒过期
	}

	// 创建新的Redis锁
	redisLock := newRedisLock(key, exp, logger)

	// 存储锁引用以便统计和管理
	rlm.locks.Store(redisLock.key, redisLock)

	return redisLock
}

// UnlockByKey 通过key解锁
func (rlm *RedisLockManager) UnlockByKey(key string) error {
	lockKey := fmt.Sprintf("lock:%s", key)

	// 尝试从本地缓存中找到锁
	if value, exists := rlm.locks.Load(lockKey); exists {
		if redisLock, ok := value.(*redisLock); ok {
			redisLock.Unlock()
			return nil
		}
	}

	// 如果本地没有找到锁，尝试直接从Redis删除
	// 注意：这种情况下无法验证锁的所有权，存在安全风险
	client := goredis.GetRedisClient()
	result, err := client.Del(context.Background(), lockKey).Result()
	if err != nil {
		return fmt.Errorf("删除Redis锁失败: %v", err)
	}

	if result == 0 {
		return fmt.Errorf("锁 %s 不存在", key)
	}

	return nil
}

// CleanExpiredLocks 清理过期锁
// 注意：Redis锁会自动过期，这个方法主要清理本地缓存中的锁引用
func (rlm *RedisLockManager) CleanExpiredLocks() int {
	count := 0
	client := goredis.GetRedisClient()

	// 遍历本地缓存的锁
	rlm.locks.Range(func(key, value interface{}) bool {
		lockKey := key.(string)
		redisLock := value.(*redisLock)

		// 检查Redis中的锁是否还存在
		exists, err := client.Exists(context.Background(), lockKey).Result()
		if err != nil {
			// 网络错误，跳过这个锁
			return true
		}

		if exists == 0 {
			// Redis中的锁已过期或被删除，清理本地缓存
			rlm.locks.Delete(lockKey)

			// 如果锁还认为自己是锁定状态，更新状态
			redisLock.mu.Lock()
			if redisLock.isLocked {
				redisLock.isLocked = false
				redisLock.cancel() // 停止看门狗
			}
			redisLock.mu.Unlock()

			count++
		}

		return true
	})

	return count
}

// GetStats 获取锁统计信息
func (rlm *RedisLockManager) GetStats() map[string]LockStats {
	stats := make(map[string]LockStats)
	client := goredis.GetRedisClient()

	rlm.locks.Range(func(key, value interface{}) bool {
		lockKey := key.(string)
		redisLock := value.(*redisLock)

		// 检查Redis中的锁状态
		exists, err := client.Exists(context.Background(), lockKey).Result()
		isLocked := false
		if err == nil && exists == 1 {
			// 进一步检查锁的值是否匹配
			val, err := client.Get(context.Background(), lockKey).Result()
			if err == nil && val == redisLock.value {
				isLocked = true
			}
		}

		// 获取TTL信息
		var createdAt time.Time
		ttl, err := client.TTL(context.Background(), lockKey).Result()
		if err == nil && ttl > 0 {
			// 根据TTL和过期时间推算创建时间
			createdAt = time.Now().Add(ttl - redisLock.expiration)
		} else {
			// 无法获取准确的创建时间，使用当前时间
			createdAt = time.Now()
		}

		stats[lockKey] = LockStats{
			Key:       lockKey,
			CreatedAt: createdAt,
			IsLocked:  isLocked,
			UseCount:  1, // Redis锁无法准确统计使用次数
		}

		return true
	})

	return stats
}

// GetRedisLockStats 获取Redis特有的锁统计信息
func (rlm *RedisLockManager) GetRedisLockStats() map[string]RedisLockStats {
	stats := make(map[string]RedisLockStats)
	client := goredis.GetRedisClient()

	rlm.locks.Range(func(key, value interface{}) bool {
		lockKey := key.(string)
		redisLock := value.(*redisLock)

		// 检查Redis中的锁状态
		exists, err := client.Exists(context.Background(), lockKey).Result()
		isLocked := false
		var ttl time.Duration
		var lockValue string

		if err == nil && exists == 1 {
			// 获取锁的值和TTL
			val, err := client.Get(context.Background(), lockKey).Result()
			if err == nil {
				lockValue = val
				if val == redisLock.value {
					isLocked = true
				}
			}

			ttlResult, err := client.TTL(context.Background(), lockKey).Result()
			if err == nil {
				ttl = ttlResult
			}
		}

		stats[lockKey] = RedisLockStats{
			Key:        lockKey,
			Value:      lockValue,
			IsLocked:   isLocked,
			TTL:        ttl,
			Expiration: redisLock.expiration,
			LocalValue: redisLock.value,
		}

		return true
	})

	return stats
}

// RedisLockStats Redis锁的详细统计信息
type RedisLockStats struct {
	Key        string        // 锁的键
	Value      string        // Redis中锁的值
	IsLocked   bool          // 是否被锁定
	TTL        time.Duration // 剩余生存时间
	Expiration time.Duration // 过期时间设置
	LocalValue string        // 本地锁的值（用于比较）
}

// IsHealthy 检查Redis锁管理器的健康状态
func (rlm *RedisLockManager) IsHealthy() bool {
	client := goredis.GetRedisClient()

	// 尝试执行一个简单的Redis命令
	_, err := client.Ping(context.Background()).Result()
	return err == nil
}

// GetRedisInfo 获取Redis连接信息
func (rlm *RedisLockManager) GetRedisInfo() map[string]interface{} {
	client := goredis.GetRedisClient()

	info := make(map[string]interface{})

	// 获取Redis服务器信息
	if result, err := client.Info(context.Background()).Result(); err == nil {
		info["server_info"] = result
	}

	// 获取连接池状态
	poolStats := client.PoolStats()
	info["pool_stats"] = map[string]interface{}{
		"hits":        poolStats.Hits,
		"misses":      poolStats.Misses,
		"timeouts":    poolStats.Timeouts,
		"total_conns": poolStats.TotalConns,
		"idle_conns":  poolStats.IdleConns,
		"stale_conns": poolStats.StaleConns,
	}

	return info
}
