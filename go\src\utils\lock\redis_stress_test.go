package lock

import (
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

// TestRedisLockStressCorrectness Redis锁功能正确性压力测试
func TestRedisLockStressCorrectness(t *testing.T) {
	if !isRedisAvailable() {
		t.<PERSON><PERSON>("Redis不可用，跳过Redis压力测试")
	}

	manager := NewRedisLockManager()

	// 测试大量不同key的锁
	const numKeys = 1000
	var wg sync.WaitGroup
	var successCount int64

	for i := 0; i < numKeys; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			key := fmt.Sprintf("stress:correctness:%d", id)
			lock := manager.GetLock(key, 30*time.Second)

			// 每个key应该都能成功获取锁
			success, _ := lock.TryLock()
			if success {
				atomic.AddInt64(&successCount, 1)

				// 持有锁一小段时间
				time.Sleep(10 * time.Millisecond)
				lock.Unlock()
			}
		}(i)
	}

	wg.Wait()

	if successCount != numKeys {
		t.<PERSON>("期望 %d 个锁成功，实际: %d", numKeys, successCount)
	}

	t.Logf("功能正确性测试: %d/%d 锁成功获取", successCount, numKeys)
}

// TestRedisLockStressConcurrency Redis锁并发安全性压力测试
func TestRedisLockStressConcurrency(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis压力测试")
	}

	manager := NewRedisLockManager()

	// 多个goroutine竞争同一个锁，使用阻塞式Lock
	const numGoroutines = 10 // 减少goroutine数量以便观察
	const lockKey = "stress:concurrency:shared"

	var wg sync.WaitGroup
	var sharedCounter int64
	var processOrder []int
	var mu sync.Mutex

	// 清理可能存在的锁
	manager.UnlockByKey(lockKey)
	time.Sleep(100 * time.Millisecond)

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			lock := manager.GetLock(lockKey, 30*time.Second)
			lock.Lock() // 使用阻塞式Lock，所有goroutine都会获得锁

			// 临界区操作
			oldValue := atomic.LoadInt64(&sharedCounter)
			time.Sleep(10 * time.Millisecond) // 模拟处理时间
			atomic.StoreInt64(&sharedCounter, oldValue+1)

			// 记录处理顺序
			mu.Lock()
			processOrder = append(processOrder, id)
			mu.Unlock()

			lock.Unlock()
		}(i)
	}

	wg.Wait()

	// 所有goroutine都应该成功处理
	if sharedCounter != int64(numGoroutines) {
		t.Errorf("期望共享计数器为%d，实际: %d", numGoroutines, sharedCounter)
	}

	// 检查是否有序处理（证明互斥性）
	if len(processOrder) != numGoroutines {
		t.Errorf("期望处理顺序长度为%d，实际: %d", numGoroutines, len(processOrder))
	}

	t.Logf("并发安全性测试: %d个goroutine顺序处理，共享计数器: %d", len(processOrder), sharedCounter)
	t.Logf("处理顺序: %v", processOrder)
}

// TestRedisLockStressPerformance Redis锁性能压力测试
func TestRedisLockStressPerformance(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis压力测试")
	}

	manager := NewRedisLockManager()

	// 性能测试参数
	const numOperations = 10000
	const numGoroutines = 50

	var wg sync.WaitGroup
	var totalOperations int64
	var successfulLocks int64

	startTime := time.Now()

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			operationsPerGoroutine := numOperations / numGoroutines
			for j := 0; j < operationsPerGoroutine; j++ {
				key := fmt.Sprintf("stress:performance:%d:%d", id, j)
				lock := manager.GetLock(key, 10*time.Second)

				success, _ := lock.TryLock()
				if success {
					atomic.AddInt64(&successfulLocks, 1)
					lock.Unlock()
				}

				atomic.AddInt64(&totalOperations, 1)
			}
		}(i)
	}

	wg.Wait()

	duration := time.Since(startTime)
	opsPerSecond := float64(totalOperations) / duration.Seconds()

	t.Logf("性能测试结果:")
	t.Logf("- 总操作数: %d", totalOperations)
	t.Logf("- 成功锁操作: %d", successfulLocks)
	t.Logf("- 成功率: %.2f%%", float64(successfulLocks)/float64(totalOperations)*100)
	t.Logf("- 总耗时: %v", duration)
	t.Logf("- 吞吐量: %.0f ops/sec", opsPerSecond)

	// 性能基准检查
	if opsPerSecond < 1000 {
		t.Logf("警告: 吞吐量较低 (%.0f ops/sec)", opsPerSecond)
	}
}

// TestRedisLockStressMemoryLeak Redis锁内存泄露测试
func TestRedisLockStressMemoryLeak(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis压力测试")
	}

	manager := NewRedisLockManager()

	// 记录初始内存
	runtime.GC()
	var initialMem runtime.MemStats
	runtime.ReadMemStats(&initialMem)

	// 创建大量锁并释放
	const numCycles = 10
	const locksPerCycle = 1000

	for cycle := 0; cycle < numCycles; cycle++ {
		t.Logf("内存测试周期 %d/%d", cycle+1, numCycles)

		locks := make([]Lock, locksPerCycle)

		// 创建锁
		for i := 0; i < locksPerCycle; i++ {
			key := fmt.Sprintf("stress:memory:%d:%d", cycle, i)
			lock := manager.GetLock(key, 5*time.Second)
			lock.Lock()
			locks[i] = lock
		}

		// 释放锁
		for _, lock := range locks {
			lock.Unlock()
		}

		// 清理过期锁
		cleaned := manager.CleanExpiredLocks()
		t.Logf("- 清理了 %d 个过期锁", cleaned)

		// 强制垃圾回收
		runtime.GC()

		// 检查内存使用
		var currentMem runtime.MemStats
		runtime.ReadMemStats(&currentMem)

		memGrowth := int64(currentMem.Alloc) - int64(initialMem.Alloc)
		t.Logf("- 内存增长: %d KB", memGrowth/1024)

		// 如果内存增长过多，发出警告
		if memGrowth > 10*1024*1024 { // 10MB
			t.Logf("警告: 内存增长过多: %d KB", memGrowth/1024)
		}
	}

	// 最终内存检查
	runtime.GC()
	var finalMem runtime.MemStats
	runtime.ReadMemStats(&finalMem)

	finalGrowth := int64(finalMem.Alloc) - int64(initialMem.Alloc)
	t.Logf("最终内存增长: %d KB", finalGrowth/1024)

	// 内存泄露检查
	if finalGrowth > 50*1024*1024 { // 50MB
		t.Errorf("可能存在内存泄露，内存增长: %d KB", finalGrowth/1024)
	}
}

// TestRedisLockStressWatchdog 看门狗机制压力测试
func TestRedisLockStressWatchdog(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis压力测试")
	}

	manager := NewRedisLockManager()

	// 测试看门狗在高并发下的表现
	const numLocks = 50
	var wg sync.WaitGroup

	for i := 0; i < numLocks; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			key := fmt.Sprintf("stress:watchdog:%d", id)
			lock := manager.GetLock(key, 2*time.Second) // 短过期时间

			lock.Lock()

			// 持有锁超过过期时间，测试看门狗续期
			time.Sleep(5 * time.Second)

			// 检查锁是否仍然有效
			lock2 := manager.GetLock(key, 2*time.Second)
			success, _ := lock2.TryLock()
			if success {
				t.Errorf("锁 %s 应该仍被持有（看门狗续期失败）", key)
				lock2.Unlock()
			}

			lock.Unlock()
		}(i)
	}

	wg.Wait()
	t.Logf("看门狗压力测试完成: %d 个锁", numLocks)
}

// TestRedisLockStressFailover Redis锁故障转移测试
func TestRedisLockStressFailover(t *testing.T) {
	if !isRedisAvailable() {
		t.Skip("Redis不可用，跳过Redis压力测试")
	}

	// 测试工厂模式的故障转移
	factory := NewLockFactory()

	// 配置为Redis锁
	config := &LockConfig{
		Type: LockTypeRedis,
		Redis: RedisConfig{
			DefaultExpiration: 30,
			RenewalInterval:   10,
			MaxRetryTimes:     3,
			RetryIntervalMs:   100,
			EnableWatchdog:    true,
		},
	}

	err := factory.UpdateConfig(config)
	if err != nil {
		t.Fatalf("更新配置失败: %v", err)
	}

	// 验证使用Redis锁
	if !factory.IsUsingRedis() {
		t.Skip("Redis锁不可用，跳过故障转移测试")
	}

	// 创建一些锁
	const numLocks = 100
	var successCount int64

	for i := 0; i < numLocks; i++ {
		lock := factory.GetLock(fmt.Sprintf("stress:failover:%d", i), 30*time.Second)
		success, _ := lock.TryLock()
		if success {
			atomic.AddInt64(&successCount, 1)
			lock.Unlock()
		}
	}

	t.Logf("故障转移测试: %d/%d 锁成功", successCount, numLocks)

	// 检查健康状态
	status := factory.GetHealthStatus()
	t.Logf("系统健康状态: %+v", status)
}
