import { defHttp } from '@/utils/http';
enum Api {
  ListLoginLogs = '/monitor/listLoginLogs',
  ListSmsRecords = '/monitor/listSmsRecords',
  ListOperateLogs = '/monitor/listOperateLogs'
}

// 登录日志
export function getLoginLogList(params: any) {
  return defHttp.post<any>({
    url: Api.ListLoginLogs,
    params
  }, {
    errorMessageMode: 'message'
  });
}
// 短信记录
export function getSmsRecordList(params: any) {
  return defHttp.post<any>({
    url: Api.ListSmsRecords,
    params
  }, {
    errorMessageMode: 'message'
  });
}
// 操作日志
export function getOperateLogList(params: any) {
  return defHttp.post<any>({
    url: Api.ListOperateLogs,
    params
  }, {
    errorMessageMode: 'message'
  });
}
