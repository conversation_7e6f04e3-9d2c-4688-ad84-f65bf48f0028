import { defHttp } from '@/utils/http';

// 基础API响应接口
export interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 用户查询参数接口
export interface CustomerQueryParams {
  // 基础信息
  name?: string;
  mobile?: string;
  idCard?: string;
  reviewerID?: string;
  channelId?: string;
  
  // 风控相关
  isRisk?: string;
  riskFlowNumber?: string;
  riskScoreMin?: string;
  riskScoreMax?: string;
  
  // 额度相关
  reminderQuotaMin?: string;
  reminderQuotaMax?: string;
  
  // 状态相关
  identityStatus?: string;
  identitySubStatus?: string;
  orderStatus?: string;
  registerIncomplete?: string;
  realNameIncomplete?: string;
  hasQuotaNoOrder?: string;
  isNewUser?: string;
  isComplaint?: string;
  
  // 其他
  userRemark?: string;
  loanCount?: string;
  deviceSource?: string;
  
  // 时间范围
  loanTimeStart?: string;
  loanTimeEnd?: string;
  registerTimeStart?: string;
  registerTimeEnd?: string;
  
  // 分页
  page?: number;
  pageSize?: number;
}

// 用户信息接口
export interface CustomerListItem {
  id: number;
  channelName: string;
  reviewerName: string;
  deviceSource: string;
  name: string;
  mobile: string;
  userRemark: string;
  loanTime: string;
  createTime: string;
  loanCount: number;
  identityStatusText: string;
  identityStatusValue: number;
  orderStatusText: string;
  orderStatusValue: number;
  complaintStatusText: string;
  complaintStatusValue: number;
  allQuota: number;
  reminderQuota: number;
  riskScore: number;
  idCardMasked: string;
  idCardFull: string;
}

// 用户列表响应接口（实际返回的数据结构）
export interface CustomerListResponse {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  list: CustomerListItem[];
}

// 筛选选项接口
export interface CustomerOptions {
  reviewerOptions: OptionItem[];
  channelOptions: OptionItem[];
}

// 用户详情接口
export interface CustomerDetail extends CustomerListItem {
  // 可以添加更多详情字段
}

export interface FkDataList {}

export interface OptionItem {
  value: string;
  label: string;
}

export interface CustomerRemarkData {
  id: number;
  userRemark: string;
  updateTime: string;
}

export interface CustomerStatistics {
  totalCustomers: number;
  newCustomers: number;
  activeCustomers: number;
  riskCustomers: number;
  complaintCustomers: number;
}

export interface reportsQueryParams {
  customer_id: string,
  start_date: string,
  end_date: string
}

// 银行卡接口
export interface bankCardList {

}

enum Api {
  ListCustomers = '/customer/customercontroller/listCustomers',
  GetCustomerDetail = '/customer/customercontroller/getCustomerDetail',
  GETREPORTS = '/risk/riskcontroller/getReports',
  // GETBANKCARDLIST = '/bankcard/getBankCardList',
  GETBANKCARDLIST = '/bankcard/bankcardcontroller/getBankCardList',
  UpdateCustomerRemark = '/customer/customercontroller/updateCustomerRemark',
  GetCustomerRemarks = '/customer/customercontroller/getCustomerRemarks',
  GetCustomerOptions = '/customer/customercontroller/getCustomerOptions',
  GetCustomerStatistics = '/customer/customercontroller/getCustomerStatistics',
  ExportCustomers = '/customer/customercontroller/exportCustomers',
  UnlockCustomer = '/customer/customercontroller/unlockCustomer',
  UPDATECUSTOMERSTATUS = '/customer/customercontroller/updateCustomerStatus',
  GetProductsByAmount = '/risk/riskcontroller/getProductsByAmount',
  UpdateCustomerQuota = '/customer/customercontroller/updateCustomerQuota',
  DeleteCustomer = '/customer/customercontroller/postDeleteCustomer',
  ForceRefreshRisk = '/risk/riskcontroller/getForceRefreshRisk',
  // 黑名单
  BlacklistList = '/customer/customercontroller/getBlacklistCustomers',
}

/**
 * 获取用户列表
 */
export function getCustomerList(params?: CustomerQueryParams) {
  return defHttp.post<CustomerListResponse>({
    url: Api.ListCustomers,
    params
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取用户详情
 */
export function getCustomerDetail(id: string) {
  return defHttp.get<BaseResponse<CustomerDetail>>({
    url: Api.GetCustomerDetail,
    params: { id: id }
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取用户风控报告
 */
export function getReports(params: reportsQueryParams) {
  return defHttp.get<BaseResponse<FkDataList>>({
    url: Api.GETREPORTS,
    params: params
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取用户绑定的银行卡
 */
export function getBankCardList(params: { customer_id: String }) {
  return defHttp.get<BaseResponse<bankCardList>>(
    {
      url: Api.GETBANKCARDLIST,
      params: params,
    },
    {
      errorMessageMode: 'message',
    }
  );
}

/**
 * 更新客户备注
 */
export function updateCustomerRemark(id: number, params: { userRemark: string }) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.UpdateCustomerRemark,
    params: {
      id: id,
      userRemark: params.userRemark
    }
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取客户备注历史
 */
export function getCustomerRemarks(id: number) {
  return defHttp.get<BaseResponse<CustomerRemarkData>>({
    url: Api.GetCustomerRemarks,
    params: { id: id }
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取客户筛选选项
 */
export function getCustomerOptions() {
  return defHttp.get<any>({
    url: Api.GetCustomerOptions
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取客户统计信息
 */
export function getCustomerStatistics() {
  return defHttp.get<BaseResponse<CustomerStatistics>>({
    url: Api.GetCustomerStatistics
  }, {
    errorMessageMode: 'message'
  });
}

// 导出客户数据
export function exportCustomers(params?: CustomerQueryParams) {
  return defHttp.get({
    url: Api.ExportCustomers,
    params,
    responseType: 'blob'
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 解除注销客户
 */
export function unlockCustomer(id: number) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.UnlockCustomer,
    params: { id }
  }, {
    errorMessageMode: 'message'
  });
}
/*
* 一键拉黑status:2,一键拉白status:1
* */
export function updateCustomerStatus(params: { id: number, status: number }) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.UPDATECUSTOMERSTATUS,
    params: params
  }, {
    errorMessageMode: 'message'
  });
}
/*
* 根据贷款额度获取产品列表  /business/risk/riskcontroller/getProductsByAmount
* */
export function getProductsByAmount(params : object) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.GetProductsByAmount,
    params
  }, {
    errorMessageMode: 'message'
  });
}
/*
* 修改用户额度  /business/customer/customercontroller/updateCustomerQuota
* */
export function updateCustomerQuota(params: object) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.UpdateCustomerQuota,
    params: params
  }, {
    errorMessageMode: 'message'
  });
}


/**
 * 更新客户备注
 */
export function deleteCustomer(id: number, type: string) {
  return defHttp.post<BaseResponse<any>>({
    url: Api.DeleteCustomer,
    params: {
      id: id,
      type
    }
  }, {
    errorMessageMode: 'message'
  });
}

/*
* 强制刷新风控
* */
export function forceRefreshRisk(params: { customer_id: number }) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.ForceRefreshRisk,
    params: params
  }, {
    errorMessageMode: 'message'
  });
}
/*
* 黑名单列表
* */
export function getBlacklistList(params: { page: number, page_size: number, idCard: string }) {
  return defHttp.get<BaseResponse<any>>({
    url: Api.BlacklistList,
    params: params
  }, {
    errorMessageMode: 'message'
  });
}
