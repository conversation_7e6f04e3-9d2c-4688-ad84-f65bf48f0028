<!-- 展示风控 -->
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { object } from 'vue-types';
import Tan_zhen_c from '@/components/riskControlReport/tan_zhen_c.vue';
import Radar from '@/components/riskControlReport/radar.vue';
import { Download } from '@element-plus/icons-vue';
import htmlToPdf from '@/utils/htmlToPDF';
import dayjs from 'dayjs';

const props = defineProps<{
  fkTabList: Array<any>,
  name: string,
}>()

// 风控相关
const fkTabProps = {
  label: 'label',
  value: 'value',
  disabled: 'disabled',
};
const fkTabOptions = ref<Array<{label: string, value: any}>>([]);
const fkTabItem = ref({});
const fkTabValue = ref();
const zwscTxt = ref("-");
const tableData = ref<Array<{
  title: string;
  n1: string;
  n3: string;
  n6: string;
  n12: string;
  n24: string;
}>>([]);
onMounted(() => {
  let reportsData = props.fkTabList;
  fkTabOptions.value = [];
  fkTabValue.value = '';
  reportsData.forEach((item: any, index: any) => {
    let txt = item.evaluation_id.indexOf('_async') != -1 ? '内部' : '外部';
    let time: string = item.evaluation_time.split(' ')[0] + ' ' + txt;
    fkTabOptions.value.push({ label: time, value: index });
  });
  fkTabValue.value = 0;
  fkTabItem.value = reportsData[0];

  const behavior_report_detail = fkTabItem.value.raw_data?.leida_v4?.behavior_report_detail || {};
  tableData.value = [{
      title: '贷款笔数',
      n1: behavior_report_detail['*********'] || '0',
      n3: behavior_report_detail['*********'] || '0',
      n6: behavior_report_detail['*********'] || '0',
      n12: behavior_report_detail['*********'] || '0',
      n24: behavior_report_detail['*********'] || '0',
    },
    {
      title: '贷款总金额',
      n1: behavior_report_detail['*********'] || '0',
      n3: behavior_report_detail['*********'] || '0',
      n6: behavior_report_detail['*********'] || '0',
      n12: behavior_report_detail['*********'] || '0',
      n24: behavior_report_detail['*********'] || '0-',
    },
    {
      title: '贷款机构数',
      n1: behavior_report_detail['*********'] || '0',
      n3: behavior_report_detail['*********'] || '0',
      n6: behavior_report_detail['B22170018'] || '0',
      n12: behavior_report_detail['B22170019'] || '0',
      n24: behavior_report_detail['B22170020'] || '0',
    },
    {
      title: '还款成功总金额',
      n1: behavior_report_detail['B22170040'] || '0',
      n3: behavior_report_detail['B22170041'] || '0',
      n6: behavior_report_detail['B22170042'] || '0',
      n12: behavior_report_detail['B22170043'] || '0',
      n24: behavior_report_detail['B22170044'] || '0',
    },
    {
      title: '成功扣款笔数',
      n1: behavior_report_detail['B22170045'] || '0',
      n3: behavior_report_detail['B22170046'] || '0',
      n6: behavior_report_detail['B22170047'] || '0',
      n12: behavior_report_detail['*********'] || '0',
      n24: behavior_report_detail['*********'] || '0',
    },
    {
      title: '失败扣款笔数',
      n1: behavior_report_detail['*********'] || '0',
      n3: behavior_report_detail['*********'] || '0',
      n6: behavior_report_detail['*********'] || '0',
      n12: behavior_report_detail['*********'] || '0',
      n24: behavior_report_detail['*********'] || '0',
    }
  ];

  zwscTransition(fkTabItem.value.raw_data.zwsc)
})
function handleFkSegmentedChange(e: number) {
  // console.log(e)
  fkTabItem.value = props.fkTabList[e];
  const behavior_report_detail = fkTabItem.value.raw_data?.leida_v4?.behavior_report_detail || {};
  tableData.value = [{
    title: '贷款笔数',
    n1: behavior_report_detail['*********'] || '0',
    n3: behavior_report_detail['*********'] || '0',
    n6: behavior_report_detail['*********'] || '0',
    n12: behavior_report_detail['*********'] || '0',
    n24: behavior_report_detail['*********'] || '0',
  },
    {
      title: '贷款总金额',
      n1: behavior_report_detail['*********'] || '0',
      n3: behavior_report_detail['*********'] || '0',
      n6: behavior_report_detail['*********'] || '0',
      n12: behavior_report_detail['*********'] || '0',
      n24: behavior_report_detail['*********'] || '0',
    },
    {
      title: '贷款机构数',
      n1: behavior_report_detail['*********'] || '0',
      n3: behavior_report_detail['*********'] || '0',
      n6: behavior_report_detail['B22170018'] || '0',
      n12: behavior_report_detail['B22170019'] || '0',
      n24: behavior_report_detail['B22170020'] || '0',
    },
    {
      title: '还款成功总金额',
      n1: behavior_report_detail['B22170040'] || '0',
      n3: behavior_report_detail['B22170041'] || '0',
      n6: behavior_report_detail['B22170042'] || '0',
      n12: behavior_report_detail['B22170043'] || '0',
      n24: behavior_report_detail['B22170044'] || '0',
    },
    {
      title: '成功扣款笔数',
      n1: behavior_report_detail['B22170045'] || '0',
      n3: behavior_report_detail['B22170046'] || '0',
      n6: behavior_report_detail['B22170047'] || '0',
      n12: behavior_report_detail['*********'] || '0',
      n24: behavior_report_detail['*********'] || '0',
    },
    {
      title: '失败扣款笔数',
      n1: behavior_report_detail['*********'] || '0',
      n3: behavior_report_detail['*********'] || '0',
      n6: behavior_report_detail['*********'] || '0',
      n12: behavior_report_detail['*********'] || '0',
      n24: behavior_report_detail['*********'] || '0',
    }
  ];
  zwscTransition(fkTabItem.value.raw_data.zwsc)
}
// 在网时长转换
// (0，3)，表示三个月内
// (3，6)，表示三个月以上半年以内
// (6，12)，表示半年以上一年以内
// (12，24)，表示1-2年
// (24，null), 表示两年以上
function zwscTransition (zwscObj:any) {
  // min 和 max 都存在 走 getTimeRangeTextByMin 方法
  if(zwscObj && zwscObj.min && !zwscObj.max) {
    zwscTxt.value = getTimeRangeTextByMin(zwscObj.min)
  }
  // min存在 max不存在 走 getTimeRangeText 方法
  if(zwscObj && zwscObj.min && zwscObj.max) {
    zwscTxt.value = getTimeRangeText(zwscObj.min, zwscObj.max)
  }
}
function getTimeRangeText(min: number, max: number) {
  if (min === 0 && max === 3) {
    return "三个月内";
  } else if (min === 3 && max === 6) {
    return "三个月以上半年以内";
  } else if (min === 6 && max === 12) {
    return "半年以上一年以内";
  } else if (min === 12 && max === 24) {
    return "1-2年";
  } else if (min === 24 && max === null) {
    return "两年以上";
  } else {
    // 如果不在预定义的区间内，可以返回默认值或根据具体逻辑处理
    return "未知区间";
  }
}
function getTimeRangeTextByMin(min: number) {
  if (min >= 0 && min < 3) {
    return "三个月内";
  } else if (min >= 3 && min < 6) {
    return "三个月以上半年以内";
  } else if (min >= 6 && min < 12) {
    return "半年以上一年以内";
  } else if (min >= 12 && min < 24) {
    return "1-2年";
  } else if (min >= 24) {
    return "两年以上";
  } else {
    return "未知区间";
  }
}

function valueToTxt(obj:object, tKey: any, val: any) {
  if(obj && obj[tKey] && obj[tKey][val]) {
    return obj[tKey][val];
  }else {
    return "-";
  }
}
function scoreValueToTxt(obj:object, tKey: any, sKey:any, val: any) {
  if(obj && obj[tKey] && obj[tKey][sKey] && obj[tKey][sKey][val]) {
    return obj[tKey][sKey][val];
  }else {
    return "-";
  }
}

const downloadLoading = ref(false);
const handleDownload = () => {
  downloadLoading.value = true;

  htmlToPdf.getPdf(props.name+'-风控报告-'+ dayjs().format('YYYY-MM-DD/HH:mm:ss'),"#fkbgId");
  setTimeout(() => {
    downloadLoading.value = false;
  },1000)
}
</script>

<template>
  <div class="fk-tab-title">
    <el-row :gutter="24" justify="space-between">
      <el-col :span="20">
        <div class="fk-tab-segmented">
          <el-segmented
            v-model="fkTabValue"
            :options="fkTabOptions"
            :props="fkTabProps"
            @change="handleFkSegmentedChange"
          />
        </div>
      </el-col>
      <el-col :span="4" style="text-align: right">
        <el-button :icon="Download" size="small" :loading="downloadLoading" @click="handleDownload">下载报告</el-button>
      </el-col>
    </el-row>
  </div>

  <div id="fkbgId">
    <el-descriptions border :column="3" label-width="120" v-if="Object.keys(fkTabItem).length > 0">
      <el-descriptions-item label="风控评分">
        {{ fkTabItem.risk_score }}
      </el-descriptions-item>
      <el-descriptions-item label="风控3评分">{{ fkTabItem.dwf_score || '-' }}</el-descriptions-item>
      <el-descriptions-item label="手机在网时长">{{ zwscTxt }}</el-descriptions-item>
      <el-descriptions-item label="风控结果">
        <el-button
          :type="fkTabItem.risk_result == 0? 'primary': fkTabItem.risk_result == 2? 'danger': 'warning'"
          text
        >
          {{fkTabItem.risk_result == 0? '通过': fkTabItem.risk_result == 1? '人工审核': fkTabItem.risk_result == 2? '拒绝': '风控模型失败'}}
        </el-button>
      </el-descriptions-item>
      <template v-if="fkTabItem.risk_result == 2">
        <el-descriptions-item label="风控类型">{{ fkTabItem.failure_type || '-' }}</el-descriptions-item>
        <el-descriptions-item label="风控拒绝原因">{{ fkTabItem.failure_reason || '-' }}</el-descriptions-item>
      </template>
    </el-descriptions>

    <el-divider>风险探针</el-divider>
    <tan_zhen_c :tan_zhen_c="fkTabItem.raw_data?fkTabItem.raw_data?.tan_zhen_c:{}"/>

    <el-divider>申请雷达</el-divider>
    <radar :leida_v4="{
    apply_report_detail: fkTabItem.raw_data?.leida_v4?.apply_report_detail || {},
    behavior_report_detail: fkTabItem.raw_data?.leida_v4?.behavior_report_detail || {},
    current_report_detail: fkTabItem.raw_data?.leida_v4?.current_report_detail || {},
    tableData: tableData
  }"/>

  </div>




</template>

<style scoped lang="less">
.fk-tab-title {
  margin-bottom: 10px;
  .fk-tab-segmented{
    overflow-y: auto;
  }
}
</style>