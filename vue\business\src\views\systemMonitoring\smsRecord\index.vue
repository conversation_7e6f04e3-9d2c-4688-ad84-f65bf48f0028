<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import {
  IconRefresh,
  IconSearch,
} from '@arco-design/web-vue/es/icon';
import dayjs from 'dayjs';
import { getSmsRecordList } from '@/api/systemMonitoring';
const queryFormRef = ref();
const queryForm = reactive({
  title: '',
  mobile: '',
  recipient: '',
  start_time: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
  end_time: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
});
const dateRange = ref<any>([dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')]);
const handleDateChange = (value: any) => {
  if (value && value.length > 0) {
    queryForm.start_time = value[0];
    queryForm.end_time = value[1];
  } else {
    queryForm.start_time = '';
    queryForm.end_time = '';
  }
};

const loading = ref(false);
const fetchData = async () => {
  loading.value = true;
  getSmsRecordList({
    ...queryForm,
    page: pagination.current,
    page_size: pagination.pageSize,
  }).then(res => {
    dataSource.value = res.data;
    pagination.total = res.total;
  }).finally(() => {
    loading.value = false;
  });
};
onMounted(async () => {
  fetchData();
})

const columns = [
  {
    title: 'No',
    dataIndex: 'no',
    width: 50,
    render: ({ rowIndex }: any) => {
      return rowIndex + 1;
    }
  },
  {
    title: '标题',
    dataIndex: 'title',
    width: 120,
  },
  {
    title: '内容',
    dataIndex: 'content'
  },
  {
    title: '手机号',
    dataIndex: 'mobile',
    width: 150,
  },
  {
    title: '接收人',
    dataIndex: 'recipient',
  },
  {
    title: '时间',
    dataIndex: 'created_at',
  }
];
const dataSource = ref([]);
// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

const handleSearch = async () => {
  const vaildate = await queryFormRef.value?.validate();
  if (vaildate) {
    return;
  }
  pagination.current = 1;
  fetchData();
};
const handleReset = async () => {
  queryFormRef.value.resetFields();
  pagination.current = 1;
  queryForm.start_time = '';
  queryForm.end_time = '';
  dateRange.value = [];
  fetchData();
};
</script>

<template>
  <div class="container">
    <!-- 短信发送记录 -->
    <a-card title="短信发送记录" :bordered="false">
      <!-- 短信发送记录表单 -->
      <div class="due-form">
        <a-form
          ref="queryFormRef"
          :model="queryForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
          auto-label-width
          @submit="handleSearch"
        >
          <a-row :gutter="[24, 0]">
            <a-col :md="6" :sm="12">
              <a-form-item field="title" label="标题">
                <a-input allow-clear v-model="queryForm.title" placeholder="账号" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="title" label="手机号">
                <a-input allow-clear v-model="queryForm.mobile" placeholder="手机号" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item field="recipient" label="接收人">
                <a-input allow-clear v-model="queryForm.recipient" placeholder="接收人" />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="12">
              <a-form-item label="时间筛选" field="start_time" required :rules="[{ required: true, message: '请选择时间范围' }]">
                <a-range-picker
                  allow-clear
                  v-model="dateRange"
                  @change="handleDateChange"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  :time-picker-props="{ defaultValue: ['00:00:00', '23:59:59'] }"
                />
              </a-form-item>
            </a-col>


            <a-col :md="6" :sm="12">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="loading">
                  <template #icon>
                    <icon-search />
                  </template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <!-- 短信发送记录列表 -->
    <a-card class="table-card" title="短信发送记录列表" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="fetchData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
      <a-table
        :columns="columns"
        :data="dataSource"
        :pagination="pagination"
        :bordered="{headerCell:true}"
        size="medium"
        :scroll="{ y: 500 }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :loading="loading"
      >
      </a-table>
    </a-card>
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 10px;
}

.stat-grid {
  .arco-col {
    height: 80px;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    background-color: var(--color-bg-1);
    padding: 16px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .stat-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-2);
    }

    .stat-number {
      font-size: 16px;
      color: rgb(var(--primary-6));
      margin-top: 10px;
      font-weight: bold;
    }
  }
}

.due-form {
  margin-top: 20px;
}

.table-card {
  margin-top: 10px;
}
</style>