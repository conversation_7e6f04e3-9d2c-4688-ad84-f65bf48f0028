<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import {
  IconRefresh,
  IconSearch,
} from '@arco-design/web-vue/es/icon';
import dayjs from 'dayjs';
import { getBlacklistList, updateCustomerStatus } from '@/api/usermanage';
import { ElMessage, ElMessageBox } from 'element-plus';
const queryFormRef = ref();
const queryForm = reactive({
  idCard: '',
});


const loading = ref(false);
const fetchData = async () => {
  loading.value = true;
  getBlacklistList({
    idCard: queryForm.idCard,
    page: pagination.current,
    page_size: pagination.pageSize,
  }).then(res => {
    dataSource.value = res.items;
    pagination.total = res.total;
  }).finally(() => {
    loading.value = false;
  });
};
onMounted(async () => {
  fetchData();
})

const columns = [
  {
    title: 'No',
    dataIndex: 'no',
    width: 50,
    render: ({ rowIndex }: any) => {
      return rowIndex + 1;
    }
  },
  {
    title: '用户姓名',
    dataIndex: 'name',
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
  },
  {
    title: '手机号码',
    dataIndex: 'mobile',
  },
  {
    title: '拉黑原因',
    dataIndex: 'blockReason',
  },
  {
    title: '操作',
    slotName: 'action',
    width: 300,
    fixed: 'right',
  }
];
const dataSource = ref([]);
// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
});

// 分页变化
const handlePageChange = (page: number) => {
  pagination.current = page;
  fetchData();
};

const handlePageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.current = 1;
  fetchData();
};

const handleSearch = async () => {
  const vaildate = await queryFormRef.value?.validate();
  if (vaildate) {
    return;
  }
  pagination.current = 1;
  fetchData();
};
const handleReset = async () => {
  queryFormRef.value.resetFields();
  pagination.current = 1;
  fetchData();
};

const handleRemoveBlacklist = (record: any) => {
  ElMessageBox.confirm('确定移出黑名单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    updateCustomerStatus({
      id: record.id,
      status: 0,
    }).then(res => {
      ElMessage.success('移出成功');
      fetchData();
    }).catch(() => {
      ElMessage.error('移出失败');
    })
  })
}
</script>

<template>
  <div class="container">
    <!-- 黑名单 -->
    <a-card title="黑名单" :bordered="false">
      <!-- 黑名单表单 -->
      <div class="due-form">
        <a-form
          ref="queryFormRef"
          :model="queryForm"
          :label-col-props="{ span: 6 }"
          :wrapper-col-props="{ span: 18 }"
          label-align="left"
          auto-label-width
          @submit="handleSearch"
        >
          <a-row :gutter="[24, 0]">
            <a-col :md="6" :sm="12">
              <a-form-item field="idCard" label="身份证">
                <a-input allow-clear v-model="queryForm.idCard" placeholder="身份证" />
              </a-form-item>
            </a-col>

            <a-col :md="6" :sm="12">
              <a-space>
                <a-button type="primary" html-type="submit" :loading="loading">
                  <template #icon>
                    <icon-search />
                  </template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon>
                    <icon-refresh />
                  </template>
                  重置
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <!-- 黑名单列表 -->
    <a-card class="table-card" title="黑名单列表" :bordered="false">
      <template #extra>
        <a-space>
          <a-button type="primary" @click="fetchData">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
        </a-space>
      </template>
      <a-table
        :columns="columns"
        :data="dataSource"
        :pagination="pagination"
        :bordered="{headerCell:true}"
        size="medium"
        :scroll="{ y: 500 }"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
        :loading="loading"
      >
        <template #action="{record}">
          <a-button type="text" @click="handleRemoveBlacklist(record)">移出黑名单</a-button>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 10px;
}

.stat-grid {
  .arco-col {
    height: 80px;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    background-color: var(--color-bg-1);
    padding: 16px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    .stat-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-text-2);
    }

    .stat-number {
      font-size: 16px;
      color: rgb(var(--primary-6));
      margin-top: 10px;
      font-weight: bold;
    }
  }
}

.due-form {
  margin-top: 20px;
}

.table-card {
  margin-top: 10px;
}
</style>