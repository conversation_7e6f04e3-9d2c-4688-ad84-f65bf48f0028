export default {
  // Menu related
  'menu.usermanage': 'Customer Management',
  'menu.usermanage.list': 'Customer List',
  'menu.usermanage.detail': 'Customer Details',
  'menu.usermanage.repurchase': 'Repurchase Customers',
  
  // Page title
  'usermanage.title': 'Customer List',
  
  // Search form
  'usermanage.search.name': 'Name',
  'usermanage.search.mobile': 'Mobile',
  'usermanage.search.idCard': 'ID Card',
  'usermanage.search.reviewer': 'Reviewer',
  'usermanage.search.channel': 'Channel Source',
  'usermanage.search.riskScore': 'Risk Score',
  'usermanage.search.identityStatus': 'Identity Status',
  'usermanage.search.orderStatus': 'Order Status',
  'usermanage.search.registerIncomplete': 'Register Incomplete',
  'usermanage.search.realNameIncomplete': 'Real Name Incomplete',
  'usermanage.search.hasQuotaNoOrder': 'Has Quota No Order',
  'usermanage.search.userRemark': 'User Remark',
  'usermanage.search.loanCount': 'Loan Count',
  'usermanage.search.isNewUser': 'Is New User',
  'usermanage.search.isComplaint': 'Is Complaint',
  'usermanage.search.joinTime': 'Join Time',
  'usermanage.search.registerTime': 'Register Time',
  
  // Search form placeholders
  'usermanage.search.placeholder.name': 'Please enter name',
  'usermanage.search.placeholder.mobile': 'Please enter mobile number',
  'usermanage.search.placeholder.idCard': 'Please enter ID card number',
  'usermanage.search.placeholder.reviewer': 'Please select reviewer',
  'usermanage.search.placeholder.channel': 'Please select channel source',
  'usermanage.search.placeholder.riskScore.min': 'Min value',
  'usermanage.search.placeholder.riskScore.max': 'Max value',
  'usermanage.search.placeholder.identityStatus': 'Please select identity status',
  'usermanage.search.placeholder.orderStatus': 'Please select order status',
  'usermanage.search.placeholder.select': 'Please select',
  'usermanage.search.placeholder.userRemark': 'Please enter user remark',
  'usermanage.search.placeholder.loanCount.min': 'Min value',
  'usermanage.search.placeholder.loanCount.max': 'Max value',
  'usermanage.search.placeholder.dateRange.start': 'Start time',
  'usermanage.search.placeholder.dateRange.end': 'End time',
  
  // Status options
  'usermanage.status.identity.unverified': 'Unverified',
  'usermanage.status.identity.verified': 'Verified',
  'usermanage.status.order.none': 'No Order',
  'usermanage.status.order.inProgress': 'In Progress',
  'usermanage.status.order.completed': 'Completed',
  'usermanage.status.order.cancelled': 'Cancelled',
  'usermanage.status.yes': 'Yes',
  'usermanage.status.no': 'No',
  'usermanage.status.risk.normal': 'Normal',
  'usermanage.status.risk.warning': 'Risk Control',
  
  // Table column titles
  'usermanage.columns.index': 'No',
  'usermanage.columns.channel': 'Channel Name',
  'usermanage.columns.reviewer': 'Reviewer',
  'usermanage.columns.deviceSource': 'Device Source',
  'usermanage.columns.name': 'Name',
  'usermanage.columns.mobile': 'Mobile',
  'usermanage.columns.userRemark': 'User Remark',
  'usermanage.columns.joinTime': 'Join Time',
  'usermanage.columns.registerTime': 'Register Time',
  'usermanage.columns.loanCount': 'Loan Count',
  'usermanage.columns.identityStatus': 'Identity Status',
  'usermanage.columns.orderStatus': 'Order Status',
  'usermanage.columns.isComplaint': 'Complaint Status',
  'usermanage.columns.complaintRemark': 'Complaint Remark',
  'usermanage.columns.historyQuota': 'History Quota',
  'usermanage.columns.totalQuota': 'Total Quota',
  'usermanage.columns.remainingQuota': 'Remaining Quota',
  'usermanage.columns.riskScore': 'Risk Score',
  'usermanage.columns.idCard': 'ID Card',
  'usermanage.columns.isRisk': 'Risk Info',
  'usermanage.columns.operations': 'Operations',
  
  // Operation buttons
  'usermanage.operation.search': 'Search',
  'usermanage.operation.reset': 'Reset',
  'usermanage.operation.cancelUser': 'Cancel User Deactivation',
  'usermanage.operation.view': 'View',
  'usermanage.operation.audit': 'Audit',
  'usermanage.operation.remark': 'Remark',
  
  // Pagination info
  'usermanage.pagination.total': '{start}-{end} of {total} items',
  
  // Message prompts
  'usermanage.message.searchSuccess': 'Search successful',
  'usermanage.message.resetSuccess': 'Reset successful',
  'usermanage.message.loadError': 'Failed to load data',
  'usermanage.message.operationSuccess': 'Operation successful',
  'usermanage.message.operationFailed': 'Operation failed',
  
  // Repurchase customers related
  'repurchase.title': 'Repurchase Customer List',
  'repurchase.search.isCancelled': 'Is Cancelled',
  'repurchase.search.borrowingOrderCount': 'Borrowing Orders',
  'repurchase.search.totalOrderCount': 'Total Orders',
  'repurchase.search.totalAmount': 'Total Amount Range',
  'repurchase.search.availableAmount': 'Available Amount Range',
  'repurchase.search.lastRepayTime': 'Last Repay Time',
  'repurchase.search.billDueTime': 'Bill Due Time',
  'repurchase.search.sortType': 'Sort Type',
  'repurchase.status.cancelled': 'Cancelled',
  'repurchase.status.unCancelled': 'Active',
  'repurchase.columns.totalAmount': 'Total Amount',
  'repurchase.columns.availableAmount': 'Available Amount',
  'repurchase.columns.borrowingOrderCount': 'Borrowing Orders',
  'repurchase.columns.totalOrderCount': 'Total Orders',
  'repurchase.columns.lastRepayTime': 'Last Repay Time',
  'repurchase.columns.billDueTime': 'Bill Due Time',
  'repurchase.columns.lastAwakenTime': 'Last Awaken Time',
  'repurchase.columns.awakenCount': 'Awaken Count',
  'repurchase.columns.awakenRemark': 'Awaken Remark',
  'repurchase.operation.sendSMS': 'Send SMS',
  'repurchase.operation.recordAwaken': 'Record Awaken',
  'repurchase.operation.viewAwakenRecords': 'View Records',
  'repurchase.operation.batchSMS': 'Batch SMS',
  'repurchase.sms.title': 'Send SMS',
  'repurchase.sms.templateType': 'SMS Template',
  'repurchase.sms.remark': 'Remark',
  'repurchase.awaken.title': 'Record Awaken',
  'repurchase.awaken.type': 'Awaken Type',
  'repurchase.awaken.content': 'Communication Content',
  'repurchase.awakenRecords.title': 'Awaken Records',

  // 黑名单
  'menu.blacklist.title': 'Blacklist List',
};