<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import Radar from '@/views/CreditQuickCheck/components/radar.vue';
import { getRecordDetail, searchUserQuery } from '@/api/CreditQuickCheck';
import { useRouter, useRoute } from 'vue-router';
import Tan_zhen_c from '@/views/CreditQuickCheck/components/tan_zhen_c.vue';
import { ElMessageBox, ElLoading } from 'element-plus';
import dayjs from 'dayjs';
const $router = useRouter();
const $route = useRoute();
const userInfo = reactive<any>({
  uid: '',
  descList: [
  ],
  product_id: '',
  detail: {}
})
const tabsActiveName = ref();
const check_time = ref();
const price = ref(1.6);
const leida_v4 = reactive<any>({
  apply_report_detail: {},
  behavior_report_detail: {},
  current_report_detail: {},
  tableData: []
})
const tan_zhen_c = ref({})
onMounted(() => {
  getDetail();
})
const getDetail = async () => {
  const res = await getRecordDetail({
    id: $route.params.id
  })
  userInfo.uid = res.name;
  userInfo.descList = [
    {
      label: '姓名',
      value: res.name
    },
    {
      label: '手机号',
      value: res.mobile
    },
    {
      label: '身份证号',
      value: res.id_no
    },
    {
      label: '查询时间',
      value: dayjs(res.created_at).format('YYYY-MM-DD HH:mm:ss')
    },
  ]
  check_time.value = dayjs(res.created_at).format('YYYY-MM-DD HH:mm:ss');
  userInfo.detail = res;

  // price.value = res.price;
  // userInfo.product_id = res.product_id;
  // tabsActiveName.value = res.product_id;
  // if(res.product_id == 'leida_v4') {
  //   leida_v4.apply_report_detail = JSON.parse(res.data).apply_report_detail;
  //   leida_v4.behavior_report_detail = JSON.parse(res.data).behavior_report_detail;
  //   leida_v4.current_report_detail = JSON.parse(res.data).current_report_detail;
  // }else if(res.product_id == 'tan_zhen_c'){
  //   tan_zhen_c.value = JSON.parse(res.data)
  // }
}

const handleBeforeLeave = (tabName: string) => {
  if (tabName === 'leida_v4' && Object.keys(leida_v4.apply_report_detail).length > 0) {
    // 处理全景雷达tab的逻辑
    return true
  } else if (tabName === 'tan_zhen_c' && Object.keys(tan_zhen_c.value).length > 0) {
    // 处理探针.Ctab的逻辑
    return true
  }
  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(
      '改操作需要消耗1.6元，是否继续查看?',
      '提示',
      {
        type: 'warning',
      },
    ).then(() => {
      const loading = ElLoading.service({
        lock: true,
        text: '查询中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      searchUserQuery({
        name: userInfo.detail.name,
        mobile: userInfo.detail.mobile,
        idNo: userInfo.detail.id_no,
        product_id: tabName
      }).then(res => {

        userInfo.product_id = res.product_id;
        if(res.product_id == 'leida_v4') {
          let apply_report_detail = JSON.parse(res.data).apply_report_detail;
          let behavior_report_detail = JSON.parse(res.data).behavior_report_detail;
          let current_report_detail = JSON.parse(res.data).current_report_detail;

          leida_v4.apply_report_detail = apply_report_detail;
          leida_v4.behavior_report_detail = behavior_report_detail;
          leida_v4.current_report_detail = current_report_detail;
          if (leida_v4.behavior_report_detail) {
            leida_v4.tableData = [
              {
                title: '贷款笔数',
                n1: behavior_report_detail['B22170002'] || 0,
                n3: behavior_report_detail['B22170003'] || 0,
                n6: behavior_report_detail['B22170004'] || 0,
                n12: behavior_report_detail['B22170005'] || 0,
                n24: behavior_report_detail['B22170006'] || 0,
              },
              {
                title: '贷款总金额',
                n1: behavior_report_detail['B22170007'] || 0,
                n3: behavior_report_detail['B22170008'] || 0,
                n6: behavior_report_detail['B22170009'] || 0,
                n12: behavior_report_detail['B22170010'] || 0,
                n24: behavior_report_detail['B22170011'] || 0,
              },
              {
                title: '贷款机构数',
                n1: behavior_report_detail['B22170016'] || 0,
                n3: behavior_report_detail['B22170017'] || 0,
                n6: behavior_report_detail['B22170018'] || 0,
                n12: behavior_report_detail['B22170019'] || 0,
                n24: behavior_report_detail['B22170020'] || 0,
              },
              {
                title: '还款成功总金额',
                n1: behavior_report_detail['B22170040'] || 0,
                n3: behavior_report_detail['B22170041'] || 0,
                n6: behavior_report_detail['B22170042'] || 0,
                n12: behavior_report_detail['B22170043'] || 0,
                n24: behavior_report_detail['B22170044'] || 0,
              },
              {
                title: '成功扣款笔数',
                n1: behavior_report_detail['B22170045'] || 0,
                n3: behavior_report_detail['B22170046'] || 0,
                n6: behavior_report_detail['B22170047'] || 0,
                n12: behavior_report_detail['B22170048'] || 0,
                n24: behavior_report_detail['B22170049'] || 0,
              },
              {
                title: '失败扣款笔数',
                n1: behavior_report_detail['B22170035'] || 0,
                n3: behavior_report_detail['B22170036'] || 0,
                n6: behavior_report_detail['B22170037'] || 0,
                n12: behavior_report_detail['B22170038'] || 0,
                n24: behavior_report_detail['B22170039'] || 0,
              }
            ];
          }


        }else if(res.product_id == 'tan_zhen_c'){
          tan_zhen_c.value = JSON.parse(res.data)
        }

        resolve(true);
      }).finally(() => {
        loading.close();
      })
    }).catch(() => {
      reject(false);
    });
  })
}

// isDisplay 计算属性判断是否显示空状态
const isDisplay = computed(() => {
  if (tabsActiveName.value == 'leida_v4' && Object.keys(leida_v4.apply_report_detail).length > 0) {
    return false;
  }else if (tabsActiveName.value == 'tan_zhen_c' && Object.keys(tan_zhen_c.value).length > 0) {
    return false;
  };
  return true
})
</script>

<template>
  <div class="container">
    <div class="d-head">
      <el-descriptions :column="3" :title="userInfo.uid">
        <el-descriptions-item :label="item.label" v-for="item in userInfo.descList">{{ item.value }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="d-tabs">
      <el-tabs v-model="tabsActiveName" size="medium" :before-leave="handleBeforeLeave">
        <el-tab-pane label="全景雷达" name="leida_v4">
          <template #label>
            <div class="tabs-pane-label">
              全景雷达(<span>{{ price }}元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">
            <radar :name="userInfo.uid" :leida_v4="leida_v4" :check_time="check_time" :price="price" />
          </div>
        </el-tab-pane>

        <el-tab-pane label="探针.C" name="tan_zhen_c">
          <template #label>
            <div class="tabs-pane-label">
              探针.C(<span>{{ price }}元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">
            <tan_zhen_c :name="userInfo.uid" :tan_zhen_c="tan_zhen_c" :check_time="check_time" :price="price" />
          </div>
        </el-tab-pane>

<!--        <el-tab-pane label="探针.A" name="t3">
          <template #label>
            <div class="tabs-pane-label">
              探针.A(<span>1.6元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">探针.A内容</div>
        </el-tab-pane>

        <el-tab-pane label="逾期风险报告" name="t4">
          <template #label>
            <div class="tabs-pane-label">
              逾期风险报告(<span>3元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">逾期风险报告内容</div>
        </el-tab-pane>

        <el-tab-pane label="逾期档案" name="t5">
          <template #label>
            <div class="tabs-pane-label">
              逾期档案(<span>3元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">逾期档案内容</div>
        </el-tab-pane>

        <el-tab-pane label="探查" name="t6">
          <template #label>
            <div class="tabs-pane-label">
              探查(<span>3元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">探查内容</div>
        </el-tab-pane>

        <el-tab-pane label="全景档案" name="t7">
          <template #label>
            <div class="tabs-pane-label">
              全景档案(<span>3元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">全景档案</div>
        </el-tab-pane>

        <el-tab-pane label="流水报告" name="t8">
          <template #label>
            <div class="tabs-pane-label">
              流水报告(<span>1.5元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">流水报告</div>
        </el-tab-pane>

        <el-tab-pane label="信用报告" name="t9">
          <template #label>
            <div class="tabs-pane-label">
              信用报告（必查）x(<span>5元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">信用报告</div>
        </el-tab-pane>

        <el-tab-pane label="JT综合" name="t10">
          <template #label>
            <div class="tabs-pane-label">
              JT综合(<span>5元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">JT综合</div>
        </el-tab-pane>

        <el-tab-pane label="租凭报告" name="t11">
          <template #label>
            <div class="tabs-pane-label">
              租凭报告(<span>8元</span>)
            </div>
          </template>
          <div class="tabs-pane-content">租凭报告</div>
        </el-tab-pane>-->
      </el-tabs>

      <el-empty v-if="isDisplay" description="请根据需求查看对应报告" />
    </div>
  </div>
</template>

<style scoped lang="less">
.container{
  padding: 15px;
  .d-head{
    border-radius: 8px;
    padding: 15px;
    background: #fff;
  }
  .d-tabs{
    margin-top: 15px;
    padding: 0 15px;
    border-radius: 8px;
    background: #fff;
    .tabs-pane-label{
      span{
        color: #f00;
      }
    }
    .tabs-pane-content{
      padding-bottom: 15px;
    }
  }
}
</style>