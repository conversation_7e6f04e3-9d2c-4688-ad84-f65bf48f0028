<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue';
import { getDepartmentBalance, recharge, getTotalBalance } from '@/api/financialStatistics';
import { Message } from '@arco-design/web-vue';

const loading =  ref(false);
const columns = [
  {
    title: 'No',
    dataIndex: 'no',
    width: 50,
    render: ({ rowIndex }: any) => {
      return rowIndex + 1;
    }
  },
  {
    title: '部门id',
    dataIndex: 'department_id',
    width: 100,
  },
  {
    title: '部门',
    dataIndex: 'name',
  },
  {
    title: '部门余额',
    dataIndex: 'balance'
  },
  {
    title: '操作',
    slotName: 'optional',
    width: 100,
  }
];

const handleSearch = () => {
  funGetTotalBalance();
  fetchData();
};
const dataSource = ref<any>([]);
const fetchData = async () => {
  loading.value = true;
  getDepartmentBalance().then(res => {
    dataSource.value = res;
  }).finally(() => {
    loading.value = false;
  })
}
onMounted(() => {
  fetchData();
  funGetTotalBalance();
})
const totalBalance = ref(0);
function funGetTotalBalance() {
  getTotalBalance().then(res => {
    totalBalance.value = res.data.balance;
  })
}

// 充值弹窗
const visible = ref(false);
const modalTitle = ref('部门充值');
const rechargeAmount = ref('');
const department_id = ref('');
const handleTopUp = (record: any) => {
  visible.value = true;
  modalTitle.value = `${record.name}充值`;
  department_id.value = record.department_id;
}
const handleRechargeOk = (done) => {
  if (!rechargeAmount.value) {
    Message.error('请输入充值金额');
    return false;
  }
  // 正则校验输入的是金额
  if (!/^\d+(\.\d{1,2})?$/.test(rechargeAmount.value)) {
    Message.error('请输入正确的金额');
    return false;
  }
  recharge({
    department_id: department_id.value,
    amount: rechargeAmount.value
  }).then(res => {
    Message.success('充值成功');
    handleRechargeCancel();
    done();
    fetchData();
    funGetTotalBalance();
  })
}
const handleRechargeCancel = () => {
  visible.value = false;
  rechargeAmount.value = '';
  department_id.value = '';
}
</script>

<template>
  <div class="container">
    <div class="depTopUp-head-top">
      <div>总余额：{{ totalBalance }}</div>
      <a-button type="primary" size="mini" @click="handleSearch">刷新</a-button>
    </div>
    <div class="f-table">
      <a-table
        :columns="columns"
        :data="dataSource"
        :bordered="{headerCell:true}"
        size="medium"
        :scroll="{ y: '100%' }"
        :loading="loading"
      >
        <template #optional="{ record }">
          <a-space>
            <a-button type="primary" @click="handleTopUp(record)" size="mini">充值</a-button>
          </a-space>

        </template>
      </a-table>
    </div>

    <!-- 充值弹窗 -->
    <a-modal v-model:visible="visible" @before-ok="handleRechargeOk" @cancel="handleRechargeCancel">
      <template #title>
        {{ modalTitle }}
      </template>
      <div>
        <a-input-number v-model="rechargeAmount" placeholder="请输入充值金额"/>
      </div>
    </a-modal>

  </div>
</template>

<style scoped lang="less">
.container{
  margin: 10px;
  background: #fff;
  border-radius: 8px;
  .depTopUp-head-top{
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .f-form{
    padding: 15px;
  }
  .f-table{
    padding: 0 15px 15px;
  }
}
</style>